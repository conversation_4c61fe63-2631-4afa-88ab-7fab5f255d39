/* 刷牙登記頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.date-display {
    background: white;
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: var(--box-shadow);
    font-weight: 500;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-display i {
    color: var(--primary-color);
}

/* 刷牙摘要卡片 */
.brushing-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.summary-card i {
    font-size: 2.5rem;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.summary-card i.fa-users {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
}

.summary-card i.fa-tooth.success {
    background: linear-gradient(135deg, #00cec9, #81ecec);
}

.summary-card i.fa-exclamation-triangle.warning {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
}

.summary-info {
    display: flex;
    flex-direction: column;
}

.summary-count {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
    color: var(--dark-color);
}

.summary-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 操作按鈕區域 */
.action-buttons {
    display: flex;
    justify-content: space-between;
    margin: 25px 0;
    flex-wrap: wrap;
    gap: 15px;
}

.button-group {
    display: flex;
    gap: 10px;
}

.search-box {
    position: relative;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 50px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2);
}

/* 表格樣式 */
.table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 25px;
}

#brushingTable {
    width: 100%;
    border-collapse: collapse;
}

#brushingTable th,
#brushingTable td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

#brushingTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
    white-space: nowrap;
}

#brushingTable tbody tr:hover {
    background-color: #f8f9fa;
}

/* 刷牙狀態標籤 */
.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
}

.status-brushed {
    background-color: #e3f8f8;
    color: #00cec9;
}

.status-not-brushed {
    background-color: #fff0f0;
    color: #ff7675;
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
}

.pagination button {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
    background-color: #f0f0f0;
    border-color: #ccc;
}

.pagination button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 確認彈窗 */
.reward-info {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #f0fdff;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    color: #00cec9;
    font-weight: 500;
}

.reward-info i {
    font-size: 1.5rem;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
}

/* 響應式設計 */
@media (max-width: 992px) {
    .brushing-summary {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-display {
        width: 100%;
        justify-content: center;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .button-group {
        width: 100%;
    }
    
    .button-group .btn {
        flex: 1;
        text-align: center;
    }
    
    .search-box {
        width: 100%;
    }
    
    .brushing-summary {
        grid-template-columns: 1fr;
    }
    
    #brushingTable th,
    #brushingTable td {
        padding: 10px;
        font-size: 0.9rem;
    }
}

/* 動畫效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 1.5s infinite;
}

/* 牙齒相關的特殊動畫 */
@keyframes sparkle {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1);
    }
}

.status-brushed {
    position: relative;
}

.status-brushed::before {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #00cec9;
    border-radius: 50%;
    animation: sparkle 2s infinite;
}

/* 時間欄位樣式 */
.time-column {
    font-family: 'Monaco', 'Consolas', monospace;
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
}

.time-column:not(:empty) {
    color: var(--primary-color);
    background-color: #f8f9fe;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}