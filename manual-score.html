<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 手動加減分</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/manual-score.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li><a href="homework-submission.html"><i class="fas fa-book"></i> 📚 作業繳交</a></li>
                    <li><a href="homework-history.html"><i class="fas fa-clipboard-list"></i> 📋 繳交歷史</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li class="active"><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>
            <div class="data-management">
                <h3>📊 資料管理</h3>
                <button id="exportBtn" class="btn"><i class="fas fa-file-export"></i> 匯出資料</button>
                <button id="importBtn" class="btn"><i class="fas fa-file-import"></i> 匯入資料</button>
                <input type="file" id="importFile" accept=".json,.csv" style="display: none;">
            </div>
        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <div class="header-container">
                <h2><i class="fas fa-edit"></i> 手動加減分</h2>
                <div class="quick-actions">
                    <button id="batchModeBtn" class="btn btn-secondary">
                        <i class="fas fa-list"></i> 批量操作
                    </button>
                    <button id="historyBtn" class="btn btn-secondary">
                        <i class="fas fa-history"></i> 操作記錄
                    </button>
                </div>
            </div>
            
            <!-- 快速操作區 -->
            <div class="quick-score-section">
                <h3>快速加減分</h3>
                <div class="quick-score-buttons">
                    <button class="quick-btn positive" data-score="10">
                        <i class="fas fa-plus"></i> +10分
                    </button>
                    <button class="quick-btn positive" data-score="5">
                        <i class="fas fa-plus"></i> +5分
                    </button>
                    <button class="quick-btn positive" data-score="1">
                        <i class="fas fa-plus"></i> +1分
                    </button>
                    <button class="quick-btn negative" data-score="-1">
                        <i class="fas fa-minus"></i> -1分
                    </button>
                    <button class="quick-btn negative" data-score="-5">
                        <i class="fas fa-minus"></i> -5分
                    </button>
                    <button class="quick-btn negative" data-score="-10">
                        <i class="fas fa-minus"></i> -10分
                    </button>
                </div>
            </div>
            
            <!-- 搜尋和過濾區 -->
            <div class="filter-section">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="搜尋學生姓名或學號...">
                </div>
                <select id="classFilter" class="form-control">
                    <option value="">所有班級</option>
                </select>
                <select id="sortBy" class="form-control">
                    <option value="number">按學號排序</option>
                    <option value="name">按姓名排序</option>
                    <option value="score">按分數排序</option>
                </select>
            </div>
            
            <!-- 學生列表 -->
            <div class="students-grid" id="studentsGrid">
                <!-- 學生卡片將由 JavaScript 動態生成 -->
            </div>
            
            <!-- 分頁 -->
            <div class="pagination" id="pagination">
                <!-- 分頁按鈕將由 JavaScript 動態生成 -->
            </div>
        </main>
    </div>

    <!-- 自訂分數彈窗 -->
    <div id="customScoreModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>自訂分數調整</h3>
            <div class="student-info" id="modalStudentInfo">
                <!-- 學生資訊將由 JavaScript 動態生成 -->
            </div>
            <form id="customScoreForm">
                <div class="form-group">
                    <label for="scoreChange">分數調整：</label>
                    <div class="score-input-group">
                        <button type="button" class="score-btn minus" id="decreaseBtn">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" id="scoreChange" value="0" min="-100" max="100" step="1">
                        <button type="button" class="score-btn plus" id="increaseBtn">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="scoreReason">調整原因：</label>
                    <select id="scoreReason" class="form-control">
                        <option value="">請選擇原因</option>
                        <option value="homework">作業表現</option>
                        <option value="behavior">行為表現</option>
                        <option value="participation">課堂參與</option>
                        <option value="test">測驗成績</option>
                        <option value="activity">活動參與</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="scoreNote">備註說明：</label>
                    <textarea id="scoreNote" rows="3" placeholder="請輸入詳細說明..."></textarea>
                </div>
                <div class="score-preview">
                    <div class="current-score">
                        目前分數：<span id="currentScore">0</span> 分
                    </div>
                    <div class="arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    <div class="new-score">
                        調整後：<span id="newScore">0</span> 分
                    </div>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn btn-primary">確認調整</button>
                    <button type="button" class="btn btn-secondary" id="cancelCustomBtn">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 批量操作彈窗 -->
    <div id="batchModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>批量操作</h3>
            <div class="batch-selection">
                <h4>選擇學生</h4>
                <div class="batch-controls">
                    <button id="selectAllBtn" class="btn btn-sm">全選</button>
                    <button id="selectNoneBtn" class="btn btn-sm">全不選</button>
                    <button id="selectByClassBtn" class="btn btn-sm">按班級選擇</button>
                </div>
                <div class="batch-student-list" id="batchStudentList">
                    <!-- 學生選擇列表將由 JavaScript 動態生成 -->
                </div>
            </div>
            <div class="batch-action">
                <h4>批量調整</h4>
                <div class="form-group">
                    <label for="batchScoreChange">分數調整：</label>
                    <input type="number" id="batchScoreChange" value="0" min="-100" max="100" step="1">
                </div>
                <div class="form-group">
                    <label for="batchReason">調整原因：</label>
                    <select id="batchReason" class="form-control">
                        <option value="">請選擇原因</option>
                        <option value="homework">作業表現</option>
                        <option value="behavior">行為表現</option>
                        <option value="participation">課堂參與</option>
                        <option value="test">測驗成績</option>
                        <option value="activity">活動參與</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="batchNote">備註說明：</label>
                    <textarea id="batchNote" rows="2" placeholder="請輸入詳細說明..."></textarea>
                </div>
            </div>
            <div class="form-buttons">
                <button id="confirmBatchBtn" class="btn btn-primary">確認批量調整</button>
                <button id="cancelBatchBtn" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 操作記錄彈窗 -->
    <div id="historyModal" class="modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h3>操作記錄</h3>
            <div class="history-filters">
                <input type="date" id="historyDateFilter" class="form-control">
                <select id="historyStudentFilter" class="form-control">
                    <option value="">所有學生</option>
                </select>
                <button id="clearHistoryBtn" class="btn btn-danger btn-sm">
                    <i class="fas fa-trash"></i> 清除記錄
                </button>
            </div>
            <div class="history-list" id="historyList">
                <!-- 操作記錄將由 JavaScript 動態生成 -->
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script src="js/mystery-box-config.js"></script>
    <script src="js/manual-score.js"></script>
</body>
</html>
