<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 作業繳交歷史</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/homework-history.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li><a href="homework-submission.html"><i class="fas fa-book"></i> 📚 作業繳交</a></li>
                    <li class="active"><a href="homework-history.html"><i class="fas fa-clipboard-list"></i> 📋 繳交歷史</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>
            <div class="data-management">
                <h3>📊 資料管理</h3>
                <button id="exportBtn" class="btn btn-secondary"><i class="fas fa-file-export"></i> 匯出資料</button>
                <button id="importBtn" class="btn btn-secondary"><i class="fas fa-file-import"></i> 匯入資料</button>
                <input type="file" id="importFile" accept=".json" style="display: none;">
            </div>
        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <div class="header-container">
                <h2><i class="fas fa-clipboard-list"></i> 作業繳交歷史記錄</h2>
                <div class="header-actions">
                    <button id="backToHomeworkBtn" class="btn btn-info">
                        <i class="fas fa-arrow-left"></i> 返回作業繳交
                    </button>
                </div>
            </div>

            <!-- 統計摘要 -->
            <div class="summary-container">
                <div class="summary-card">
                    <i class="fas fa-list-alt"></i>
                    <div class="summary-info">
                        <span id="totalRecords" class="summary-count">0</span>
                        <span class="summary-label">總記錄數</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-calendar-day"></i>
                    <div class="summary-info">
                        <span id="todayRecords" class="summary-count">0</span>
                        <span class="summary-label">今日記錄</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-calendar-week"></i>
                    <div class="summary-info">
                        <span id="weekRecords" class="summary-count">0</span>
                        <span class="summary-label">本週記錄</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-star"></i>
                    <div class="summary-info">
                        <span id="totalPoints" class="summary-count">0</span>
                        <span class="summary-label">總獲得分數</span>
                    </div>
                </div>
            </div>

            <!-- 篩選控制 -->
            <div class="filter-container">
                <h3><i class="fas fa-filter"></i> 篩選條件</h3>
                <div class="filter-controls">
                    <div class="filter-group">
                        <label for="dateStart">開始日期：</label>
                        <input type="date" id="dateStart">
                    </div>
                    <div class="filter-group">
                        <label for="dateEnd">結束日期：</label>
                        <input type="date" id="dateEnd">
                    </div>
                    <div class="filter-group">
                        <label for="studentSelect">學生：</label>
                        <select id="studentSelect">
                            <option value="">全部學生</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="homeworkItemSelect">作業項目：</label>
                        <select id="homeworkItemSelect">
                            <option value="">全部項目</option>
                        </select>
                    </div>
                    <div class="filter-buttons">
                        <button id="applyFilterBtn" class="btn btn-primary">
                            <i class="fas fa-search"></i> 篩選
                        </button>
                        <button id="clearFilterBtn" class="btn btn-secondary">
                            <i class="fas fa-eraser"></i> 清除
                        </button>
                        <button id="downloadBtn" class="btn btn-success">
                            <i class="fas fa-download"></i> 下載記錄
                        </button>
                    </div>
                </div>
            </div>

            <!-- 記錄表格 -->
            <div class="table-container">
                <table id="historyTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>時間</th>
                            <th>頭像</th>
                            <th>學號</th>
                            <th>姓名</th>
                            <th>作業項目</th>
                            <th>獲得分數</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="historyTableBody">
                        <!-- 歷史記錄將由 JavaScript 動態生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 分頁控制 -->
            <div class="pagination" id="pagination">
                <!-- 分頁按鈕將由 JavaScript 動態生成 -->
            </div>
        </main>
    </div>

    <!-- 編輯記錄彈窗 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>編輯作業繳交記錄</h3>
            <form id="editForm">
                <div class="form-group">
                    <label for="editStudent">學生：</label>
                    <select id="editStudent" disabled>
                        <!-- 學生選項將由 JavaScript 動態生成 -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="editHomeworkItem">作業項目：</label>
                    <input type="text" id="editHomeworkItem" maxlength="50" required>
                </div>
                <div class="form-group">
                    <label for="editDate">日期：</label>
                    <input type="date" id="editDate" required>
                </div>
                <div class="form-group">
                    <label for="editScore">分數：</label>
                    <input type="number" id="editScore" value="3" min="0" max="10" required>
                </div>
                <div class="modal-buttons">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存修改
                    </button>
                    <button type="button" id="cancelEditBtn" class="btn btn-secondary">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/animal-config.js"></script>
    <script src="js/script.js"></script>
    <script src="js/homework-history.js"></script>
</body>
</html>