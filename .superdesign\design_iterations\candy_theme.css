/* 糖果般的柔和色調主題 - Candy Pastel Theme */
:root {
  /* 主要糖果色系 */
  --candy-pink: #FFB6C1;          /* 粉色糖果 */
  --candy-mint: #98FB98;          /* 薄荷綠 */
  --candy-sky: #87CEEB;           /* 天空藍 */
  --candy-lemon: #FFFFE0;         /* 檸檬黃 */
  --candy-lavender: #E6E6FA;      /* 薰衣草紫 */
  --candy-peach: #FFDAB9;         /* 蜜桃色 */
  --candy-cream: #FFF8DC;         /* 奶油白 */
  
  /* 深度糖果色（用於文字和邊框） */
  --candy-pink-deep: #FF69B4;
  --candy-mint-deep: #00FA9A;
  --candy-sky-deep: #4169E1;
  --candy-lemon-deep: #FFD700;
  --candy-lavender-deep: #9370DB;
  --candy-peach-deep: #FF7F50;
  
  /* 背景色系 */
  --background: #FFFEF7;           /* 溫暖米白 */
  --card-background: #FFFFFF;      /* 純白卡片 */
  --soft-gray: #F5F5F5;          /* 柔和灰 */
  
  /* 文字色系 */
  --text-primary: #4A4A4A;        /* 深灰文字 */
  --text-secondary: #7A7A7A;      /* 中灰文字 */
  --text-light: #A0A0A0;         /* 淺灰文字 */
  
  /* 字體設定 */
  --font-primary: 'Nunito', 'Poppins', sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 600;
  --font-weight-bold: 700;
  
  /* 圓角設定 - 氣泡感 */
  --radius-small: 12px;
  --radius-medium: 20px;
  --radius-large: 28px;
  --radius-xl: 40px;
  
  /* 陰影設定 - 柔和立體感 */
  --shadow-soft: 0 4px 20px rgba(255, 182, 193, 0.15);
  --shadow-medium: 0 8px 30px rgba(255, 182, 193, 0.2);
  --shadow-strong: 0 12px 40px rgba(255, 182, 193, 0.25);
  --shadow-glow: 0 0 20px rgba(255, 182, 193, 0.3);
  
  /* 漸變色系 */
  --gradient-pink: linear-gradient(135deg, var(--candy-pink) 0%, var(--candy-peach) 100%);
  --gradient-mint: linear-gradient(135deg, var(--candy-mint) 0%, var(--candy-sky) 100%);
  --gradient-lemon: linear-gradient(135deg, var(--candy-lemon) 0%, var(--candy-cream) 100%);
  --gradient-lavender: linear-gradient(135deg, var(--candy-lavender) 0%, var(--candy-pink) 100%);
  
  /* 動畫時間 */
  --transition-fast: 0.2s ease-out;
  --transition-medium: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 全域基礎樣式 */
* {
  box-sizing: border-box !important;
}

body {
  font-family: var(--font-primary) !important;
  background: var(--background) !important;
  color: var(--text-primary) !important;
  line-height: 1.6 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 氣泡卡片基礎樣式 */
.bubble-card {
  background: var(--card-background);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-soft);
  padding: 24px;
  transition: all var(--transition-medium);
  border: 2px solid transparent;
}

.bubble-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

/* 按鈕樣式 - Q彈感 */
.candy-button {
  background: var(--gradient-pink);
  border: none;
  border-radius: var(--radius-medium);
  padding: 12px 24px;
  font-family: var(--font-primary);
  font-weight: var(--font-weight-medium);
  color: white;
  cursor: pointer;
  transition: all var(--transition-medium);
  box-shadow: var(--shadow-soft);
  position: relative;
  overflow: hidden;
}

.candy-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-glow);
}

.candy-button:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease-out;
}

/* 不同顏色的按鈕變體 */
.candy-button.mint {
  background: var(--gradient-mint);
}

.candy-button.lemon {
  background: var(--gradient-lemon);
  color: var(--text-primary);
}

.candy-button.lavender {
  background: var(--gradient-lavender);
}

/* 標題樣式 */
h1, h2, h3 {
  font-family: var(--font-primary) !important;
  font-weight: var(--font-weight-bold) !important;
  color: var(--text-primary) !important;
  margin-bottom: 16px !important;
}

h1 {
  font-size: 2.5rem !important;
  background: var(--gradient-pink);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h2 {
  font-size: 2rem !important;
  color: var(--candy-pink-deep) !important;
}

h3 {
  font-size: 1.5rem !important;
  color: var(--candy-mint-deep) !important;
}

/* 輸入框樣式 */
.candy-input {
  border: 2px solid var(--candy-pink);
  border-radius: var(--radius-medium);
  padding: 12px 16px;
  font-family: var(--font-primary);
  font-size: 1rem;
  background: var(--card-background);
  transition: all var(--transition-medium);
  outline: none;
}

.candy-input:focus {
  border-color: var(--candy-pink-deep);
  box-shadow: var(--shadow-glow);
  transform: scale(1.02);
}

/* 進度條樣式 */
.candy-progress {
  background: var(--soft-gray);
  border-radius: var(--radius-xl);
  height: 12px;
  overflow: hidden;
  position: relative;
}

.candy-progress-bar {
  background: var(--gradient-mint);
  height: 100%;
  border-radius: var(--radius-xl);
  transition: width var(--transition-slow);
  position: relative;
}

.candy-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 動物頭像樣式 */
.animal-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-lavender);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  box-shadow: var(--shadow-soft);
  transition: all var(--transition-medium);
  border: 3px solid white;
}

.animal-avatar:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-glow);
}

/* 統計卡片樣式 */
.stat-card {
  background: var(--card-background);
  border-radius: var(--radius-large);
  padding: 20px;
  text-align: center;
  box-shadow: var(--shadow-soft);
  transition: all var(--transition-medium);
  border: 2px solid var(--candy-pink);
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.stat-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  background: var(--gradient-pink);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 8px;
}

/* 隨機事件卡片 */
.event-card {
  background: var(--gradient-lemon);
  border-radius: var(--radius-large);
  padding: 24px;
  border: 3px dashed var(--candy-lemon-deep);
  text-align: center;
  box-shadow: var(--shadow-medium);
  animation: gentle-glow 3s ease-in-out infinite alternate;
}

@keyframes gentle-glow {
  0% { box-shadow: var(--shadow-medium); }
  100% { box-shadow: var(--shadow-glow); }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .bubble-card {
    padding: 16px;
    border-radius: var(--radius-medium);
  }
  
  h1 {
    font-size: 2rem !important;
  }
  
  h2 {
    font-size: 1.5rem !important;
  }
  
  .animal-avatar {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}