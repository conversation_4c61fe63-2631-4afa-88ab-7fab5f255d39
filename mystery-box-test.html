<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>神祕寶箱功能測試</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/manual-score.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-header h1 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .test-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .test-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(102, 126, 234, 0.4);
        }
        
        .test-btn.secondary {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }
        
        .test-btn.secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 184, 148, 0.4);
        }
        
        .student-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .mini-student-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        
        .mini-student-card h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .mini-student-card .score {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .mini-student-card .status {
            margin-top: 8px;
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            background: #74b9ff;
            color: white;
            display: inline-block;
        }
        
        .event-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .event-card {
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s ease;
            color: white;
            font-weight: 600;
        }
        
        .event-card:hover {
            transform: scale(1.05);
        }
        
        .event-card.lucky { background: linear-gradient(135deg, #00b894, #00a085); }
        .event-card.steal { background: linear-gradient(135deg, #fdcb6e, #e17055); }
        .event-card.angel { background: linear-gradient(135deg, #74b9ff, #0984e3); }
        .event-card.devil { background: linear-gradient(135deg, #ff7675, #e84393); }
        .event-card.swap { background: linear-gradient(135deg, #a29bfe, #6c5ce7); }
        .event-card.transform { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎁 神祕寶箱功能測試</h1>
            <p>測試記分小達人系統的神祕寶箱隨機事件功能</p>
        </div>
        
        <div class="test-section">
            <h3>📋 測試學生資料</h3>
            <div class="student-preview" id="studentPreview">
                <!-- 學生資料將由 JavaScript 生成 -->
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="test-btn secondary" onclick="resetTestData()">
                    <i class="fas fa-refresh"></i> 重置測試資料
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎲 神祕寶箱觸發測試</h3>
            <p>點擊下方按鈕來模擬學生獲得加分並觸發神祕寶箱：</p>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="triggerMysteryBox('小明', 5)">
                    <i class="fas fa-gift"></i> 小明 +5分 (觸發寶箱)
                </button>
                <button class="test-btn primary" onclick="triggerMysteryBox('小華', 3)">
                    <i class="fas fa-gift"></i> 小華 +3分 (觸發寶箱)
                </button>
                <button class="test-btn primary" onclick="triggerMysteryBox('小美', 10)">
                    <i class="fas fa-gift"></i> 小美 +10分 (觸發寶箱)
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 直接測試特定事件</h3>
            <p>點擊下方卡片來直接測試特定的神祕寶箱事件：</p>
            <div class="event-demo">
                <div class="event-card lucky" onclick="testSpecificEvent('lucky')">
                    🍀<br>強運
                </div>
                <div class="event-card steal" onclick="testSpecificEvent('steal')">
                    💰<br>掠奪
                </div>
                <div class="event-card angel" onclick="testSpecificEvent('angel')">
                    😇<br>天使
                </div>
                <div class="event-card devil" onclick="testSpecificEvent('devil')">
                    😈<br>惡魔
                </div>
                <div class="event-card swap" onclick="testSpecificEvent('swap')">
                    🔄<br>交換
                </div>
                <div class="event-card transform" onclick="testSpecificEvent('transform')">
                    🎭<br>變身
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 功能說明</h3>
            <ul style="line-height: 1.8;">
                <li><strong>🍀 強運：</strong>當前獲得的分數翻倍</li>
                <li><strong>💰 掠奪：</strong>從其他隨機學生身上偷取5分</li>
                <li><strong>😇 天使：</strong>獲得一次「回頭免扣分」保護狀態</li>
                <li><strong>😈 惡魔：</strong>當前學生的總分數減半</li>
                <li><strong>🔄 交換：</strong>與隨機選定的另一位學生完全交換總分數</li>
                <li><strong>🎭 變身：</strong>一半機率變成班上最高分或最低分，可以選擇取消</li>
            </ul>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script src="js/mystery-box-config.js"></script>
    <script src="js/manual-score.js"></script>
    <script>
        // 測試用的學生資料
        let testStudents = [
            { id: 1, name: '拿破崙', number: '01', class: '603', score: 15 },
            { id: 2, name: '亞歷山大大帝', number: '02', class: '603', score: 20 },
            { id: 3, name: '凱撒大帝', number: '03', class: '603', score: 12 },
            { id: 4, name: '秦始皇', number: '04', class: '603', score: 18 },
            { id: 5, name: '漢武帝', number: '05', class: '603', score: 8 }
        ];
        
        // 初始化測試環境
        function initTest() {
            // 設置全局學生資料
            window.students = [...testStudents];
            updateStudentPreview();
        }
        
        // 更新學生預覽
        function updateStudentPreview() {
            const preview = document.getElementById('studentPreview');
            preview.innerHTML = '';
            
            testStudents.forEach(student => {
                const card = document.createElement('div');
                card.className = 'mini-student-card';
                
                const hasAngel = student.specialStates && student.specialStates.angelProtection;
                
                card.innerHTML = `
                    <h4>${student.name}</h4>
                    <div class="score">${student.score} 分</div>
                    ${hasAngel ? '<div class="status">😇 天使保護</div>' : ''}
                `;
                
                preview.appendChild(card);
            });
        }
        
        // 重置測試資料
        function resetTestData() {
            testStudents = [
                { id: 1, name: '拿破崙', number: '01', class: '603', score: 15 },
                { id: 2, name: '亞歷山大大帝', number: '02', class: '603', score: 20 },
                { id: 3, name: '凱撒大帝', number: '03', class: '603', score: 12 },
                { id: 4, name: '秦始皇', number: '04', class: '603', score: 18 },
                { id: 5, name: '漢武帝', number: '05', class: '603', score: 8 }
            ];
            window.students = [...testStudents];
            updateStudentPreview();
            alert('測試資料已重置！');
        }
        
        // 觸發神祕寶箱
        function triggerMysteryBox(studentName, scoreChange) {
            const student = testStudents.find(s => s.name === studentName);
            if (student) {
                // 先加分
                student.score += scoreChange;
                window.students = [...testStudents];
                
                // 觸發神祕寶箱
                if (typeof showMysteryBoxTrigger === 'function') {
                    showMysteryBoxTrigger(student, scoreChange);
                } else {
                    alert('神祕寶箱功能尚未載入，請確認 manual-score.js 已正確載入。');
                }
            }
        }
        
        // 測試特定事件
        function testSpecificEvent(eventType) {
            const student = testStudents[0]; // 使用第一個學生
            const originalChange = 5;
            
            // 創建模擬的神祕寶箱事件
            const events = {
                'lucky': { id: 'lucky', name: '強運', icon: '🍀', description: '當前獲得的分數翻倍', color: '#00b894' },
                'steal': { id: 'steal', name: '掠奪', icon: '💰', description: '從其他隨機學生身上偷取5分', color: '#fdcb6e' },
                'angel': { id: 'angel', name: '天使', icon: '😇', description: '獲得一次「回頭免扣分」保護', color: '#74b9ff' },
                'devil': { id: 'devil', name: '惡魔', icon: '😈', description: '當前總分數減半', color: '#ff7675' },
                'swap': { id: 'swap', name: '交換', icon: '🔄', description: '與隨機學生交換總分數', color: '#a29bfe' },
                'transform': { id: 'transform', name: '變身', icon: '🎭', description: '一半機率變成最高分或最低分', color: '#9b59b6' }
            };
            
            const event = events[eventType];
            if (event && typeof executeMysteryBoxEvent === 'function') {
                // 創建模擬彈窗
                const modal = document.createElement('div');
                modal.className = 'modal mystery-box-modal';
                modal.style.display = 'flex';
                document.body.appendChild(modal);
                
                window.students = [...testStudents];
                executeMysteryBoxEvent(student, originalChange, event, modal);
                
                // 更新顯示
                setTimeout(() => {
                    testStudents = [...window.students];
                    updateStudentPreview();
                }, 100);
            } else {
                alert('神祕寶箱功能尚未載入，請確認 manual-score.js 已正確載入。');
            }
        }
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
