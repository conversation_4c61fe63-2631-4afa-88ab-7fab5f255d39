<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轉盤測試頁面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .wheel-demo {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            margin: 20px auto;
            background: conic-gradient(
                from 0deg,
                #ff6b6b 0deg 36deg,
                #4ecdc4 36deg 108deg,
                #45b7d1 108deg 288deg,
                #f9ca24 288deg 360deg
            );
            border: 8px solid #fff;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            position: relative;
            transition: transform 4s cubic-bezier(0.17, 0.67, 0.12, 0.99);
        }
        .wheel-labels {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        .wheel-label {
            position: absolute;
            color: white;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        .wheel-label:nth-child(1) {
            top: 15%;
            left: 75%;
            transform: rotate(18deg);
        }
        .wheel-label:nth-child(2) {
            top: 35%;
            right: 15%;
            transform: rotate(72deg);
        }
        .wheel-label:nth-child(3) {
            bottom: 35%;
            left: 15%;
            transform: rotate(198deg);
        }
        .wheel-label:nth-child(4) {
            top: 35%;
            left: 15%;
            transform: rotate(324deg);
        }
        .pointer {
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-top: 40px solid #ff4757;
            z-index: 25;
        }
        .pointer::after {
            content: '';
            position: absolute;
            top: -35px;
            left: -15px;
            width: 30px;
            height: 30px;
            background: #ff4757;
            border-radius: 50%;
            border: 3px solid white;
        }
        .center-circle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            border: 6px solid white;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            z-index: 20;
        }
        button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 10px;
        }
        button:hover {
            background: #5a4fcf;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            background: #6c5ce7;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <h1>轉盤功能測試</h1>
    
    <div class="nav-links">
        <a href="lucky-draw.html">前往實際轉盤頁面</a>
        <a href="test.html">系統測試頁面</a>
    </div>
    
    <div class="test-section">
        <h2>轉盤視覺效果測試</h2>
        <div class="wheel-demo" id="testWheel">
            <div class="wheel-labels">
                <div class="wheel-label">
                    <i class="fas fa-star"></i>
                    <span>+10分</span>
                </div>
                <div class="wheel-label">
                    <i class="fas fa-rocket"></i>
                    <span>+5分</span>
                </div>
                <div class="wheel-label">
                    <i class="fas fa-thumbs-up"></i>
                    <span>+1分</span>
                </div>
                <div class="wheel-label">
                    <i class="fas fa-sad-tear"></i>
                    <span>-1分</span>
                </div>
            </div>
            <div class="center-circle">
                <i class="fas fa-star"></i>
            </div>
            <div class="pointer"></div>
        </div>
        
        <div style="text-align: center;">
            <button onclick="testSpin()">測試旋轉</button>
            <button onclick="resetWheel()">重置轉盤</button>
        </div>
        
        <div id="testResult" style="margin-top: 20px; text-align: center; font-weight: bold;"></div>
    </div>
    
    <div class="test-section">
        <h2>轉盤區域說明</h2>
        <ul>
            <li><strong style="color: #ff6b6b;">紅色區域 (0°-36°)</strong>: +10分 - 機率 10%</li>
            <li><strong style="color: #4ecdc4;">青色區域 (36°-108°)</strong>: +5分 - 機率 20%</li>
            <li><strong style="color: #45b7d1;">藍色區域 (108°-288°)</strong>: +1分 - 機率 50%</li>
            <li><strong style="color: #f9ca24;">黃色區域 (288°-360°)</strong>: -1分 - 機率 20%</li>
        </ul>
    </div>


    <script>
        let currentRotation = 0;
        
        function testSpin() {
            const wheel = document.getElementById('testWheel');
            const resultDiv = document.getElementById('testResult');
            
            // 隨機選擇一個結果
            const prizes = [
                { value: 10, angle: 18, color: '#ff6b6b', label: '+10分' },
                { value: 5, angle: 72, color: '#4ecdc4', label: '+5分' },
                { value: 1, angle: 198, color: '#45b7d1', label: '+1分' },
                { value: -1, angle: 324, color: '#f9ca24', label: '-1分' }
            ];
            
            const selectedPrize = prizes[Math.floor(Math.random() * prizes.length)];
            
            // 計算旋轉角度
            const extraSpins = 5 + Math.random() * 3;
            const targetAngle = 360 - selectedPrize.angle;
            const finalAngle = extraSpins * 360 + targetAngle;
            
            currentRotation += finalAngle;
            
            // 應用旋轉
            wheel.style.transform = `rotate(${currentRotation}deg)`;
            
            // 顯示結果
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div style="color: ${selectedPrize.color}; font-size: 1.5rem;">
                        結果: ${selectedPrize.label}
                    </div>
                `;
            }, 4000);
            
            resultDiv.innerHTML = '<div style="color: #666;">轉盤旋轉中...</div>';
        }
        
        function resetWheel() {
            const wheel = document.getElementById('testWheel');
            const resultDiv = document.getElementById('testResult');
            
            currentRotation = 0;
            wheel.style.transform = 'rotate(0deg)';
            wheel.style.transition = 'none';
            
            setTimeout(() => {
                wheel.style.transition = 'transform 4s cubic-bezier(0.17, 0.67, 0.12, 0.99)';
            }, 100);
            
            resultDiv.innerHTML = '';
        }
    </script>
</body>
</html>
