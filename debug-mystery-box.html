<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>神祕寶箱調試頁面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        /* 神祕寶箱樣式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }
        
        .mystery-box-content {
            max-width: 500px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .mystery-box-header h3 {
            margin-bottom: 10px;
            font-size: 1.5rem;
        }
        
        .mystery-box-animation {
            margin: 30px 0;
        }
        
        .treasure-box {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            position: relative;
        }
        
        .box-body {
            width: 80px;
            height: 50px;
            background: linear-gradient(135deg, #8B4513, #A0522D);
            border-radius: 8px;
            position: absolute;
            bottom: 0;
            border: 3px solid #654321;
        }
        
        .box-lid {
            width: 84px;
            height: 25px;
            background: linear-gradient(135deg, #DAA520, #FFD700);
            border-radius: 8px 8px 4px 4px;
            position: absolute;
            top: 0;
            left: -2px;
            border: 3px solid #B8860B;
            transition: transform 0.5s ease;
            transform-origin: bottom;
        }
        
        .treasure-box.opening .box-lid {
            transform: rotateX(-45deg) translateY(-10px);
        }
        
        .mystery-box-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .mystery-open-btn {
            background: linear-gradient(135deg, #00b894, #00a085);
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            color: white;
            cursor: pointer;
        }
        
        .mystery-skip-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🎁 神祕寶箱調試頁面</h1>
        
        <div>
            <h3>測試按鈕</h3>
            <button class="test-btn" onclick="testMysteryBox()">
                <i class="fas fa-gift"></i> 測試神祕寶箱觸發
            </button>
            <button class="test-btn" onclick="testDirectEvent()">
                <i class="fas fa-star"></i> 直接測試事件
            </button>
            <button class="test-btn" onclick="clearDebugLog()">
                <i class="fas fa-trash"></i> 清除日誌
            </button>
        </div>
        
        <div>
            <h3>調試信息</h3>
            <div class="debug-info" id="debugLog">等待測試...</div>
        </div>
    </div>

    <script>
        // 調試日誌
        let debugLog = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${message}`);
            document.getElementById('debugLog').textContent = debugLog.join('\n');
            console.log(message);
        }
        
        function clearDebugLog() {
            debugLog = [];
            document.getElementById('debugLog').textContent = '日誌已清除...';
        }
        
        // 測試學生資料
        const testStudent = {
            id: 1,
            name: '測試學生',
            number: '01',
            class: '603',
            score: 15
        };
        
        // 測試神祕寶箱
        function testMysteryBox() {
            log('開始測試神祕寶箱...');
            
            try {
                // 創建神祕寶箱彈窗
                const mysteryModal = document.createElement('div');
                mysteryModal.className = 'modal';
                mysteryModal.style.display = 'flex';
                
                mysteryModal.innerHTML = `
                    <div class="mystery-box-content">
                        <div class="mystery-box-header">
                            <h3>🎁 神祕寶箱出現了！</h3>
                            <p>${testStudent.name} 獲得了 +5 分，觸發了神祕事件！</p>
                        </div>
                        <div class="mystery-box-animation">
                            <div class="treasure-box">
                                <div class="box-lid"></div>
                                <div class="box-body"></div>
                            </div>
                        </div>
                        <div class="mystery-box-actions">
                            <button class="mystery-open-btn">
                                <i class="fas fa-gift"></i> 開啟寶箱
                            </button>
                            <button class="mystery-skip-btn">
                                <i class="fas fa-times"></i> 跳過
                            </button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(mysteryModal);
                log('神祕寶箱彈窗已創建');
                
                // 綁定事件
                const openBtn = mysteryModal.querySelector('.mystery-open-btn');
                const skipBtn = mysteryModal.querySelector('.mystery-skip-btn');
                
                if (openBtn) {
                    openBtn.addEventListener('click', () => {
                        log('點擊了開啟寶箱按鈕');
                        openBox(mysteryModal);
                    });
                    log('開啟按鈕事件已綁定');
                } else {
                    log('錯誤：找不到開啟按鈕');
                }
                
                if (skipBtn) {
                    skipBtn.addEventListener('click', () => {
                        log('點擊了跳過按鈕');
                        closeModal(mysteryModal);
                    });
                    log('跳過按鈕事件已綁定');
                } else {
                    log('錯誤：找不到跳過按鈕');
                }
                
                // 點擊背景關閉
                mysteryModal.addEventListener('click', (e) => {
                    if (e.target === mysteryModal) {
                        log('點擊背景關閉彈窗');
                        closeModal(mysteryModal);
                    }
                });
                
                log('神祕寶箱測試完成，請點擊按鈕進行互動');
                
            } catch (error) {
                log('錯誤：' + error.message);
            }
        }
        
        function openBox(modal) {
            log('開始開箱動畫...');
            
            const box = modal.querySelector('.treasure-box');
            const actions = modal.querySelector('.mystery-box-actions');
            
            // 隱藏按鈕
            actions.style.display = 'none';
            
            // 開箱動畫
            box.classList.add('opening');
            
            setTimeout(() => {
                log('開箱完成，顯示隨機事件結果');
                showResult(modal);
            }, 2000);
        }
        
        function showResult(modal) {
            const events = ['強運', '掠奪', '天使', '惡魔', '交換'];
            const randomEvent = events[Math.floor(Math.random() * events.length)];
            
            const content = modal.querySelector('.mystery-box-content');
            content.innerHTML = `
                <div style="padding: 20px;">
                    <div style="font-size: 4rem; margin-bottom: 15px;">🎉</div>
                    <h3>${randomEvent}</h3>
                    <p>恭喜！您獲得了 ${randomEvent} 事件！</p>
                    <button class="mystery-open-btn" onclick="closeModal(this.closest('.modal'))">
                        確定
                    </button>
                </div>
            `;
            
            log(`隨機事件結果：${randomEvent}`);
        }
        
        function closeModal(modal) {
            log('關閉神祕寶箱彈窗');
            modal.style.display = 'none';
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 100);
        }
        
        function testDirectEvent() {
            log('直接測試事件效果...');
            const events = ['🍀 強運', '💰 掠奪', '😇 天使', '😈 惡魔', '🔄 交換'];
            const randomEvent = events[Math.floor(Math.random() * events.length)];
            log(`隨機選中事件：${randomEvent}`);
            alert(`測試事件：${randomEvent}`);
        }
        
        // 頁面載入完成
        document.addEventListener('DOMContentLoaded', function() {
            log('調試頁面載入完成');
        });
    </script>
</body>
</html>
