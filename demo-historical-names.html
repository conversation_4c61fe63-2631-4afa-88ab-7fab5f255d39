<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試歷史人物名字更新功能</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: white;
            color: #333;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .btn:hover {
            background: linear-gradient(135deg, #00a085 0%, #00b7b8 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);
        }
        .demo-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .demo-item {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 歷史人物名字庫展示</h1>
        
        <div class="info">
            <strong>📚 歷史人物名字庫包含：</strong><br>
            • 古代帝王：拿破崙、秦始皇、唐太宗等<br>
            • 藝術家：達文西、米開朗基羅、莫札特等<br>
            • 科學家：牛頓、愛因斯坦、居禮夫人等<br>
            • 哲學家：孔子、蘇格拉底、柏拉圖等<br>
            • 文學家：莎士比亞、李白、杜甫等<br>
            • 還有更多各領域的歷史名人！
        </div>

        <div style="text-align: center;">
            <button class="btn" onclick="showRandomNames()">
                <i>🎲</i> 隨機顯示20位歷史人物
            </button>
            <button class="btn" onclick="showAllCategories()">
                <i>📜</i> 顯示所有類別
            </button>
        </div>

        <div id="result" class="demo-list"></div>
    </div>

    <script>
        // 歷史人物名字庫（簡化版本）
        const historicalFigures = [
            // 古代帝王
            '拿破崙', '亞歷山大大帝', '凱撒大帝', '秦始皇', '漢武帝', '唐太宗', '康熙皇帝',
            '武則天', '埃及豔后', '伊莉莎白一世', '路易十四', '彼得大帝', '查理曼大帝',
            '成吉思汗', '忽必烈', '朱元璋', '雍正皇帝', '乾隆皇帝', '明治天皇',

            // 藝術家與音樂家
            '達文西', '米開朗基羅', '莫札特', '貝多芬', '巴哈', '蕭邦', '畢卡索', '梵谷',
            '莎士比亞', '但丁', '歌德', '托爾斯泰', '雨果', '狄更斯', '拉斐爾', '莫內',
            '雷諾瓦', '羅丹', '李白', '杜甫', '蘇東坡', '王羲之', '顏真卿',

            // 科學家與發明家
            '牛頓', '愛因斯坦', '居禮夫人', '達爾文', '伽利略', '哥白尼', '愛迪生', '特斯拉',
            '法拉第', '孟德爾', '巴斯德', '弗萊明', '華生', '克里克', '霍金', '諾貝爾',
            '萊特兄弟', '貝爾', '馬可尼', '富蘭克林', '阿基米德', '歐幾里得',

            // 哲學家與思想家
            '孔子', '老子', '莊子', '孟子', '荀子', '蘇格拉底', '柏拉圖', '亞里斯多德', 
            '笛卡爾', '康德', '尼采', '盧梭', '伏爾泰', '洛克', '休謨', '黑格爾',

            // 古代名將與英雄
            '項羽', '劉邦', '關羽', '張飛', '趙雲', '諸葛亮', '韓信', '岳飛',
            '文天祥', '鄭成功', '戚繼光', '霍去病', '衛青', '李廣', '馬援',

            // 女性歷史人物
            '居禮夫人', '南丁格爾', '德蕾莎修女', '海倫凱勒', '安妮法蘭克', '貞德',
            '花木蘭', '梁紅玉', '李清照', '慈禧太后'
        ];

        function showRandomNames() {
            const shuffled = [...historicalFigures].sort(() => Math.random() - 0.5);
            const selected = shuffled.slice(0, 20);
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = selected.map(name => 
                `<div class="demo-item">${name}</div>`
            ).join('');
        }

        function showAllCategories() {
            const categories = [
                '古代帝王', '藝術家音樂家', '科學家發明家', '哲學家思想家',
                '古代名將英雄', '女性歷史人物', '探險家航海家', '政治家改革家',
                '宗教領袖', '企業家創新者', '現代政治人物', '體育明星'
            ];
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = categories.map(category => 
                `<div class="demo-item">📚 ${category}</div>`
            ).join('');
        }

        // 頁面載入時自動顯示一些歷史人物
        window.addEventListener('load', showRandomNames);
    </script>
</body>
</html>