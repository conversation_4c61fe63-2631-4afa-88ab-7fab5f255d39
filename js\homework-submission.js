// 作業繳交功能 - 重新設計版本
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const homeworkTableBody = document.getElementById('homeworkTableBody');
    const searchInput = document.getElementById('searchInput');
    const confirmModal = document.getElementById('confirmModal');
    const confirmMessage = document.getElementById('confirmMessage');
    const studentNameSpan = document.getElementById('studentNameSpan');
    const homeworkItemSpan = document.getElementById('homeworkItemSpan');
    const confirmSubmissionBtn = document.getElementById('confirmSubmissionBtn');
    const cancelSubmissionBtn = document.getElementById('cancelSubmissionBtn');
    const downloadHistoryBtn = document.getElementById('downloadHistoryBtn');
    const markAllSubmittedBtn = document.getElementById('markAllSubmittedBtn');
    const markAllNotSubmittedBtn = document.getElementById('markAllNotSubmittedBtn');
    const closeModalBtns = document.querySelectorAll('.close');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');
    const currentDateElement = document.getElementById('currentDate');
    const totalStudentsElement = document.getElementById('totalStudents');
    const totalHomeworkItemsElement = document.getElementById('totalHomeworkItems');
    const completionRateElement = document.getElementById('completionRate');
    const todayPointsElement = document.getElementById('todayPoints');
    const paginationElement = document.getElementById('pagination');
    
    // 作業項目管理元素
    const homeworkInput = document.getElementById('homeworkInput');
    const addHomeworkBtn = document.getElementById('addHomeworkBtn');
    const homeworkItems = document.getElementById('homeworkItems');
    const homeworkColumns = document.getElementById('homeworkColumns');
    
    // 常用作業庫管理元素
    const frequentHomeworkInput = document.getElementById('frequentHomeworkInput');
    const addFrequentHomeworkBtn = document.getElementById('addFrequentHomeworkBtn');
    const frequentHomeworkItems = document.getElementById('frequentHomeworkItems');
    
    // 常數設定
    const ITEMS_PER_PAGE = 10; // 每頁顯示的學生數量
    const HOMEWORK_REWARD = 3; // 每項作業繳交獎勵分數
    
    // 狀態變數
    let students = [];
    let filteredStudents = [];
    let currentPage = 1;
    let selectedStudent = null;
    let selectedHomeworkItem = null;
    let today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    let homeworkHistory = [];
    let dailyHomework = {}; // 每日作業項目
    let frequentHomework = []; // 常用作業項目
    
    // 初始化頁面
    function initPage() {
        // 設置當前日期
        const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
        const todayFormatted = new Date().toLocaleDateString('zh-TW', options);
        currentDateElement.textContent = todayFormatted;

        // 載入資料（按順序載入，確保資料完整性）
        loadHomeworkHistory();
        loadDailyHomework();
        loadFrequentHomework(); // 載入常用作業庫
        loadStudents(); // 最後載入學生資料，這樣會在所有資料載入完成後才渲染表格
        
        // 事件監聽器
        addHomeworkBtn.addEventListener('click', addHomeworkItem);
        homeworkInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                addHomeworkItem();
            }
        });
        
        // 常用作業庫事件監聽器
        addFrequentHomeworkBtn.addEventListener('click', addFrequentHomeworkItem);
        frequentHomeworkInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                addFrequentHomeworkItem();
            }
        });
        searchInput.addEventListener('input', filterStudents);
        cancelSubmissionBtn.addEventListener('click', closeModal);
        downloadHistoryBtn.addEventListener('click', downloadHistory);
        markAllSubmittedBtn.addEventListener('click', markAllAsSubmitted);
        markAllNotSubmittedBtn.addEventListener('click', markAllAsNotSubmitted);
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', closeModal);
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === confirmModal) {
                closeModal();
            }
        });

        // 匯入匯出功能
        exportBtn.addEventListener('click', exportData);
        importBtn.addEventListener('click', () => importFile.click());
        importFile.addEventListener('change', importData);
    }
    
    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
            
            // 初始化每個學生的作業繳交記錄
            students.forEach(student => {
                if (!student.homeworkSubmission) {
                    student.homeworkSubmission = {};
                }
                
                // 如果今天還沒有作業記錄，初始化為空物件
                if (!student.homeworkSubmission[today]) {
                    student.homeworkSubmission[today] = {};
                }
            });
            
            saveStudents();
            updateDisplay();
        } else {
            // 如果沒有學生資料，顯示空狀態
            displayEmptyState();
        }
    }
    
    // 載入作業繳交歷史記錄
    function loadHomeworkHistory() {
        const savedHistory = localStorage.getItem('homeworkHistory');
        if (savedHistory) {
            homeworkHistory = JSON.parse(savedHistory);
        } else {
            homeworkHistory = [];
        }
    }
    
    // 載入每日作業項目
    function loadDailyHomework() {
        const savedDailyHomework = localStorage.getItem('dailyHomework');
        if (savedDailyHomework) {
            dailyHomework = JSON.parse(savedDailyHomework);
        } else {
            dailyHomework = {};
        }
        
        // 如果今天沒有作業項目，初始化為空陣列
        if (!dailyHomework[today]) {
            dailyHomework[today] = [];
        }
        
        renderHomeworkItems();
        renderTableHeader();
    }
    
    // 保存學生資料
    function saveStudents() {
        localStorage.setItem('students', JSON.stringify(students));
    }
    
    // 保存作業繳交歷史記錄
    function saveHomeworkHistory() {
        localStorage.setItem('homeworkHistory', JSON.stringify(homeworkHistory));
    }
    
    // 保存每日作業項目
    function saveDailyHomework() {
        localStorage.setItem('dailyHomework', JSON.stringify(dailyHomework));
    }
    
    // 新增作業項目
    function addHomeworkItem() {
        console.log('addHomeworkItem 函式被調用');
        
        if (!homeworkInput) {
            console.error('homeworkInput 元素未找到');
            alert('系統錯誤：找不到輸入框元素');
            return;
        }
        
        const itemName = homeworkInput.value.trim();
        console.log('itemName:', itemName);
        
        if (!itemName) {
            alert('請輸入作業項目名稱！');
            return;
        }
        
        // 確保今天的作業陣列存在
        if (!dailyHomework[today]) {
            dailyHomework[today] = [];
        }
        
        if ((dailyHomework[today] || []).includes(itemName)) {
            alert('此作業項目已經存在！');
            return;
        }
        
        try {
            // 添加作業項目
            dailyHomework[today].push(itemName);
            homeworkInput.value = '';
            
            // 保存數據
            saveDailyHomework();
            
            // 更新界面
            renderHomeworkItems();
            renderTableHeader();
            updateDisplay();
            
            showSuccessMessage(`已新增作業項目：${itemName}`);
            console.log('作業項目新增成功:', itemName);
            console.log('當前作業項目:', dailyHomework[today]);
        } catch (error) {
            console.error('新增作業項目時發生錯誤:', error);
            alert('新增作業項目時發生錯誤，請重試！');
        }
    }
    
    // 移除作業項目
    function removeHomeworkItem(itemName) {
        if (confirm(`確定要刪除作業項目「${itemName}」嗎？\n這將同時刪除所有相關的繳交記錄！`)) {
            const itemIndex = (dailyHomework[today] || []).indexOf(itemName);
            if (itemIndex > -1) {
                (dailyHomework[today] || []).splice(itemIndex, 1);
                
                // 移除所有學生對這個作業項目的繳交記錄
                students.forEach(student => {
                    if (student.homeworkSubmission[today] && student.homeworkSubmission[today][itemName]) {
                        student.score -= HOMEWORK_REWARD; // 退還分數
                        delete student.homeworkSubmission[today][itemName];
                    }
                });
                
                // 從歷史記錄中移除相關記錄
                homeworkHistory = homeworkHistory.filter(record => 
                    !(record.date === today && record.homeworkItem === itemName)
                );
                
                saveStudents();
                saveHomeworkHistory();
                saveDailyHomework();
                renderHomeworkItems();
                renderTableHeader();
                updateDisplay();
                showSuccessMessage(`已刪除作業項目：${itemName}`);
            }
        }
    }
    
    // 渲染作業項目
    function renderHomeworkItems() {
        if (!homeworkItems) return;
        
        if ((dailyHomework[today] || []).length === 0) {
            homeworkItems.innerHTML = `
                <p class="empty-today-hint">
                    <i class="fas fa-arrow-up"></i> 
                    點擊上方常用作業庫的項目可快速添加，或手動輸入新的作業項目！
                </p>
            `;
        } else {
            const itemsHTML = (dailyHomework[today] || []).map(item => `
                <div class="homework-item">
                    <span>${item}</span>
                    <button class="homework-item-remove" onclick="removeHomeworkItem('${item}')">×</button>
                </div>
            `).join('');
            homeworkItems.innerHTML = itemsHTML;
        }
    }
    
    // 渲染表格標題
    function renderTableHeader() {
        const table = document.getElementById('homeworkTable');
        const thead = table.querySelector('thead tr');
        
        // 清除現有的動態欄位
        const dynamicColumns = thead.querySelectorAll('.homework-column');
        dynamicColumns.forEach(col => col.remove());
        
        // 插入作業項目欄位
        const completionColumn = thead.querySelector('th:nth-child(6)'); // 完成度欄位
        
        (dailyHomework[today] || []).forEach(item => {
            const th = document.createElement('th');
            th.className = 'homework-column';
            th.textContent = item;
            th.style.minWidth = '80px';
            th.style.textAlign = 'center';
            thead.insertBefore(th, completionColumn);
        });
    }
    
    // 更新顯示
    function updateDisplay() {
        filteredStudents = [...students];
        updateSummary();
        renderTable();
        renderPagination();
    }
    
    // 更新摘要資訊
    function updateSummary() {
        const totalStudents = students.length;
        const totalHomeworkItems = (dailyHomework[today] || []).length;
        let totalSubmissions = 0;
        let todayPoints = 0;
        
        students.forEach(student => {
            const todaySubmissions = student.homeworkSubmission[today] || {};
            const submittedCount = Object.keys(todaySubmissions).length;
            totalSubmissions += submittedCount;
            todayPoints += submittedCount * HOMEWORK_REWARD;
        });
        
        const completionRate = totalHomeworkItems > 0 && totalStudents > 0 
            ? Math.round((totalSubmissions / (totalStudents * totalHomeworkItems)) * 100) 
            : 0;
        
        totalStudentsElement.textContent = totalStudents;
        totalHomeworkItemsElement.textContent = totalHomeworkItems;
        completionRateElement.textContent = `${completionRate}%`;
        todayPointsElement.textContent = todayPoints;
    }
    
    // 渲染表格
    function renderTable() {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        const pageStudents = filteredStudents.slice(startIndex, endIndex);
        
        homeworkTableBody.innerHTML = '';
        
        pageStudents.forEach(student => {
            const row = createStudentRow(student);
            homeworkTableBody.appendChild(row);
        });
    }
    
    // 創建學生行
    function createStudentRow(student) {
        const row = document.createElement('tr');
        const todaySubmissions = student.homeworkSubmission[today] || {};
        const submittedCount = Object.keys(todaySubmissions).length;
        const totalHomeworkCount = (dailyHomework[today] || []).length;
        const completionPercentage = totalHomeworkCount > 0 
            ? Math.round((submittedCount / totalHomeworkCount) * 100) 
            : 0;
        const dailyScore = submittedCount * HOMEWORK_REWARD;
        
        // 基本資訊欄位
        let rowHTML = `
            <td>
                ${(() => {
                    let animalDisplay = student.animal || '🐱';
                    if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                        const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                        const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                        animalDisplay = `<div class="student-animal-avatar">
                            <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                            <span class="animal-emoji">${animalInfo.emoji}</span>
                        </div>`;
                    }
                    return `<span style="font-size: 24px;">${animalDisplay}</span>`;
                })()}
            </td>
            <td class="student-id">${student.number}</td>
            <td class="student-name">${student.name}</td>
            <td class="score-display">${student.score}</td>
            <td></td>
        `;

        // 動態生成作業項目欄位
        (dailyHomework[today] || []).forEach(item => {
            const isSubmitted = todaySubmissions[item];
            rowHTML += `
                <td style="text-align: center;">
                    <button class="homework-button ${isSubmitted ? 'submitted' : 'not-submitted'}"
                            onclick="toggleHomeworkSubmission('${student.id}', '${item}')">
                        ${isSubmitted ? '<i class="fas fa-check"></i>' : '<i class="fas fa-times"></i>'}
                    </button>
                </td>
            `;
        });
        
        // 完成度和得分欄位
        const completionClass = completionPercentage === 100 ? 'completion-100' :
                              completionPercentage >= 75 ? 'completion-75' :
                              completionPercentage >= 50 ? 'completion-50' : 'completion-0';
        
        rowHTML += `
            <td>
                <span class="completion-rate ${completionClass}">${completionPercentage}%</span>
            </td>
            <td>
                <span class="daily-score">${dailyScore}</span>
            </td>
        `;
        
        row.innerHTML = rowHTML;
        return row;
    }
    
    // 切換作業繳交狀態
    window.toggleHomeworkSubmission = function(studentId, homeworkItem) {
        // 確保資料已載入
        if (!students || students.length === 0) {
            loadStudents();
        }
        if (!dailyHomework || Object.keys(dailyHomework).length === 0) {
            loadDailyHomework();
        }
        if (!homeworkHistory || homeworkHistory.length === 0) {
            loadHomeworkHistory();
        }

        // 將 studentId 轉換為數字類型以匹配資料格式
        const numericStudentId = parseInt(studentId);
        const student = students.find(s => s.id === numericStudentId);
        if (!student) {
            console.error('找不到學生:', studentId, '(轉換後:', numericStudentId, ')');
            console.log('可用學生:', students.map(s => ({ id: s.id, name: s.name })));
            return;
        }

        // 確保學生有作業繳交記錄結構
        if (!student.homeworkSubmission) {
            student.homeworkSubmission = {};
        }
        if (!student.homeworkSubmission[today]) {
            student.homeworkSubmission[today] = {};
        }

        const isSubmitted = student.homeworkSubmission[today][homeworkItem];
        
        if (isSubmitted) {
            // 取消繳交 - 直接執行，不需要確認
            // 移除繳交記錄
            delete student.homeworkSubmission[today][homeworkItem];

            // 扣除分數
            student.score -= HOMEWORK_REWARD;

            // 從歷史記錄中移除
            const historyIndex = homeworkHistory.findIndex(h =>
                h.studentId == numericStudentId && // 使用寬鬆比較以匹配不同類型的ID
                h.date === today &&
                h.homeworkItem === homeworkItem
            );

            if (historyIndex !== -1) {
                homeworkHistory.splice(historyIndex, 1);
            }

            saveStudents();
            saveHomeworkHistory();
            updateDisplay();
            showTopNotification(`已取消 ${student.name} 的「${homeworkItem}」繳交記錄`, 'cancel');
        } else {
            // 新增繳交 - 直接執行，不需要確認
            const now = new Date();
            const timestamp = now.toISOString();

            // 添加到學生的今日作業記錄
            student.homeworkSubmission[today][homeworkItem] = {
                timestamp: timestamp,
                date: today
            };

            // 增加分數
            student.score += HOMEWORK_REWARD;

            // 添加到歷史記錄
            homeworkHistory.push({
                studentId: numericStudentId, // 使用數字類型的ID
                studentName: student.name,
                homeworkItem: homeworkItem,
                date: today,
                timestamp: timestamp,
                score: HOMEWORK_REWARD,
                id: Date.now() + Math.random() // 唯一ID
            });

            saveStudents();
            saveHomeworkHistory();
            updateDisplay();
            showTopNotification(`${student.name} 的「${homeworkItem}」繳交成功！獲得 ${HOMEWORK_REWARD} 分`, 'success');
        }
    };
    

    
    // 篩選學生
    function filterStudents() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        
        if (searchTerm === '') {
            filteredStudents = [...students];
        } else {
            filteredStudents = students.filter(student =>
                student.name.toLowerCase().includes(searchTerm) ||
                student.number.toString().includes(searchTerm)
            );
        }
        
        currentPage = 1;
        renderTable();
        renderPagination();
    }
    
    // 渲染分頁
    function renderPagination() {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        paginationElement.innerHTML = '';
        
        if (totalPages <= 1) return;
        
        // 上一頁按鈕
        const prevBtn = document.createElement('button');
        prevBtn.textContent = '上一頁';
        prevBtn.disabled = currentPage === 1;
        prevBtn.addEventListener('click', () => changePage(currentPage - 1));
        paginationElement.appendChild(prevBtn);
        
        // 頁碼按鈕
        for (let i = 1; i <= totalPages; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.classList.toggle('active', i === currentPage);
            pageBtn.addEventListener('click', () => changePage(i));
            paginationElement.appendChild(pageBtn);
        }
        
        // 下一頁按鈕
        const nextBtn = document.createElement('button');
        nextBtn.textContent = '下一頁';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.addEventListener('click', () => changePage(currentPage + 1));
        paginationElement.appendChild(nextBtn);
    }
    
    // 切換頁面
    function changePage(page) {
        currentPage = page;
        renderTable();
        renderPagination();
    }

    // 全部繳交功能
    function markAllAsSubmitted() {
        // 檢查是否有今日作業項目
        const todayHomework = dailyHomework[today] || [];
        if (todayHomework.length === 0) {
            alert('今日沒有作業項目，無法執行全部繳交操作！');
            return;
        }

        // 確認對話框
        const confirmMessage = `確定要將所有學生的今日作業項目標記為已繳交嗎？\n\n今日作業項目：${todayHomework.join('、')}\n總學生數：${students.length} 人\n每人將獲得：${todayHomework.length * HOMEWORK_REWARD} 分`;

        if (!confirm(confirmMessage)) {
            return;
        }

        let updatedCount = 0;
        let totalPointsAwarded = 0;

        // 為每個學生標記所有作業項目為已繳交
        students.forEach(student => {
            // 初始化學生的作業繳交記錄
            if (!student.homeworkSubmission) {
                student.homeworkSubmission = {};
            }
            if (!student.homeworkSubmission[today]) {
                student.homeworkSubmission[today] = {};
            }

            // 檢查每個作業項目
            todayHomework.forEach(homeworkItem => {
                // 只處理尚未繳交的作業項目
                if (!student.homeworkSubmission[today][homeworkItem]) {
                    student.homeworkSubmission[today][homeworkItem] = true;
                    updatedCount++;

                    // 增加學生分數
                    student.score = (student.score || 0) + HOMEWORK_REWARD;
                    totalPointsAwarded += HOMEWORK_REWARD;

                    // 添加到歷史記錄
                    const historyRecord = {
                        timestamp: new Date().toISOString(),
                        studentId: student.id,
                        studentName: student.name,
                        homeworkItem: homeworkItem,
                        score: HOMEWORK_REWARD, // 統一使用 score 而不是 points
                        date: today,
                        id: Date.now() + Math.random() // 唯一ID
                    };
                    homeworkHistory.push(historyRecord);
                }
            });
        });

        // 保存資料
        saveStudents();
        saveHomeworkHistory();

        // 更新顯示
        updateDisplay();

        // 顯示成功消息
        const successMessage = `全部繳交操作完成！\n\n更新項目數：${updatedCount} 項\n總獲得分數：${totalPointsAwarded} 分`;
        alert(successMessage);

        console.log('全部繳交操作完成:', {
            updatedCount,
            totalPointsAwarded,
            todayHomework,
            studentsCount: students.length
        });
    }

    // 全部取消繳交功能
    function markAllAsNotSubmitted() {
        // 檢查是否有今日作業項目
        const todayHomework = dailyHomework[today] || [];
        if (todayHomework.length === 0) {
            alert('今日沒有作業項目，無法執行全部取消操作！');
            return;
        }

        // 確認對話框
        const confirmMessage = `確定要將所有學生的今日作業項目標記為未繳交嗎？\n\n今日作業項目：${todayHomework.join('、')}\n總學生數：${students.length} 人\n每人將扣除：${todayHomework.length * HOMEWORK_REWARD} 分`;

        if (!confirm(confirmMessage)) {
            return;
        }

        let updatedCount = 0;
        let totalPointsDeducted = 0;

        // 為每個學生取消所有作業項目的繳交狀態
        students.forEach(student => {
            // 檢查學生是否有作業繳交記錄
            if (!student.homeworkSubmission || !student.homeworkSubmission[today]) {
                return; // 如果沒有今日記錄，跳過
            }

            // 檢查每個作業項目
            todayHomework.forEach(homeworkItem => {
                // 只處理已經繳交的作業項目
                if (student.homeworkSubmission[today][homeworkItem] === true) {
                    student.homeworkSubmission[today][homeworkItem] = false;
                    updatedCount++;

                    // 扣除學生分數
                    student.score = (student.score || 0) - HOMEWORK_REWARD;
                    totalPointsDeducted += HOMEWORK_REWARD;

                    // 添加到歷史記錄（負分記錄）
                    const historyRecord = {
                        timestamp: new Date().toISOString(),
                        studentId: student.id,
                        studentName: student.name,
                        homeworkItem: homeworkItem,
                        score: -HOMEWORK_REWARD, // 負分表示取消繳交，統一使用 score
                        date: today,
                        action: 'cancel', // 標記為取消操作
                        id: Date.now() + Math.random() // 唯一ID
                    };
                    homeworkHistory.push(historyRecord);
                }
            });
        });

        // 保存資料
        saveStudents();
        saveHomeworkHistory();

        // 更新顯示
        updateDisplay();

        // 顯示成功消息
        const successMessage = `全部取消操作完成！\n\n取消項目數：${updatedCount} 項\n總扣除分數：${totalPointsDeducted} 分`;
        alert(successMessage);

        console.log('全部取消操作完成:', {
            updatedCount,
            totalPointsDeducted,
            todayHomework,
            studentsCount: students.length
        });
    }

    // 下載歷史記錄
    function downloadHistory() {
        const csvContent = generateHistoryCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `作業繳交記錄_${today}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    // 生成歷史記錄CSV
    function generateHistoryCSV() {
        const headers = ['日期', '時間', '學號', '姓名', '作業項目', '獲得分數'];
        const rows = [headers];
        
        homeworkHistory
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .forEach(record => {
                rows.push([
                    record.date,
                    formatTime(record.timestamp),
                    record.studentId,
                    record.studentName,
                    record.homeworkItem || '未指定',
                    record.score
                ]);
            });
        
        return rows.map(row => row.join(',')).join('\n');
    }
    
    // 匯出資料
    function exportData() {
        const data = {
            students: students,
            homeworkHistory: homeworkHistory,
            dailyHomework: dailyHomework,
            exportDate: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `作業繳交資料_${today}.json`;
        link.click();
        URL.revokeObjectURL(url);
    }
    
    // 匯入資料
    function importData(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.students && data.homeworkHistory) {
                        if (confirm('匯入資料將覆蓋現有資料，確定要繼續嗎？')) {
                            students = data.students;
                            homeworkHistory = data.homeworkHistory;
                            if (data.dailyHomework) {
                                dailyHomework = data.dailyHomework;
                            }
                            saveStudents();
                            saveHomeworkHistory();
                            saveDailyHomework();
                            loadDailyHomework();
                            updateDisplay();
                            populateStudentFilter();
                            showSuccessMessage('資料匯入成功！');
                        }
                    } else {
                        alert('無效的資料格式！');
                    }
                } catch (error) {
                    alert('檔案格式錯誤！');
                }
            };
            reader.readAsText(file);
        }
        event.target.value = '';
    }
    
    // 關閉彈窗
    function closeModal() {
        confirmModal.style.display = 'none';
        selectedStudent = null;
        selectedHomeworkItem = null;
    }
    
    // 顯示空狀態
    function displayEmptyState() {
        homeworkTableBody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 40px; color: #999;">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.3;"></i>
                    <div>尚未加入任何學生</div>
                    <div style="margin-top: 10px;">
                        <a href="index.html" class="btn btn-primary">前往學生管理</a>
                    </div>
                </td>
            </tr>
        `;
    }
    
    // 格式化時間
    function formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-TW', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        });
    }
    
    // 顯示成功消息
    function showSuccessMessage(message) {
        // 創建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = 'success-message';
        messageEl.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        
        // 添加樣式
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            animation: slideIn 0.3s ease;
        `;
        
        // 添加到頁面
        document.body.appendChild(messageEl);
        
        // 3秒後移除
        setTimeout(() => {
            messageEl.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }
    
    // 全域函數：移除作業項目
    window.removeHomeworkItem = function(itemName) {
        removeHomeworkItem(itemName);
    };
    
    // 添加動畫樣式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    `;
    document.head.appendChild(style);

    // 顯示頂部通知
    function showTopNotification(message, type = 'success') {
        // 移除現有的通知
        const existingNotification = document.querySelector('.top-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // 創建通知元素
        const notification = document.createElement('div');
        notification.className = `top-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加樣式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? 'linear-gradient(135deg, #4CAF50, #45a049)' : 'linear-gradient(135deg, #f44336, #d32f2f)'};
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 16px;
            font-weight: 500;
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
            transition: all 0.3s ease;
            max-width: 90%;
            text-align: center;
        `;

        // 設置通知內容樣式
        const content = notification.querySelector('.notification-content');
        content.style.cssText = `
            display: flex;
            align-items: center;
            gap: 10px;
            justify-content: center;
        `;

        // 設置圖標樣式
        const icon = notification.querySelector('i');
        icon.style.cssText = `
            font-size: 18px;
            opacity: 0.9;
        `;

        // 添加到頁面
        document.body.appendChild(notification);

        // 觸發動畫
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(-50%) translateY(0)';
        }, 10);

        // 自動移除
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(-20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    // ==================== 常用作業庫功能 ====================
    
    // 載入常用作業庫
    function loadFrequentHomework() {
        const savedFrequentHomework = localStorage.getItem('frequentHomework');
        if (savedFrequentHomework) {
            frequentHomework = JSON.parse(savedFrequentHomework);
        } else {
            frequentHomework = [];
        }
        renderFrequentHomeworkItems();
    }
    
    // 保存常用作業庫
    function saveFrequentHomework() {
        localStorage.setItem('frequentHomework', JSON.stringify(frequentHomework));
    }
    
    // 新增常用作業項目
    function addFrequentHomeworkItem() {
        const itemName = frequentHomeworkInput.value.trim();
        
        if (!itemName) {
            alert('請輸入作業項目名稱！');
            return;
        }
        
        if (itemName.length > 50) {
            alert('作業項目名稱不能超過50個字元！');
            return;
        }
        
        // 檢查是否已存在
        if (frequentHomework.includes(itemName)) {
            alert('此作業項目已存在於常用作業庫中！');
            frequentHomeworkInput.value = '';
            return;
        }
        
        // 添加到常用作業庫
        frequentHomework.push(itemName);
        saveFrequentHomework();
        renderFrequentHomeworkItems();
        
        // 清空輸入框
        frequentHomeworkInput.value = '';
        
        // 顯示成功提示
        showTopNotification(`已將「${itemName}」添加到常用作業庫！`, 'success');
    }
    
    // 從常用作業庫刪除項目
    function removeFrequentHomeworkItem(itemName) {
        if (confirm(`確定要從常用作業庫中刪除「${itemName}」嗎？`)) {
            frequentHomework = frequentHomework.filter(item => item !== itemName);
            saveFrequentHomework();
            renderFrequentHomeworkItems();
            showTopNotification(`已從常用作業庫中刪除「${itemName}」！`, 'cancel');
        }
    }
    
    // 將常用作業項目添加到今日作業
    function addFrequentToToday(itemName) {
        // 檢查是否已存在於今日作業中
        if (dailyHomework[today] && dailyHomework[today].includes(itemName)) {
            showTopNotification(`「${itemName}」已存在於今日作業項目中！`, 'cancel');
            return;
        }
        
        // 添加到今日作業
        if (!dailyHomework[today]) {
            dailyHomework[today] = [];
        }
        
        dailyHomework[today].push(itemName);
        saveDailyHomework();
        
        // 更新學生作業記錄結構
        students.forEach(student => {
            if (!student.homeworkSubmission[today][itemName]) {
                student.homeworkSubmission[today][itemName] = {
                    submitted: false,
                    timestamp: null,
                    score: 0
                };
            }
        });
        saveStudents();
        
        // 重新渲染界面
        renderHomeworkItems();
        renderTableHeader();
        updateDisplay();
        
        // 顯示成功提示
        showTopNotification(`已將「${itemName}」添加到今日作業項目！`, 'success');
    }
    
    // 渲染常用作業項目
    function renderFrequentHomeworkItems() {
        if (!frequentHomeworkItems) return;
        
        if (frequentHomework.length === 0) {
            frequentHomeworkItems.innerHTML = `
                <p class="empty-frequent-hint">
                    <i class="fas fa-lightbulb"></i> 
                    添加常用作業項目，下次可快速選用！
                </p>
            `;
            return;
        }
        
        const itemsHTML = frequentHomework.map(item => `
            <div class="frequent-homework-item" data-item="${item}">
                <button class="add-to-today" onclick="addFrequentToToday('${item}')" title="點擊添加到今日作業">
                    <i class="fas fa-plus-circle"></i>
                    ${item}
                </button>
                <button class="remove-btn" onclick="removeFrequentHomeworkItem('${item}')" title="從常用作業庫中刪除">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
        
        frequentHomeworkItems.innerHTML = itemsHTML;
    }

    // 將函數設為全局可用
    window.showTopNotification = showTopNotification;
    window.addFrequentToToday = addFrequentToToday;
    window.removeFrequentHomeworkItem = removeFrequentHomeworkItem;
    window.removeHomeworkItem = removeHomeworkItem;

    // 初始化頁面
    initPage();
});