// 手動加減分功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const studentsGrid = document.getElementById('studentsGrid');
    const searchInput = document.getElementById('searchInput');
    const classFilter = document.getElementById('classFilter');
    const sortBy = document.getElementById('sortBy');
    const pagination = document.getElementById('pagination');
    
    // 快速操作按鈕
    const quickBtns = document.querySelectorAll('.quick-btn');
    const batchModeBtn = document.getElementById('batchModeBtn');
    const historyBtn = document.getElementById('historyBtn');
    
    // 彈窗元素
    const customScoreModal = document.getElementById('customScoreModal');
    const batchModal = document.getElementById('batchModal');
    const historyModal = document.getElementById('historyModal');
    const closeModalBtns = document.querySelectorAll('.close');
    
    // 自訂分數彈窗元素
    const modalStudentInfo = document.getElementById('modalStudentInfo');
    const customScoreForm = document.getElementById('customScoreForm');
    const scoreChange = document.getElementById('scoreChange');
    const scoreReason = document.getElementById('scoreReason');
    const scoreNote = document.getElementById('scoreNote');
    const currentScore = document.getElementById('currentScore');
    const newScore = document.getElementById('newScore');
    const decreaseBtn = document.getElementById('decreaseBtn');
    const increaseBtn = document.getElementById('increaseBtn');
    const cancelCustomBtn = document.getElementById('cancelCustomBtn');
    
    // 批量操作元素
    const batchStudentList = document.getElementById('batchStudentList');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const selectNoneBtn = document.getElementById('selectNoneBtn');
    const selectByClassBtn = document.getElementById('selectByClassBtn');
    const batchScoreChange = document.getElementById('batchScoreChange');
    const batchReason = document.getElementById('batchReason');
    const batchNote = document.getElementById('batchNote');
    const confirmBatchBtn = document.getElementById('confirmBatchBtn');
    const cancelBatchBtn = document.getElementById('cancelBatchBtn');
    
    // 操作記錄元素
    const historyList = document.getElementById('historyList');
    const historyDateFilter = document.getElementById('historyDateFilter');
    const historyStudentFilter = document.getElementById('historyStudentFilter');
    const clearHistoryBtn = document.getElementById('clearHistoryBtn');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');
    
    // 常數設定
    const ITEMS_PER_PAGE = 12;
    
    // 狀態變數
    let students = [];
    let filteredStudents = [];
    let currentPage = 1;
    let selectedStudent = null;
    let selectedStudentIds = new Set();
    let quickScoreMode = false;
    let scoreHistory = [];
    
    // 初始化頁面
    function initPage() {
        loadStudents();
        loadScoreHistory();
        setupEventListeners();
    }
    
    // 設置事件監聽器
    function setupEventListeners() {
        // 搜尋和過濾
        searchInput.addEventListener('input', filterStudents);
        classFilter.addEventListener('change', filterStudents);
        sortBy.addEventListener('change', filterStudents);
        
        // 快速操作按鈕
        quickBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                toggleQuickScoreMode(parseInt(this.dataset.score));
            });
        });
        
        // 功能按鈕
        batchModeBtn.addEventListener('click', showBatchModal);
        historyBtn.addEventListener('click', showHistoryModal);
        
        // 自訂分數彈窗
        decreaseBtn.addEventListener('click', () => adjustScoreInput(-1));
        increaseBtn.addEventListener('click', () => adjustScoreInput(1));
        scoreChange.addEventListener('input', updateScorePreview);
        customScoreForm.addEventListener('submit', handleCustomScoreSubmit);
        cancelCustomBtn.addEventListener('click', closeModal);
        
        // 批量操作
        selectAllBtn.addEventListener('click', selectAllStudents);
        selectNoneBtn.addEventListener('click', selectNoneStudents);
        selectByClassBtn.addEventListener('click', selectByClass);
        confirmBatchBtn.addEventListener('click', handleBatchSubmit);
        cancelBatchBtn.addEventListener('click', closeModal);
        
        // 操作記錄
        historyDateFilter.addEventListener('change', filterHistory);
        historyStudentFilter.addEventListener('change', filterHistory);
        clearHistoryBtn.addEventListener('click', clearHistory);
        
        // 關閉彈窗
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', closeModal);
        });
        
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                closeModal();
            }
        });

        // 匯入匯出功能
        exportBtn.addEventListener('click', exportData);
        importBtn.addEventListener('click', () => importFile.click());
        importFile.addEventListener('change', importData);
    }
    
    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
            
            // 確保每個學生都有分數
            students.forEach(student => {
                if (typeof student.score !== 'number') {
                    student.score = 0;
                }
            });
            
            updateClassFilter();
            updateHistoryStudentFilter();
            filterStudents();
        } else {
            showEmptyState();
        }
    }
    
    // 載入分數記錄
    function loadScoreHistory() {
        const savedHistory = localStorage.getItem('scoreHistory');
        if (savedHistory) {
            scoreHistory = JSON.parse(savedHistory);
        }
    }
    
    // 更新班級過濾器
    function updateClassFilter() {
        const classes = [...new Set(students.map(s => s.class))].sort();
        classFilter.innerHTML = '<option value="">所有班級</option>';
        
        classes.forEach(className => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = className;
            classFilter.appendChild(option);
        });
    }
    
    // 更新操作記錄學生過濾器
    function updateHistoryStudentFilter() {
        historyStudentFilter.innerHTML = '<option value="">所有學生</option>';
        
        students.forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = `${student.number} ${student.name}`;
            historyStudentFilter.appendChild(option);
        });
    }
    
    // 過濾學生
    function filterStudents() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedClass = classFilter.value;
        const sortOption = sortBy.value;
        
        filteredStudents = students.filter(student => {
            const matchesSearch = (
                student.name.toLowerCase().includes(searchTerm) ||
                student.number.toString().includes(searchTerm)
            );
            
            const matchesClass = !selectedClass || student.class === selectedClass;
            
            return matchesSearch && matchesClass;
        });
        
        // 排序
        filteredStudents.sort((a, b) => {
            switch (sortOption) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'score':
                    return (b.score || 0) - (a.score || 0);
                case 'number':
                default:
                    return parseInt(a.number) - parseInt(b.number);
            }
        });
        
        currentPage = 1;
        renderStudentsGrid();
        renderPagination();
    }
    
    // 渲染學生網格
    function renderStudentsGrid() {
        if (!filteredStudents.length) {
            studentsGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                    <p>沒有找到符合條件的學生</p>
                </div>
            `;
            return;
        }
        
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, filteredStudents.length);
        const currentPageStudents = filteredStudents.slice(startIndex, endIndex);
        
        studentsGrid.innerHTML = '';
        
        currentPageStudents.forEach(student => {
            const studentCard = document.createElement('div');
            studentCard.className = `student-card ${quickScoreMode ? 'quick-mode' : ''}`;
            studentCard.dataset.studentId = student.id;
            
            // 檢查特殊狀態
            const specialStates = student.specialStates || {};
            const hasAngel = specialStates.angelProtection;

            studentCard.innerHTML = `
                <div class="student-avatar">
                    <i class="fas fa-user-graduate"></i>
                    ${hasAngel ? '<div class="angel-protection">😇</div>' : ''}
                </div>
                <div class="student-info">
                    <h4>${student.name}</h4>
                    <p>學號：${student.number}</p>
                    <p>班級：${student.class}</p>
                    <div class="score-display">
                        <span class="score-value">${student.score || 0}</span>
                        <span class="score-label">分</span>
                    </div>
                    ${hasAngel ? '<div class="special-status angel-status">😇 天使保護</div>' : ''}
                </div>
                <div class="student-actions">
                    ${quickScoreMode ? `
                        <div class="quick-score-indicator">
                            點擊調整 ${quickScoreMode > 0 ? '+' : ''}${quickScoreMode} 分
                        </div>
                    ` : `
                        <button class="btn btn-sm btn-primary custom-score-btn">
                            <i class="fas fa-edit"></i> 調整分數
                        </button>
                    `}
                </div>
            `;
            
            // 添加點擊事件
            if (quickScoreMode) {
                studentCard.addEventListener('click', () => applyQuickScore(student, quickScoreMode));
            } else {
                const customBtn = studentCard.querySelector('.custom-score-btn');
                customBtn.addEventListener('click', () => showCustomScoreModal(student));
            }
            
            studentsGrid.appendChild(studentCard);
        });
    }
    
    // 切換快速加分模式
    function toggleQuickScoreMode(score) {
        if (quickScoreMode === score) {
            // 取消快速模式
            quickScoreMode = false;
            quickBtns.forEach(btn => btn.classList.remove('active'));
            showNotification('已取消快速加減分模式', 'info');
        } else {
            // 啟用快速模式
            quickScoreMode = score;
            quickBtns.forEach(btn => btn.classList.remove('active'));
            event.target.closest('.quick-btn').classList.add('active');
            showNotification(`快速${score > 0 ? '加' : '減'}分模式已啟用，點擊學生卡片即可調整 ${score > 0 ? '+' : ''}${score} 分`, 'success');
        }
        
        renderStudentsGrid();
    }
    
    // 應用快速加減分
    function applyQuickScore(student, scoreChange) {
        const oldScore = student.score || 0;

        // 檢查是否為扣分且學生有天使保護
        if (scoreChange < 0 && hasAngelProtection(student.id)) {
            // 使用天使保護，免除扣分
            const protectionUsed = useAngelProtection(student.id);
            if (protectionUsed) {
                // 記錄天使保護使用
                addScoreHistory({
                    studentId: student.id,
                    studentName: student.name,
                    oldScore,
                    newScore: oldScore, // 分數不變
                    change: 0,
                    reason: 'angel_protection',
                    note: `天使保護生效：免除 ${scoreChange} 分回頭扣分`,
                    timestamp: new Date().toISOString()
                });

                // 更新顯示
                filterStudents();

                // 顯示天使保護生效通知
                showAngelProtectionNotification(student, Math.abs(scoreChange));
                return;
            }
        }

        const newScore = Math.max(0, oldScore + scoreChange);

        // 更新學生分數
        const studentIndex = students.findIndex(s => s.id === student.id);
        if (studentIndex !== -1) {
            students[studentIndex].score = newScore;
            saveStudents();

            // 記錄操作
            addScoreHistory({
                studentId: student.id,
                studentName: student.name,
                oldScore,
                newScore,
                change: scoreChange,
                reason: 'quick',
                note: `快速${scoreChange > 0 ? '加' : '減'}分`,
                timestamp: new Date().toISOString()
            });

            // 更新顯示
            filterStudents();

            // 顯示通知
            showNotification(`${student.name} 分數已調整：${oldScore} → ${newScore}`, 'success');

            // 如果是加分，檢查是否觸發神祕寶箱
            if (scoreChange > 0 && typeof shouldTriggerMysteryBox === 'function' && shouldTriggerMysteryBox(scoreChange)) {
                setTimeout(() => {
                    showMysteryBoxTrigger(student, scoreChange);
                }, 300);
            }
        }
    }
    
    // 顯示自訂分數彈窗
    function showCustomScoreModal(student) {
        selectedStudent = student;
        
        modalStudentInfo.innerHTML = `
            <div class="student-profile">
                <div class="student-avatar">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="student-details">
                    <h4>${student.name}</h4>
                    <p>學號：${student.number} | 班級：${student.class}</p>
                </div>
            </div>
        `;
        
        scoreChange.value = 0;
        scoreReason.value = '';
        scoreNote.value = '';
        updateScorePreview();
        
        customScoreModal.style.display = 'flex';
    }
    
    // 調整分數輸入
    function adjustScoreInput(delta) {
        const currentValue = parseInt(scoreChange.value) || 0;
        scoreChange.value = currentValue + delta;
        updateScorePreview();
    }
    
    // 更新分數預覽
    function updateScorePreview() {
        if (!selectedStudent) return;
        
        const change = parseInt(scoreChange.value) || 0;
        const current = selectedStudent.score || 0;
        const newScoreValue = Math.max(0, current + change);
        
        currentScore.textContent = current;
        newScore.textContent = newScoreValue;
        
        // 更新顏色
        if (change > 0) {
            newScore.style.color = '#00b894';
        } else if (change < 0) {
            newScore.style.color = '#ff7675';
        } else {
            newScore.style.color = '#333';
        }
    }
    
    // 處理自訂分數提交
    function handleCustomScoreSubmit(e) {
        e.preventDefault();
        
        if (!selectedStudent) return;
        
        const change = parseInt(scoreChange.value) || 0;
        const reason = scoreReason.value;
        const note = scoreNote.value.trim();
        
        if (change === 0) {
            showNotification('請輸入要調整的分數', 'warning');
            return;
        }
        
        if (!reason) {
            showNotification('請選擇調整原因', 'warning');
            return;
        }
        
        const oldScore = selectedStudent.score || 0;

        // 檢查是否為扣分且學生有天使保護
        if (change < 0 && hasAngelProtection(selectedStudent.id)) {
            // 使用天使保護，免除扣分
            const protectionUsed = useAngelProtection(selectedStudent.id);
            if (protectionUsed) {
                // 記錄天使保護使用
                addScoreHistory({
                    studentId: selectedStudent.id,
                    studentName: selectedStudent.name,
                    oldScore,
                    newScore: oldScore, // 分數不變
                    change: 0,
                    reason: 'angel_protection',
                    note: `天使保護生效：免除 ${change} 分回頭扣分 (原因：${getReasonText(reason)})`,
                    timestamp: new Date().toISOString()
                });

                // 更新顯示
                filterStudents();
                closeModal();

                // 顯示天使保護生效通知
                showAngelProtectionNotification(selectedStudent, Math.abs(change));
                return;
            }
        }

        const newScoreValue = Math.max(0, oldScore + change);

        // 更新學生分數
        const studentIndex = students.findIndex(s => s.id === selectedStudent.id);
        if (studentIndex !== -1) {
            students[studentIndex].score = newScoreValue;
            saveStudents();

            // 記錄操作
            addScoreHistory({
                studentId: selectedStudent.id,
                studentName: selectedStudent.name,
                oldScore,
                newScore: newScoreValue,
                change,
                reason,
                note,
                timestamp: new Date().toISOString()
            });

            // 更新顯示
            filterStudents();
            closeModal();

            // 顯示通知
            showNotification(`${selectedStudent.name} 分數已調整：${oldScore} → ${newScoreValue}`, 'success');

            // 如果是加分，檢查是否觸發神祕寶箱
            if (change > 0 && typeof shouldTriggerMysteryBox === 'function' && shouldTriggerMysteryBox(change)) {
                setTimeout(() => {
                    showMysteryBoxTrigger(selectedStudent, change);
                }, 300);
            }
        }
    }
    
    // 顯示批量操作彈窗
    function showBatchModal() {
        updateBatchStudentList();
        batchModal.style.display = 'flex';
    }
    
    // 更新批量學生列表
    function updateBatchStudentList() {
        batchStudentList.innerHTML = '';
        
        students.forEach(student => {
            const studentItem = document.createElement('div');
            studentItem.className = 'batch-student-item';
            studentItem.innerHTML = `
                <label>
                    <input type="checkbox" value="${student.id}">
                    <span>${student.number} ${student.name} (${student.class}班) - ${student.score || 0}分</span>
                </label>
            `;
            batchStudentList.appendChild(studentItem);
        });
    }
    
    // 全選學生
    function selectAllStudents() {
        const checkboxes = batchStudentList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = true);
    }
    
    // 全不選學生
    function selectNoneStudents() {
        const checkboxes = batchStudentList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = false);
    }
    
    // 按班級選擇
    function selectByClass() {
        const selectedClass = prompt('請輸入班級名稱：');
        if (!selectedClass) return;
        
        const checkboxes = batchStudentList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => {
            const student = students.find(s => s.id == cb.value);
            cb.checked = student && student.class === selectedClass;
        });
    }
    
    // 處理批量提交
    function handleBatchSubmit() {
        const selectedIds = Array.from(batchStudentList.querySelectorAll('input[type="checkbox"]:checked'))
            .map(cb => parseInt(cb.value));
        
        if (selectedIds.length === 0) {
            showNotification('請至少選擇一位學生', 'warning');
            return;
        }
        
        const change = parseInt(batchScoreChange.value) || 0;
        const reason = batchReason.value;
        const note = batchNote.value.trim();
        
        if (change === 0) {
            showNotification('請輸入要調整的分數', 'warning');
            return;
        }
        
        if (!reason) {
            showNotification('請選擇調整原因', 'warning');
            return;
        }
        
        if (!confirm(`確定要為 ${selectedIds.length} 位學生調整 ${change > 0 ? '+' : ''}${change} 分嗎？`)) {
            return;
        }
        
        // 批量更新分數
        let updatedCount = 0;
        let firstAddScoreStudent = null;

        selectedIds.forEach(studentId => {
            const studentIndex = students.findIndex(s => s.id === studentId);
            if (studentIndex !== -1) {
                const student = students[studentIndex];
                const oldScore = student.score || 0;

                // 檢查是否為扣分且學生有天使保護
                if (change < 0 && hasAngelProtection(student.id)) {
                    // 使用天使保護，免除扣分
                    const protectionUsed = useAngelProtection(student.id);
                    if (protectionUsed) {
                        // 記錄天使保護使用
                        addScoreHistory({
                            studentId: student.id,
                            studentName: student.name,
                            oldScore,
                            newScore: oldScore, // 分數不變
                            change: 0,
                            reason: 'angel_protection',
                            note: `批量操作中天使保護生效：免除 ${change} 分回頭扣分`,
                            timestamp: new Date().toISOString()
                        });

                        updatedCount++;
                        return; // 跳過正常的分數更新
                    }
                }

                const newScoreValue = Math.max(0, oldScore + change);
                students[studentIndex].score = newScoreValue;

                // 記錄操作
                addScoreHistory({
                    studentId: student.id,
                    studentName: student.name,
                    oldScore,
                    newScore: newScoreValue,
                    change,
                    reason,
                    note: `批量操作：${note}`,
                    timestamp: new Date().toISOString()
                });

                // 記錄第一個加分的學生，用於觸發神祕寶箱
                if (change > 0 && !firstAddScoreStudent) {
                    firstAddScoreStudent = student;
                }

                updatedCount++;
            }
        });

        if (updatedCount > 0) {
            saveStudents();
            filterStudents();
            closeModal();
            showNotification(`已為 ${updatedCount} 位學生調整分數`, 'success');

            // 如果是加分且有學生，檢查是否觸發神祕寶箱（只為第一個學生觸發）
            if (firstAddScoreStudent && change > 0 && typeof shouldTriggerMysteryBox === 'function' && shouldTriggerMysteryBox(change, true)) {
                setTimeout(() => {
                    showMysteryBoxTrigger(firstAddScoreStudent, change);
                }, 500); // 延遲一點時間讓通知先顯示
            }
        }
    }
    
    // 顯示操作記錄彈窗
    function showHistoryModal() {
        renderHistory();
        historyModal.style.display = 'flex';
    }
    
    // 渲染操作記錄
    function renderHistory() {
        const filteredHistory = filterHistoryData();
        
        if (filteredHistory.length === 0) {
            historyList.innerHTML = '<div class="empty-history">暫無操作記錄</div>';
            return;
        }
        
        historyList.innerHTML = '';
        
        filteredHistory.forEach(record => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            const date = new Date(record.timestamp);
            const changeClass = record.change > 0 ? 'positive' : 'negative';
            
            historyItem.innerHTML = `
                <div class="history-header">
                    <span class="student-name">${record.studentName}</span>
                    <span class="history-date">${date.toLocaleString('zh-TW')}</span>
                </div>
                <div class="history-details">
                    <span class="score-change ${changeClass}">
                        ${record.oldScore} → ${record.newScore} (${record.change > 0 ? '+' : ''}${record.change})
                    </span>
                    <span class="reason">${getReasonText(record.reason)}</span>
                </div>
                ${record.note ? `<div class="history-note">${record.note}</div>` : ''}
            `;
            
            historyList.appendChild(historyItem);
        });
    }
    
    // 過濾操作記錄
    function filterHistoryData() {
        let filtered = [...scoreHistory];
        
        const dateFilter = historyDateFilter.value;
        const studentFilter = historyStudentFilter.value;
        
        if (dateFilter) {
            const filterDate = new Date(dateFilter).toDateString();
            filtered = filtered.filter(record => {
                const recordDate = new Date(record.timestamp).toDateString();
                return recordDate === filterDate;
            });
        }
        
        if (studentFilter) {
            filtered = filtered.filter(record => record.studentId == studentFilter);
        }
        
        return filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }
    
    // 過濾操作記錄
    function filterHistory() {
        renderHistory();
    }
    
    // 清除操作記錄
    function clearHistory() {
        if (confirm('確定要清除所有操作記錄嗎？此操作無法復原。')) {
            scoreHistory = [];
            localStorage.removeItem('scoreHistory');
            renderHistory();
            showNotification('操作記錄已清除', 'success');
        }
    }
    
    // 添加分數記錄
    function addScoreHistory(record) {
        scoreHistory.push(record);
        localStorage.setItem('scoreHistory', JSON.stringify(scoreHistory));
    }
    
    // 獲取原因文字
    function getReasonText(reason) {
        const reasons = {
            'homework': '作業表現',
            'behavior': '行為表現',
            'participation': '課堂參與',
            'test': '測驗成績',
            'activity': '活動參與',
            'quick': '快速調整',
            'mystery_box': '神祕寶箱',
            'angel_protection': '天使保護',
            'other': '其他'
        };
        return reasons[reason] || reason;
    }

    // ==================== 神祕寶箱功能 ====================

    // 神祕寶箱事件類型
    const MYSTERY_BOX_EVENTS = {
        LUCKY: {
            id: 'lucky',
            name: '強運',
            icon: '🍀',
            description: '當前獲得的分數翻倍',
            color: '#00b894'
        },
        STEAL: {
            id: 'steal',
            name: '掠奪',
            icon: '💰',
            description: '從其他隨機學生身上偷取5分',
            color: '#fdcb6e'
        },
        ANGEL: {
            id: 'angel',
            name: '天使',
            icon: '😇',
            description: '獲得一次「回頭免扣分」保護',
            color: '#74b9ff'
        },
        DEVIL: {
            id: 'devil',
            name: '惡魔',
            icon: '😈',
            description: '當前總分數減半',
            color: '#ff7675'
        },
        SWAP: {
            id: 'swap',
            name: '交換',
            icon: '🔄',
            description: '與隨機學生交換總分數',
            color: '#a29bfe'
        },
        CIRCUS: {
            id: 'circus',
            name: '馬戲團',
            icon: '🎪',
            description: '所有學生的分數都增加1分',
            color: '#ff6b9d'
        },
        METEOR: {
            id: 'meteor',
            name: '流星雨',
            icon: '🌟',
            description: '幫自己以外的一位同學加1分',
            color: '#2c3e50'
        },
        TRANSFORM: {
            id: 'transform',
            name: '變身',
            icon: '🎭',
            description: '一半機率變成最高分或最低分',
            color: '#9b59b6'
        }
    };

    // 顯示神祕寶箱觸發器
    function showMysteryBoxTrigger(student, originalChange) {
        try {
            console.log('觸發神祕寶箱:', student.name, originalChange);

            // 創建神祕寶箱彈窗
            const mysteryModal = document.createElement('div');
            mysteryModal.className = 'modal mystery-box-modal';
            mysteryModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 2000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            mysteryModal.innerHTML = `
                <div class="modal-content mystery-box-content">
                    <div class="mystery-box-header">
                        <h3>🎁 神祕寶箱出現了！</h3>
                        <p>${student.name} 獲得了 +${originalChange} 分，觸發了神祕事件！</p>
                    </div>
                    <div class="mystery-box-animation">
                        <div class="treasure-box">
                            <div class="box-lid"></div>
                            <div class="box-body"></div>
                            <div class="sparkles"></div>
                        </div>
                    </div>
                    <div class="mystery-box-actions">
                        <button class="btn btn-primary mystery-open-btn" style="
                            background: linear-gradient(135deg, #00b894, #00a085);
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 25px;
                            font-weight: 600;
                            cursor: pointer;
                            margin: 0 10px;
                            animation: pulse 2s infinite;
                        ">
                            <i class="fas fa-gift"></i> 開啟寶箱
                        </button>
                        <button class="btn btn-secondary mystery-skip-btn" style="
                            background: rgba(255, 255, 255, 0.2);
                            color: white;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            padding: 10px 20px;
                            border-radius: 25px;
                            cursor: pointer;
                            margin: 0 10px;
                        ">
                            <i class="fas fa-times"></i> 跳過
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(mysteryModal);

            // 顯示彈窗
            setTimeout(() => {
                mysteryModal.style.opacity = '1';
            }, 10);

            // 綁定事件
            const openBtn = mysteryModal.querySelector('.mystery-open-btn');
            const skipBtn = mysteryModal.querySelector('.mystery-skip-btn');

            if (openBtn) {
                openBtn.addEventListener('click', () => {
                    console.log('點擊開啟寶箱');
                    triggerMysteryBoxEvent(student, originalChange, mysteryModal);
                });
            } else {
                console.error('找不到開啟按鈕');
            }

            if (skipBtn) {
                skipBtn.addEventListener('click', () => {
                    console.log('點擊跳過');
                    closeMysteryBoxModal(mysteryModal);
                });
            } else {
                console.error('找不到跳過按鈕');
            }

            // 點擊背景關閉
            mysteryModal.addEventListener('click', (e) => {
                if (e.target === mysteryModal) {
                    closeMysteryBoxModal(mysteryModal);
                }
            });

        } catch (error) {
            console.error('神祕寶箱顯示錯誤:', error);
            alert('神祕寶箱功能發生錯誤，請重試。');
        }
    }

    // 觸發神祕寶箱事件
    function triggerMysteryBoxEvent(student, originalChange, modal) {
        // 根據配置選擇事件
        let selectedEventId;
        if (typeof selectEventByProbability === 'function') {
            selectedEventId = selectEventByProbability();
        } else {
            // 備用方案：隨機選擇
            const events = Object.keys(MYSTERY_BOX_EVENTS);
            selectedEventId = events[Math.floor(Math.random() * events.length)].toLowerCase();
        }

        const randomEvent = MYSTERY_BOX_EVENTS[selectedEventId.toUpperCase()] || MYSTERY_BOX_EVENTS.LUCKY;

        // 播放開箱動畫
        playOpenBoxAnimation(modal, () => {
            // 執行事件效果
            executeMysteryBoxEvent(student, originalChange, randomEvent, modal);
        });
    }

    // 播放開箱動畫
    function playOpenBoxAnimation(modal, callback) {
        const box = modal.querySelector('.treasure-box');
        const header = modal.querySelector('.mystery-box-header');
        const actions = modal.querySelector('.mystery-box-actions');

        // 隱藏按鈕
        actions.style.display = 'none';

        // 更新標題
        header.innerHTML = '<h3>🎁 正在開啟寶箱...</h3>';

        // 添加開箱動畫
        box.classList.add('opening');

        // 播放音效（如果需要）
        if (typeof playMysteryBoxSound === 'function') {
            playMysteryBoxSound('boxOpen');
        } else {
            playSound('box-open');
        }

        // 動畫完成後執行回調
        setTimeout(callback, 2000);
    }

    // 執行神祕寶箱事件
    function executeMysteryBoxEvent(student, originalChange, event, modal) {
        try {
            console.log('執行神祕寶箱事件:', event.name, '學生:', student.name, '原始分數變化:', originalChange);

            let resultMessage = '';
            let affectedStudents = [];

            const studentIndex = students.findIndex(s => s.id === student.id);
            if (studentIndex === -1) {
                console.error('找不到學生:', student.name);
                return;
            }

        switch (event.id) {
            case 'lucky':
                // 強運：分數翻倍
                const bonusScore = originalChange;
                students[studentIndex].score += bonusScore;
                resultMessage = `🍀 強運降臨！額外獲得 +${bonusScore} 分！`;

                addScoreHistory({
                    studentId: student.id,
                    studentName: student.name,
                    oldScore: student.score,
                    newScore: students[studentIndex].score,
                    change: bonusScore,
                    reason: 'mystery_box',
                    note: `神祕寶箱 - 強運：分數翻倍`,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'steal':
                // 掠奪：從其他學生偷取5分
                const otherStudents = students.filter(s => s.id !== student.id && s.score >= 5);
                if (otherStudents.length > 0) {
                    const targetStudent = otherStudents[Math.floor(Math.random() * otherStudents.length)];
                    const targetIndex = students.findIndex(s => s.id === targetStudent.id);

                    students[targetIndex].score -= 5;
                    students[studentIndex].score += 5;

                    resultMessage = `💰 掠奪成功！從 ${targetStudent.name} 身上偷取了 5 分！`;
                    affectedStudents.push(targetStudent.name);

                    // 記錄被偷取者的歷史
                    addScoreHistory({
                        studentId: targetStudent.id,
                        studentName: targetStudent.name,
                        oldScore: targetStudent.score + 5,
                        newScore: targetStudent.score,
                        change: -5,
                        reason: 'mystery_box',
                        note: `神祕寶箱 - 被 ${student.name} 掠奪`,
                        timestamp: new Date().toISOString()
                    });

                    // 記錄掠奪者的歷史
                    addScoreHistory({
                        studentId: student.id,
                        studentName: student.name,
                        oldScore: student.score,
                        newScore: students[studentIndex].score,
                        change: 5,
                        reason: 'mystery_box',
                        note: `神祕寶箱 - 掠奪：從 ${targetStudent.name} 偷取`,
                        timestamp: new Date().toISOString()
                    });
                } else {
                    resultMessage = `💰 掠奪失敗！沒有其他學生有足夠的分數可以偷取。`;
                }
                break;

            case 'angel':
                // 天使：獲得保護狀態
                if (!students[studentIndex].specialStates) {
                    students[studentIndex].specialStates = {};
                }
                students[studentIndex].specialStates.angelProtection = true;
                resultMessage = `😇 天使庇佑！獲得一次「回頭免扣分」保護！`;

                addScoreHistory({
                    studentId: student.id,
                    studentName: student.name,
                    oldScore: student.score,
                    newScore: student.score,
                    change: 0,
                    reason: 'mystery_box',
                    note: `神祕寶箱 - 天使：獲得回頭免扣分保護`,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'devil':
                // 惡魔：分數減半
                const oldScore = students[studentIndex].score;
                const newScore = Math.floor(oldScore / 2);
                students[studentIndex].score = newScore;
                resultMessage = `😈 惡魔詛咒！分數減半：${oldScore} → ${newScore}`;

                addScoreHistory({
                    studentId: student.id,
                    studentName: student.name,
                    oldScore: oldScore,
                    newScore: newScore,
                    change: newScore - oldScore,
                    reason: 'mystery_box',
                    note: `神祕寶箱 - 惡魔：分數減半`,
                    timestamp: new Date().toISOString()
                });
                break;

            case 'swap':
                // 交換：讓學生選擇交換對象
                const swapCandidates = students.filter(s => s.id !== student.id);
                if (swapCandidates.length > 0) {
                    // 顯示交換選擇界面，而不是立即執行
                    showSwapSelectionModal(student, originalChange, modal, swapCandidates);
                    return; // 不繼續執行，等待用戶選擇
                } else {
                    resultMessage = `🔄 交換失敗！沒有其他學生可以交換分數。`;
                }
                break;

            case 'circus':
                // 馬戲團：所有學生分數都增加1分
                let circusAffectedCount = 0;
                students.forEach((s, index) => {
                    students[index].score += 1;
                    circusAffectedCount++;

                    // 為每個學生記錄歷史
                    addScoreHistory({
                        studentId: s.id,
                        studentName: s.name,
                        oldScore: s.score - 1,
                        newScore: s.score,
                        change: 1,
                        reason: 'mystery_box',
                        note: `神祕寶箱 - 馬戲團：全體受益`,
                        timestamp: new Date().toISOString()
                    });
                });

                resultMessage = `🎪 馬戲團表演！所有 ${circusAffectedCount} 位學生都獲得了 +1 分！`;
                affectedStudents = students.map(s => s.name);
                break;

            case 'meteor':
                // 流星雨：幫自己以外的一位同學加1分
                const meteorCandidates = students.filter(s => s.id !== student.id);
                if (meteorCandidates.length > 0) {
                    // 顯示選擇界面，讓用戶選擇要幫助的同學
                    showMeteorSelectionModal(student, originalChange, modal, meteorCandidates);
                    return; // 不繼續執行，等待用戶選擇
                } else {
                    resultMessage = `🌟 流星雨失效！沒有其他同學可以幫助。`;
                }
                break;

            case 'transform':
                // 變身：一半機率變成最高分或最低分
                if (students.length <= 1) {
                    resultMessage = `🎭 變身失效！需要至少2位學生才能變身。`;
                } else {
                    // 顯示變身選擇界面
                    showTransformSelectionModal(student, originalChange, modal);
                    return; // 不繼續執行，等待用戶選擇
                }
                break;
        }

            // 保存更新
            saveStudents();

            console.log('事件執行完成，結果訊息:', resultMessage);

            // 顯示結果
            showMysteryBoxResult(event, resultMessage, affectedStudents, modal);

        } catch (error) {
            console.error('執行神祕寶箱事件錯誤:', error);
            alert('神祕寶箱事件執行失敗，請重試。');
            closeMysteryBoxModal(modal);
        }
    }

    // 顯示神祕寶箱結果
    function showMysteryBoxResult(event, message, affectedStudents, modal) {
        try {
            console.log('顯示神祕寶箱結果:', event.name, message);

            const content = modal.querySelector('.modal-content');

            content.innerHTML = `
                <div class="mystery-box-result" style="
                    padding: 20px;
                    text-align: center;
                    opacity: 0;
                    transform: translateY(20px);
                    transition: all 0.5s ease;
                ">
                    <div class="event-icon" style="
                        font-size: 4rem;
                        margin-bottom: 15px;
                        color: ${event.color};
                        animation: bounceIn 0.8s ease;
                    ">
                        <span class="event-emoji">${event.icon}</span>
                    </div>
                    <h3 class="event-title" style="
                        font-size: 1.8rem;
                        margin-bottom: 10px;
                        color: ${event.color};
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                    ">${event.name}</h3>
                    <p class="event-description" style="
                        font-size: 1rem;
                        margin-bottom: 20px;
                        opacity: 0.9;
                    ">${event.description}</p>
                    <div class="result-message" style="
                        background: rgba(255, 255, 255, 0.2);
                        padding: 15px;
                        border-radius: 10px;
                        margin: 20px 0;
                        font-size: 1.1rem;
                        font-weight: 600;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                    ">
                        ${message}
                    </div>
                    ${affectedStudents.length > 0 ? `
                        <div class="affected-students" style="
                            margin-top: 10px;
                            font-style: italic;
                            opacity: 0.8;
                        ">
                            <small>影響的其他學生：${affectedStudents.join(', ')}</small>
                        </div>
                    ` : ''}
                    <div class="result-actions" style="margin-top: 25px;">
                        <button class="close-result-btn" style="
                            background: rgba(255, 255, 255, 0.9);
                            color: #333;
                            border: none;
                            padding: 12px 30px;
                            border-radius: 25px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-check"></i> 確定
                        </button>
                    </div>
                </div>
            `;

            // 播放結果音效
            if (typeof playMysteryBoxSound === 'function') {
                playMysteryBoxSound('result');
            } else {
                playSound('result');
            }

            // 綁定關閉事件
            const closeBtn = content.querySelector('.close-result-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    console.log('點擊確定按鈕');
                    closeMysteryBoxModal(modal);
                    // 更新顯示
                    if (typeof filterStudents === 'function') {
                        filterStudents();
                    }
                });

                // 添加懸停效果
                closeBtn.addEventListener('mouseenter', () => {
                    closeBtn.style.background = 'white';
                    closeBtn.style.transform = 'translateY(-2px)';
                    closeBtn.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                });

                closeBtn.addEventListener('mouseleave', () => {
                    closeBtn.style.background = 'rgba(255, 255, 255, 0.9)';
                    closeBtn.style.transform = 'translateY(0)';
                    closeBtn.style.boxShadow = 'none';
                });
            } else {
                console.error('找不到確定按鈕');
            }

            // 添加結果動畫
            setTimeout(() => {
                const resultDiv = content.querySelector('.mystery-box-result');
                if (resultDiv) {
                    resultDiv.style.opacity = '1';
                    resultDiv.style.transform = 'translateY(0)';
                }
            }, 100);

            console.log('神祕寶箱結果顯示完成');

        } catch (error) {
            console.error('顯示結果錯誤:', error);
            // 備用方案：簡單的alert
            alert(`神祕寶箱結果：${event.name}\n${message}`);
            closeMysteryBoxModal(modal);
        }
    }

    // 關閉神祕寶箱彈窗
    function closeMysteryBoxModal(modal) {
        try {
            console.log('關閉神祕寶箱彈窗');
            modal.style.opacity = '0';
            setTimeout(() => {
                if (modal && modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        } catch (error) {
            console.error('關閉彈窗錯誤:', error);
            // 強制移除
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }
    }

    // 顯示流星雨選擇界面
    function showMeteorSelectionModal(student, originalChange, modal, candidates) {
        const content = modal.querySelector('.modal-content');

        content.innerHTML = `
            <div class="meteor-selection" style="
                padding: 20px;
                text-align: center;
                background: linear-gradient(135deg, #2c3e50, #34495e);
                border-radius: 15px;
                color: white;
            ">
                <div class="meteor-header" style="margin-bottom: 20px;">
                    <div style="font-size: 3rem; margin-bottom: 10px;">🌟</div>
                    <h3 style="color: #ecf0f1; margin-bottom: 10px;">流星雨降臨</h3>
                    <p style="opacity: 0.9;">選擇一位同學，為他/她增加 1 分！</p>
                </div>

                <div class="candidate-list" style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 15px;
                    margin: 20px 0;
                    max-height: 300px;
                    overflow-y: auto;
                ">
                    ${candidates.map(candidate => `
                        <button class="candidate-btn" data-student-id="${candidate.id}" style="
                            background: rgba(255, 255, 255, 0.1);
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            border-radius: 10px;
                            padding: 15px;
                            color: white;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            text-align: center;
                        ">
                            <div style="font-size: 1.2rem; font-weight: 600;">${candidate.name}</div>
                            <div style="opacity: 0.8; margin-top: 5px;">目前分數: ${candidate.score}</div>
                        </button>
                    `).join('')}
                </div>

                <div class="meteor-actions" style="margin-top: 20px;">
                    <button class="cancel-meteor-btn" style="
                        background: rgba(255, 255, 255, 0.2);
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        padding: 10px 20px;
                        border-radius: 25px;
                        cursor: pointer;
                    ">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        `;

        // 綁定候選人按鈕事件
        const candidateBtns = content.querySelectorAll('.candidate-btn');
        candidateBtns.forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.background = 'rgba(255, 255, 255, 0.2)';
                btn.style.transform = 'translateY(-2px)';
                btn.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
            });

            btn.addEventListener('mouseleave', () => {
                btn.style.background = 'rgba(255, 255, 255, 0.1)';
                btn.style.transform = 'translateY(0)';
                btn.style.boxShadow = 'none';
            });

            btn.addEventListener('click', () => {
                const selectedStudentId = parseInt(btn.dataset.studentId);
                executeMeteorEvent(student, originalChange, selectedStudentId, modal);
            });
        });

        // 綁定取消按鈕
        const cancelBtn = content.querySelector('.cancel-meteor-btn');
        cancelBtn.addEventListener('click', () => {
            closeMysteryBoxModal(modal);
        });
    }

    // 執行流星雨事件
    function executeMeteorEvent(triggerStudent, originalChange, targetStudentId, modal) {
        const targetIndex = students.findIndex(s => s.id === targetStudentId);
        const targetStudent = students[targetIndex];

        if (targetIndex !== -1) {
            // 為目標學生加1分
            students[targetIndex].score += 1;

            // 記錄歷史
            addScoreHistory({
                studentId: targetStudent.id,
                studentName: targetStudent.name,
                oldScore: targetStudent.score - 1,
                newScore: targetStudent.score,
                change: 1,
                reason: 'mystery_box',
                note: `神祕寶箱 - 流星雨：由 ${triggerStudent.name} 贈送`,
                timestamp: new Date().toISOString()
            });

            // 保存更新
            saveStudents();

            // 顯示結果
            const event = MYSTERY_BOX_EVENTS.METEOR;
            const resultMessage = `🌟 流星雨閃耀！${targetStudent.name} 獲得了 +1 分！`;
            showMysteryBoxResult(event, resultMessage, [targetStudent.name], modal);
        }
    }

    // 顯示變身選擇界面
    function showTransformSelectionModal(student, originalChange, modal) {
        // 計算最高分和最低分
        const sortedStudents = [...students].sort((a, b) => (b.score || 0) - (a.score || 0));
        const highestScore = sortedStudents[0].score || 0;
        const lowestScore = sortedStudents[sortedStudents.length - 1].score || 0;

        // 隨機決定是最高分還是最低分
        const isHighScore = Math.random() < 0.5;
        const targetScore = isHighScore ? highestScore : lowestScore;
        const targetType = isHighScore ? '最高分' : '最低分';
        const currentScore = student.score || 0;

        const content = modal.querySelector('.modal-content');

        content.innerHTML = `
            <div class="transform-selection" style="
                padding: 20px;
                text-align: center;
                background: linear-gradient(135deg, #9b59b6, #8e44ad);
                border-radius: 15px;
                color: white;
            ">
                <div class="transform-header" style="margin-bottom: 20px;">
                    <div style="font-size: 3rem; margin-bottom: 10px;">🎭</div>
                    <h3 style="color: #ecf0f1; margin-bottom: 10px;">變身魔法</h3>
                    <p style="opacity: 0.9;">命運之輪已經轉動...</p>
                </div>

                <div class="transform-info" style="
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                    padding: 20px;
                    margin: 20px 0;
                ">
                    <div style="font-size: 1.2rem; margin-bottom: 15px;">
                        <strong>${student.name}</strong> 的變身預告
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 15px; align-items: center;">
                        <div style="text-align: center;">
                            <div style="font-size: 0.9rem; opacity: 0.8;">目前分數</div>
                            <div style="font-size: 1.5rem; font-weight: bold; color: #f39c12;">
                                ${currentScore} 分
                            </div>
                        </div>

                        <div style="font-size: 2rem;">
                            ➡️
                        </div>

                        <div style="text-align: center;">
                            <div style="font-size: 0.9rem; opacity: 0.8;">變身後分數</div>
                            <div style="font-size: 1.5rem; font-weight: bold; color: ${isHighScore ? '#27ae60' : '#e74c3c'};">
                                ${targetScore} 分
                            </div>
                            <div style="font-size: 0.8rem; margin-top: 5px; opacity: 0.9;">
                                (班上${targetType})
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 15px; padding: 10px; background: rgba(255, 255, 255, 0.1); border-radius: 5px;">
                        <div style="font-size: 0.9rem;">
                            分數變化: ${targetScore > currentScore ? '+' : ''}${targetScore - currentScore} 分
                        </div>
                    </div>
                </div>

                <div class="transform-actions" style="display: flex; gap: 15px; justify-content: center; margin-top: 20px;">
                    <button class="accept-transform-btn" style="
                        background: linear-gradient(135deg, #27ae60, #2ecc71);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">
                        <i class="fas fa-magic"></i> 接受變身
                    </button>

                    <button class="cancel-transform-btn" style="
                        background: rgba(255, 255, 255, 0.2);
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        padding: 12px 24px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: bold;
                        transition: all 0.3s ease;
                    ">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>

                <div style="margin-top: 15px; font-size: 0.8rem; opacity: 0.7;">
                    💡 提示：變身是不可逆的，請謹慎選擇！
                </div>
            </div>
        `;

        // 綁定按鈕事件
        const acceptBtn = content.querySelector('.accept-transform-btn');
        const cancelBtn = content.querySelector('.cancel-transform-btn');

        // 按鈕懸停效果
        acceptBtn.addEventListener('mouseenter', () => {
            acceptBtn.style.transform = 'translateY(-2px)';
            acceptBtn.style.boxShadow = '0 4px 12px rgba(39, 174, 96, 0.4)';
        });

        acceptBtn.addEventListener('mouseleave', () => {
            acceptBtn.style.transform = 'translateY(0)';
            acceptBtn.style.boxShadow = 'none';
        });

        cancelBtn.addEventListener('mouseenter', () => {
            cancelBtn.style.background = 'rgba(255, 255, 255, 0.3)';
            cancelBtn.style.transform = 'translateY(-2px)';
        });

        cancelBtn.addEventListener('mouseleave', () => {
            cancelBtn.style.background = 'rgba(255, 255, 255, 0.2)';
            cancelBtn.style.transform = 'translateY(0)';
        });

        // 接受變身
        acceptBtn.addEventListener('click', () => {
            executeTransformEvent(student, originalChange, targetScore, targetType, modal);
        });

        // 取消變身
        cancelBtn.addEventListener('click', () => {
            closeMysteryBoxModal(modal);
        });
    }

    // 執行變身事件
    function executeTransformEvent(student, originalChange, targetScore, targetType, modal) {
        const studentIndex = students.findIndex(s => s.id === student.id);

        if (studentIndex !== -1) {
            const oldScore = students[studentIndex].score || 0;
            const scoreChange = targetScore - oldScore;

            // 更新學生分數
            students[studentIndex].score = targetScore;

            // 記錄歷史
            addScoreHistory({
                studentId: student.id,
                studentName: student.name,
                oldScore: oldScore,
                newScore: targetScore,
                change: scoreChange,
                reason: 'mystery_box',
                note: `神祕寶箱 - 變身：變成班上${targetType} (${targetScore}分)`,
                timestamp: new Date().toISOString()
            });

            // 保存更新
            saveStudents();

            // 顯示結果
            const event = MYSTERY_BOX_EVENTS.TRANSFORM;
            let resultMessage;

            if (scoreChange > 0) {
                resultMessage = `🎭 變身成功！${student.name} 變身為班上${targetType}，獲得 +${scoreChange} 分！`;
            } else if (scoreChange < 0) {
                resultMessage = `🎭 變身完成！${student.name} 變身為班上${targetType}，失去 ${Math.abs(scoreChange)} 分！`;
            } else {
                resultMessage = `🎭 變身奇蹟！${student.name} 變身後分數竟然沒有改變！`;
            }

            showMysteryBoxResult(event, resultMessage, [student.name], modal);
        }
    }

    // 播放音效
    function playSound(type) {
        // 這裡可以添加音效播放邏輯
        // 例如使用 Web Audio API 或 HTML5 Audio
        try {
            const audio = new Audio();
            switch (type) {
                case 'box-open':
                    // 可以添加開箱音效
                    break;
                case 'result':
                    // 可以添加結果音效
                    break;
            }
        } catch (error) {
            // 音效播放失敗時不影響功能
            console.log('音效播放失敗:', error);
        }
    }

    // 檢查學生是否有天使保護
    function hasAngelProtection(studentId) {
        const student = students.find(s => s.id === studentId);
        return student && student.specialStates && student.specialStates.angelProtection;
    }

    // 使用天使保護
    function useAngelProtection(studentId) {
        const studentIndex = students.findIndex(s => s.id === studentId);
        if (studentIndex !== -1 && students[studentIndex].specialStates && students[studentIndex].specialStates.angelProtection) {
            console.log('使用天使保護:', students[studentIndex].name);

            delete students[studentIndex].specialStates.angelProtection;

            // 如果沒有其他特殊狀態，刪除整個 specialStates 對象
            if (Object.keys(students[studentIndex].specialStates).length === 0) {
                delete students[studentIndex].specialStates;
            }

            saveStudents();
            return true;
        }
        return false;
    }

    // 顯示天使保護生效通知
    function showAngelProtectionNotification(student, protectedScore) {
        // 創建特殊的天使保護通知彈窗
        const protectionModal = document.createElement('div');
        protectionModal.className = 'modal angel-protection-modal';
        protectionModal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        protectionModal.innerHTML = `
            <div style="
                max-width: 450px;
                text-align: center;
                background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
                color: white;
                border-radius: 15px;
                padding: 30px;
                position: relative;
                overflow: hidden;
                box-shadow: 0 0 30px rgba(116, 185, 255, 0.5);
            ">
                <div style="
                    position: absolute;
                    top: -50%;
                    left: -50%;
                    width: 200%;
                    height: 200%;
                    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                    animation: angelGlow 3s ease-in-out infinite;
                "></div>

                <div style="position: relative; z-index: 1;">
                    <div style="
                        font-size: 4rem;
                        margin-bottom: 15px;
                        animation: angelFloat 2s ease-in-out infinite;
                    ">😇</div>

                    <h3 style="
                        font-size: 1.8rem;
                        margin-bottom: 10px;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                    ">天使保護生效！</h3>

                    <p style="
                        font-size: 1.1rem;
                        margin-bottom: 20px;
                        opacity: 0.9;
                    ">${student.name} 的天使保護啟動</p>

                    <div style="
                        background: rgba(255, 255, 255, 0.2);
                        padding: 15px;
                        border-radius: 10px;
                        margin: 20px 0;
                        font-size: 1.1rem;
                        font-weight: 600;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                    ">
                        免除了 ${protectedScore} 分的回頭扣分！
                    </div>

                    <p style="
                        font-size: 0.9rem;
                        opacity: 0.8;
                        margin-bottom: 20px;
                    ">天使保護已使用完畢</p>

                    <button onclick="closeAngelProtectionModal(this.closest('.modal'))" style="
                        background: rgba(255, 255, 255, 0.9);
                        color: #333;
                        border: none;
                        padding: 12px 30px;
                        border-radius: 25px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    ">
                        <i class="fas fa-check"></i> 確定
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(protectionModal);

        // 顯示彈窗
        setTimeout(() => {
            protectionModal.style.opacity = '1';
        }, 10);

        // 添加動畫樣式
        if (!document.getElementById('angel-protection-styles')) {
            const style = document.createElement('style');
            style.id = 'angel-protection-styles';
            style.textContent = `
                @keyframes angelFloat {
                    0%, 100% { transform: translateY(0px); }
                    50% { transform: translateY(-10px); }
                }

                @keyframes angelGlow {
                    0%, 100% { opacity: 0.3; transform: rotate(0deg); }
                    50% { opacity: 0.6; transform: rotate(180deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // 3秒後自動關閉
        setTimeout(() => {
            closeAngelProtectionModal(protectionModal);
        }, 3000);
    }

    // 關閉天使保護通知
    window.closeAngelProtectionModal = function(modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    };

    // 顯示交換選擇界面
    function showSwapSelectionModal(student, originalChange, parentModal, swapCandidates) {
        try {
            console.log('顯示交換選擇界面:', student.name, '可選對象:', swapCandidates.length);

            const content = parentModal.querySelector('.modal-content');

            content.innerHTML = `
                <div class="swap-selection-content" style="
                    padding: 20px;
                    text-align: center;
                    opacity: 0;
                    transform: translateY(20px);
                    transition: all 0.5s ease;
                ">
                    <div style="
                        font-size: 3rem;
                        margin-bottom: 15px;
                        color: #a29bfe;
                    ">🔄</div>

                    <h3 style="
                        font-size: 1.8rem;
                        margin-bottom: 10px;
                        color: #a29bfe;
                        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                    ">分數交換</h3>

                    <p style="
                        font-size: 1rem;
                        margin-bottom: 20px;
                        opacity: 0.9;
                    ">${student.name} 可以選擇與其他同學交換分數</p>

                    <div style="
                        background: rgba(255, 255, 255, 0.2);
                        padding: 15px;
                        border-radius: 10px;
                        margin: 20px 0;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                    ">
                        <div style="font-weight: 600; margin-bottom: 10px;">
                            ${student.name} 目前分數：${student.score} 分
                        </div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">
                            選擇一位同學進行分數交換
                        </div>
                    </div>

                    <div class="swap-candidates" style="
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                        gap: 15px;
                        margin: 20px 0;
                        max-height: 300px;
                        overflow-y: auto;
                    ">
                        ${swapCandidates.map(candidate => `
                            <div class="swap-candidate-card"
                                 onclick="executeSwap('${student.id}', '${candidate.id}', ${originalChange}, this.closest('.modal'))"
                                 style="
                                background: rgba(255, 255, 255, 0.15);
                                padding: 15px;
                                border-radius: 10px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                border: 2px solid rgba(255, 255, 255, 0.2);
                                position: relative;
                                overflow: hidden;
                            "
                            onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.2)'"
                            onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <div style="font-weight: 600; margin-bottom: 8px;">
                                    ${candidate.name}
                                </div>
                                <div style="
                                    font-size: 1.2rem;
                                    font-weight: bold;
                                    color: ${candidate.score > student.score ? '#00b894' : candidate.score < student.score ? '#ff7675' : '#74b9ff'};
                                    margin-bottom: 5px;
                                ">
                                    ${candidate.score} 分
                                </div>
                                <div style="
                                    font-size: 0.8rem;
                                    opacity: 0.8;
                                    color: ${candidate.score > student.score ? '#00b894' : candidate.score < student.score ? '#ff7675' : '#74b9ff'};
                                ">
                                    ${candidate.score > student.score ? '↗ 獲利' : candidate.score < student.score ? '↘ 虧損' : '→ 持平'}
                                    ${Math.abs(candidate.score - student.score)} 分
                                </div>

                                ${candidate.specialStates && candidate.specialStates.angelProtection ? `
                                    <div style="
                                        position: absolute;
                                        top: 5px;
                                        right: 5px;
                                        font-size: 0.8rem;
                                    ">😇</div>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>

                    <div style="margin-top: 25px;">
                        <button onclick="cancelSwap(this.closest('.modal'))" style="
                            background: rgba(255, 255, 255, 0.2);
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            color: white;
                            padding: 10px 20px;
                            border-radius: 25px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                        "
                        onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
                        onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">
                            <i class="fas fa-times"></i> 取消交換
                        </button>
                    </div>
                </div>
            `;

            // 顯示動畫
            setTimeout(() => {
                const swapContent = content.querySelector('.swap-selection-content');
                if (swapContent) {
                    swapContent.style.opacity = '1';
                    swapContent.style.transform = 'translateY(0)';
                }
            }, 100);

            console.log('交換選擇界面顯示完成');

        } catch (error) {
            console.error('顯示交換選擇界面錯誤:', error);
            // 備用方案：隨機交換
            const randomTarget = swapCandidates[Math.floor(Math.random() * swapCandidates.length)];
            executeSwap(student.id, randomTarget.id, originalChange, parentModal);
        }
    }

    // 執行分數交換
    window.executeSwap = function(studentId, targetId, originalChange, modal) {
        try {
            console.log('執行分數交換:', studentId, '↔', targetId);

            const studentIndex = students.findIndex(s => s.id == studentId);
            const targetIndex = students.findIndex(s => s.id == targetId);

            if (studentIndex === -1 || targetIndex === -1) {
                console.error('找不到交換的學生');
                return;
            }

            const student = students[studentIndex];
            const target = students[targetIndex];

            const studentOldScore = student.score;
            const targetOldScore = target.score;

            // 執行交換
            students[studentIndex].score = targetOldScore;
            students[targetIndex].score = studentOldScore;

            // 保存更新
            saveStudents();

            const resultMessage = `🔄 分數交換成功！與 ${target.name} 交換分數：${studentOldScore} ↔ ${targetOldScore}`;
            const affectedStudents = [target.name];

            // 記錄交換雙方的歷史
            addScoreHistory({
                studentId: student.id,
                studentName: student.name,
                oldScore: studentOldScore,
                newScore: targetOldScore,
                change: targetOldScore - studentOldScore,
                reason: 'mystery_box',
                note: `神祕寶箱 - 交換：與 ${target.name} 交換分數`,
                timestamp: new Date().toISOString()
            });

            addScoreHistory({
                studentId: target.id,
                studentName: target.name,
                oldScore: targetOldScore,
                newScore: studentOldScore,
                change: studentOldScore - targetOldScore,
                reason: 'mystery_box',
                note: `神祕寶箱 - 被 ${student.name} 交換分數`,
                timestamp: new Date().toISOString()
            });

            console.log('交換執行完成，顯示結果');

            // 顯示交換結果
            const swapEvent = {
                id: 'swap',
                name: '交換',
                icon: '🔄',
                description: '與選定的同學交換總分數',
                color: '#a29bfe'
            };

            showMysteryBoxResult(swapEvent, resultMessage, affectedStudents, modal);

        } catch (error) {
            console.error('執行交換錯誤:', error);
            alert('交換執行失敗，請重試。');
            closeMysteryBoxModal(modal);
        }
    };

    // 取消交換
    window.cancelSwap = function(modal) {
        try {
            console.log('用戶取消交換');

            const cancelEvent = {
                id: 'swap',
                name: '交換',
                icon: '🔄',
                description: '與其他同學交換總分數',
                color: '#a29bfe'
            };

            const cancelMessage = '🔄 您選擇了取消交換，沒有進行任何分數變動。';

            showMysteryBoxResult(cancelEvent, cancelMessage, [], modal);

        } catch (error) {
            console.error('取消交換錯誤:', error);
            closeMysteryBoxModal(modal);
        }
    };
    
    // 渲染分頁
    function renderPagination() {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一頁
        paginationHTML += `
            <button class="page-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 頁碼
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                paginationHTML += `
                    <button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === currentPage - 3 || i === currentPage + 3) {
                paginationHTML += '<span class="ellipsis">...</span>';
            }
        }
        
        // 下一頁
        paginationHTML += `
            <button class="page-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        pagination.innerHTML = paginationHTML;
    }
    
    // 切換頁面
    window.changePage = function(page) {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        if (page < 1 || page > totalPages) return;
        
        currentPage = page;
        renderStudentsGrid();
        renderPagination();
    };
    
    // 保存學生資料
    function saveStudents() {
        localStorage.setItem('students', JSON.stringify(students));
    }
    
    // 顯示空狀態
    function showEmptyState() {
        studentsGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                <p>還沒有學生資料</p>
                <a href="index.html" class="btn btn-primary">前往新增學生</a>
            </div>
        `;
    }
    
    // 關閉彈窗
    function closeModal() {
        customScoreModal.style.display = 'none';
        batchModal.style.display = 'none';
        historyModal.style.display = 'none';
        selectedStudent = null;
    }
    
    // 顯示通知
    function showNotification(message, type = 'info') {
        // 這裡可以使用與 daily-checkin.js 相同的通知系統
        // 為了簡化，這裡使用 alert
        if (type === 'success') {
            console.log('✅ ' + message);
        } else if (type === 'warning') {
            console.log('⚠️ ' + message);
        } else if (type === 'error') {
            console.log('❌ ' + message);
        } else {
            console.log('ℹ️ ' + message);
        }
        
        // 簡單的通知顯示
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: ${type === 'success' ? '#00b894' : type === 'warning' ? '#fdcb6e' : type === 'error' ? '#ff7675' : '#74b9ff'};
            color: white;
            border-radius: 8px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    // 添加 CSS 動畫樣式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .notification {
            animation: slideIn 0.3s ease;
        }
    `;
    document.head.appendChild(style);

    // 匯出資料
    function exportData() {
        const dataStr = JSON.stringify(students, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `學生名單_${new Date().toISOString().split('T')[0]}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    }

    // 匯入資料
    function importData(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedStudents = JSON.parse(e.target.result);
                if (Array.isArray(importedStudents)) {
                    if (confirm(`確定要匯入 ${importedStudents.length} 筆學生資料嗎？這將覆蓋現有資料。`)) {
                        students = importedStudents;
                        saveStudents();
                        updateClassFilter();
                        updateHistoryStudentFilter();
                        filterStudents();
                        alert('資料匯入成功！');
                    }
                } else {
                    alert('匯入的檔案格式不正確！');
                }
            } catch (error) {
                console.error('匯入錯誤:', error);
                alert('匯入檔案時發生錯誤！');
            }
            // 重置檔案輸入，允許重複選擇同一個檔案
            importFile.value = '';
        };
        reader.readAsText(file);
    }

    // 初始化頁面
    initPage();
});
