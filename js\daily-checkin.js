// 每日簽到功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const checkinTableBody = document.getElementById('checkinTableBody');
    const checkAllBtn = document.getElementById('checkAllBtn');
    const uncheckAllBtn = document.getElementById('uncheckAllBtn');
    const searchInput = document.getElementById('searchInput');
    const confirmModal = document.getElementById('confirmModal');
    const confirmMessage = document.getElementById('confirmMessage');
    const studentNameSpan = document.getElementById('studentNameSpan');
    const confirmCheckinBtn = document.getElementById('confirmCheckinBtn');
    const cancelCheckinBtn = document.getElementById('cancelCheckinBtn');
    const closeModalBtns = document.querySelectorAll('.close');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');
    const currentDateElement = document.getElementById('currentDate');
    const totalStudentsElement = document.getElementById('totalStudents');
    const checkedInCountElement = document.getElementById('checkedInCount');
    const notCheckedInCountElement = document.getElementById('notCheckedInCount');
    const paginationElement = document.getElementById('pagination');
    
    // 常數設定
    const ITEMS_PER_PAGE = 10; // 每頁顯示的學生數量
    const CHECKIN_REWARD = 5; // 簽到獎勵分數
    
    // 狀態變數
    let students = [];
    let filteredStudents = [];
    let currentPage = 1;
    let selectedStudent = null;
    let today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    
    // 初始化頁面
    function initPage() {
        // 設置當前日期
        const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
        const todayFormatted = new Date().toLocaleDateString('zh-TW', options);
        currentDateElement.textContent = todayFormatted;
        
        // 載入學生資料
        loadStudents();
        
        // 事件監聽器
        checkAllBtn.addEventListener('click', checkAllStudents);
        uncheckAllBtn.addEventListener('click', uncheckAllStudents);
        searchInput.addEventListener('input', filterStudents);
        confirmCheckinBtn.addEventListener('click', confirmCheckin);
        cancelCheckinBtn.addEventListener('click', closeModal);
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', closeModal);
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === confirmModal) {
                closeModal();
            }
        });

        // 匯入匯出功能
        exportBtn.addEventListener('click', exportData);
        importBtn.addEventListener('click', () => importFile.click());
        importFile.addEventListener('change', importData);
    }
    
    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
            
            // 初始化每個學生的簽到記錄
            students.forEach(student => {
                if (!student.checkins) {
                    student.checkins = {};
                }
                
                // 如果今天還沒有簽到記錄，初始化為未簽到
                if (!student.checkins[today]) {
                    student.checkins[today] = {
                        checkedIn: false,
                        timestamp: null
                    };
                }
            });
            
            // 保存更新後的學生資料
            saveStudents();
            
            // 過濾和顯示學生
            filterStudents();
            updateSummary();
        } else {
            alert('找不到學生資料，請先新增學生。');
            window.location.href = 'index.html';
        }
    }
    
    // 過濾學生
    function filterStudents() {
        const searchTerm = searchInput.value.toLowerCase();
        
        filteredStudents = students.filter(student => {
            return (
                student.name.toLowerCase().includes(searchTerm) ||
                student.number.toString().includes(searchTerm) ||
                student.class.toString().includes(searchTerm)
            );
        });
        
        // 重置到第一頁
        currentPage = 1;
        renderStudentTable();
        renderPagination();
        updateSummary();
    }
    
    // 渲染學生表格
    function renderStudentTable() {
        if (!filteredStudents.length) {
            checkinTableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 30px 0;">沒有找到符合條件的學生</td></tr>';
            return;
        }
        
        // 計算當前頁的學生
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, filteredStudents.length);
        const currentPageStudents = filteredStudents.slice(startIndex, endIndex);
        
        checkinTableBody.innerHTML = '';
        
        currentPageStudents.forEach(student => {
            const isCheckedIn = student.checkins[today]?.checkedIn || false;
            const row = document.createElement('tr');
            
            const timestamp = student.checkins[today]?.timestamp;
            const timeString = timestamp ? formatTime(new Date(timestamp)) : '-';
            const streakDays = calculateStreakDays(student);
            const monthlyRate = calculateMonthlyRate(student);
            
            row.innerHTML = `
                <td>${student.number}</td>
                <td>${student.name}</td>
                <td>${student.score || 0} 分</td>
                <td>
                    <span class="status-badge ${isCheckedIn ? 'status-checked' : 'status-unchecked'}">
                        ${isCheckedIn ? '已簽到' : '未簽到'}
                    </span>
                </td>
                <td class="time-column">
                    ${timeString}
                </td>
                <td class="streak-column">
                    <span class="streak-badge ${streakDays >= 7 ? 'streak-high' : streakDays >= 3 ? 'streak-medium' : 'streak-low'}">
                        ${streakDays} 天
                    </span>
                </td>
                <td class="rate-column">
                    <span class="rate-badge ${monthlyRate >= 80 ? 'rate-high' : monthlyRate >= 60 ? 'rate-medium' : 'rate-low'}">
                        ${monthlyRate}%
                    </span>
                </td>
                <td>
                    ${!isCheckedIn ? `
                        <button class="btn btn-sm btn-primary checkin-btn" data-id="${student.id}">
                            <i class="fas fa-calendar-check"></i> 簽到
                        </button>
                    ` : `
                        <span class="text-muted">已簽到</span>
                    `}
                </td>
            `;
            
            checkinTableBody.appendChild(row);
        });
        
        // 添加簽到按鈕事件監聽器
        document.querySelectorAll('.checkin-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const studentId = parseInt(this.getAttribute('data-id'));
                showCheckinConfirmation(studentId);
            });
        });
    }
    
    // 渲染分頁
    function renderPagination() {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        
        // 如果只有一頁或沒有學生，不顯示分頁
        if (totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一頁按鈕
        paginationHTML += `
            <button class="page-btn" id="prevPage" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 頁碼按鈕
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        // 第一頁按鈕
        if (startPage > 1) {
            paginationHTML += `
                <button class="page-btn" data-page="1">1</button>
                ${startPage > 2 ? '<span class="ellipsis">...</span>' : ''}
            `;
        }
        
        // 頁碼
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        // 最後一頁按鈕
        if (endPage < totalPages) {
            paginationHTML += `
                ${endPage < totalPages - 1 ? '<span class="ellipsis">...</span>' : ''}
                <button class="page-btn" data-page="${totalPages}">${totalPages}</button>
            `;
        }
        
        // 下一頁按鈕
        paginationHTML += `
            <button class="page-btn" id="nextPage" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        paginationElement.innerHTML = paginationHTML;
        
        // 添加頁面按鈕事件監聽器
        document.querySelectorAll('.page-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.id === 'prevPage') {
                    if (currentPage > 1) changePage(currentPage - 1);
                } else if (this.id === 'nextPage') {
                    if (currentPage < totalPages) changePage(currentPage + 1);
                } else {
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page && !isNaN(page)) changePage(page);
                }
            });
        });
    }
    
    // 切換頁面
    function changePage(page) {
        if (page < 1 || page > Math.ceil(filteredStudents.length / ITEMS_PER_PAGE)) return;
        
        currentPage = page;
        renderStudentTable();
        renderPagination();
        
        // 滾動到表格頂部
        checkinTableBody.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
    
    // 顯示簽到確認彈窗
    function showCheckinConfirmation(studentId) {
        const student = students.find(s => s.id === studentId);
        if (!student) return;
        
        selectedStudent = student;
        studentNameSpan.textContent = student.name;
        confirmMessage.textContent = `確定要為 ${student.name} 登記作業繳交簽到嗎？`;
        confirmModal.style.display = 'flex';
    }
    
    // 確認簽到
    function confirmCheckin() {
        if (!selectedStudent) {
            closeModal();
            return;
        }
        
        // 更新學生簽到狀態
        const student = students.find(s => s.id === selectedStudent.id);
        if (student) {
            student.checkins[today] = {
                checkedIn: true,
                timestamp: new Date().toISOString()
            };
            
            // 增加分數
            student.score = (student.score || 0) + CHECKIN_REWARD;
            
            // 保存更新
            saveStudents();
            
            // 更新UI
            renderStudentTable();
            updateSummary();
            
            // 顯示成功消息
            showNotification(`${student.name} 簽到成功！獲得 ${CHECKIN_REWARD} 分`, 'success');
        }
        
        closeModal();
    }
    
    // 全部簽到
    function checkAllStudents() {
        if (confirm('確定要為所有未簽到的學生簽到嗎？')) {
            let checkedCount = 0;
            
            students.forEach(student => {
                if (!student.checkins[today]?.checkedIn) {
                    student.checkins[today] = {
                        checkedIn: true,
                        timestamp: new Date().toISOString()
                    };
                    student.score = (student.score || 0) + CHECKIN_REWARD;
                    checkedCount++;
                }
            });
            
            if (checkedCount > 0) {
                saveStudents();
                renderStudentTable();
                updateSummary();
                showNotification(`已為 ${checkedCount} 名學生簽到，每人獲得 ${CHECKIN_REWARD} 分`, 'success');
            } else {
                showNotification('所有學生都已經簽到過了！', 'info');
            }
        }
    }
    
    // 全部取消簽到
    function uncheckAllStudents() {
        if (confirm('確定要取消所有學生的簽到嗎？這將撤銷他們今天獲得的積分！')) {
            let uncheckedCount = 0;
            
            students.forEach(student => {
                if (student.checkins[today]?.checkedIn) {
                    student.checkins[today].checkedIn = false;
                    student.score = Math.max(0, (student.score || 0) - CHECKIN_REWARD);
                    uncheckedCount++;
                }
            });
            
            if (uncheckedCount > 0) {
                saveStudents();
                renderStudentTable();
                updateSummary();
                showNotification(`已取消 ${uncheckedCount} 名學生的簽到`, 'warning');
            } else {
                showNotification('沒有學生需要取消簽到', 'info');
            }
        }
    }
    
    // 更新摘要信息
    function updateSummary() {
        const total = students.length;
        const checkedIn = students.filter(s => s.checkins[today]?.checkedIn).length;
        const notCheckedIn = total - checkedIn;
        
        totalStudentsElement.textContent = total;
        checkedInCountElement.textContent = checkedIn;
        notCheckedInCountElement.textContent = notCheckedIn;
        
        // 更新統計信息
        updateStatistics();
    }
    
    // 計算學生連續簽到天數
    function calculateStreakDays(student) {
        if (!student.checkins) return 0;
        
        let streak = 0;
        let currentDate = new Date();
        
        // 從今天開始往前檢查
        while (true) {
            const dateStr = currentDate.toISOString().split('T')[0];
            
            // 跳過週末（假設週末不需要簽到）
            if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
                currentDate.setDate(currentDate.getDate() - 1);
                continue;
            }
            
            if (student.checkins[dateStr]?.checkedIn) {
                streak++;
                currentDate.setDate(currentDate.getDate() - 1);
            } else {
                break;
            }
            
            // 防止無限迴圈，最多檢查30天
            if (streak >= 30) break;
        }
        
        return streak;
    }
    
    // 計算學生本月簽到率
    function calculateMonthlyRate(student) {
        if (!student.checkins) return 0;
        
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        let totalWorkdays = 0;
        let checkedDays = 0;
        
        for (let d = new Date(firstDay); d <= lastDay; d.setDate(d.getDate() + 1)) {
            // 跳過週末
            if (d.getDay() === 0 || d.getDay() === 6) continue;
            
            totalWorkdays++;
            const dateStr = d.toISOString().split('T')[0];
            
            if (student.checkins[dateStr]?.checkedIn) {
                checkedDays++;
            }
        }
        
        return totalWorkdays === 0 ? 0 : Math.round((checkedDays / totalWorkdays) * 100);
    }
    
    // 計算本週簽到率
    function calculateWeeklyRate() {
        if (students.length === 0) return 0;
        
        const now = new Date();
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay() + 1); // 週一
        
        let totalWorkdays = 0;
        let totalCheckedIn = 0;
        
        for (let d = new Date(startOfWeek); d <= now; d.setDate(d.getDate() + 1)) {
            // 跳過週末
            if (d.getDay() === 0 || d.getDay() === 6) continue;
            
            totalWorkdays++;
            const dateStr = d.toISOString().split('T')[0];
            
            const checkedInToday = students.filter(s => s.checkins[dateStr]?.checkedIn).length;
            totalCheckedIn += checkedInToday;
        }
        
        const maxPossible = totalWorkdays * students.length;
        return maxPossible === 0 ? 0 : Math.round((totalCheckedIn / maxPossible) * 100);
    }
    
    // 計算整體本月簽到率
    function calculateOverallMonthlyRate() {
        if (students.length === 0) return 0;
        
        const rates = students.map(s => calculateMonthlyRate(s));
        const sum = rates.reduce((acc, rate) => acc + rate, 0);
        return Math.round(sum / rates.length);
    }
    
    // 計算平均連續天數
    function calculateAverageStreak() {
        if (students.length === 0) return 0;
        
        const streaks = students.map(s => calculateStreakDays(s));
        const sum = streaks.reduce((acc, streak) => acc + streak, 0);
        return Math.round(sum / streaks.length);
    }
    
    // 找出簽到王
    function findTopPerformer() {
        if (students.length === 0) return '--';
        
        let topStudent = null;
        let maxRate = -1;
        
        students.forEach(student => {
            const rate = calculateMonthlyRate(student);
            if (rate > maxRate) {
                maxRate = rate;
                topStudent = student;
            }
        });
        
        return topStudent ? `${topStudent.name} (${maxRate}%)` : '--';
    }
    
    // 更新統計信息
    function updateStatistics() {
        const weeklyRateElement = document.getElementById('weeklyRate');
        const monthlyRateElement = document.getElementById('monthlyRate');
        const averageStreakElement = document.getElementById('averageStreak');
        const topPerformerElement = document.getElementById('topPerformer');
        
        if (weeklyRateElement) weeklyRateElement.textContent = calculateWeeklyRate() + '%';
        if (monthlyRateElement) monthlyRateElement.textContent = calculateOverallMonthlyRate() + '%';
        if (averageStreakElement) averageStreakElement.textContent = calculateAverageStreak();
        if (topPerformerElement) topPerformerElement.textContent = findTopPerformer();
    }
    
    // 保存學生資料到本地存儲
    function saveStudents() {
        localStorage.setItem('students', JSON.stringify(students));
    }
    
    // 關閉彈窗
    function closeModal() {
        confirmModal.style.display = 'none';
        selectedStudent = null;
    }
    
    // 顯示通知消息
    function showNotification(message, type = 'info') {
        // 創建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="close-notification">&times;</button>
        `;
        
        // 添加到頁面
        document.body.appendChild(notification);
        
        // 顯示通知
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // 點擊關閉按鈕
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });
        
        // 自動關閉
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }
    
    // 獲取通知圖標
    function getNotificationIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    }
    
    // 匯出資料
    function exportData() {
        const dataStr = JSON.stringify(students, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `學生名單_${new Date().toISOString().split('T')[0]}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    }

    // 匯入資料
    function importData(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedStudents = JSON.parse(e.target.result);
                if (Array.isArray(importedStudents)) {
                    if (confirm(`確定要匯入 ${importedStudents.length} 筆學生資料嗎？這將覆蓋現有資料。`)) {
                        students = importedStudents;

                        // 初始化每個學生的簽到記錄
                        students.forEach(student => {
                            if (!student.checkins) {
                                student.checkins = {};
                            }

                            // 如果今天還沒有簽到記錄，初始化為未簽到
                            if (!student.checkins[today]) {
                                student.checkins[today] = {
                                    checkedIn: false,
                                    timestamp: null
                                };
                            }
                        });

                        saveStudents();
                        filterStudents();
                        updateSummary();
                        alert('資料匯入成功！');
                    }
                } else {
                    alert('匯入的檔案格式不正確！');
                }
            } catch (error) {
                console.error('匯入錯誤:', error);
                alert('匯入檔案時發生錯誤！');
            }
            // 重置檔案輸入，允許重複選擇同一個檔案
            importFile.value = '';
        };
        reader.readAsText(file);
    }

    // 格式化時間顯示
    function formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
    
    // 初始化頁面
    initPage();
});
