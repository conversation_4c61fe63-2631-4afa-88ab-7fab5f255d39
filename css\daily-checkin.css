/* 每日簽到頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.date-display {
    background: white;
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: var(--box-shadow);
    font-weight: 500;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-display i {
    color: var(--primary-color);
}

/* 簽到摘要卡片 */
.checkin-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.summary-card i {
    font-size: 2.5rem;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.summary-card i.fa-users {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
}

.summary-card i.fa-calendar-check {
    background: linear-gradient(135deg, #00b894, #55efc4);
}

.summary-card i.fa-calendar-times {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
}

.summary-info {
    display: flex;
    flex-direction: column;
}

.summary-count {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
    color: var(--dark-color);
}

.summary-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 統計分析區域 */
.statistics-section {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-bottom: 30px;
}

.statistics-section h3 {
    margin: 0 0 20px 0;
    color: var(--dark-color);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.statistics-section h3 i {
    color: var(--primary-color);
}

.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.stat-card:nth-child(1) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 5px;
}

/* 操作按鈕區域 */
.action-buttons {
    display: flex;
    justify-content: space-between;
    margin: 25px 0;
    flex-wrap: wrap;
    gap: 15px;
}

.button-group {
    display: flex;
    gap: 10px;
}

.search-box {
    position: relative;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 50px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2);
}

/* 表格樣式 */
.table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 25px;
}

#checkinTable {
    width: 100%;
    border-collapse: collapse;
}

#checkinTable th,
#checkinTable td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

#checkinTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
    white-space: nowrap;
}

#checkinTable tbody tr:hover {
    background-color: #f8f9fa;
}

/* 簽到狀態標籤 */
.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
}

.status-checked {
    background-color: #e3f9e5;
    color: #00b894;
}

.status-unchecked {
    background-color: #fff0f0;
    color: #ff7675;
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
}

.pagination button {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
    background-color: #f0f0f0;
    border-color: #ccc;
}

.pagination button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 確認彈窗 */
.reward-info {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #f8f5ff;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    color: var(--primary-color);
    font-weight: 500;
}

.reward-info i {
    font-size: 1.5rem;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
}

/* 個人統計欄位樣式 */
.streak-column, .rate-column {
    text-align: center;
    white-space: nowrap;
}

.streak-badge, .rate-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.streak-high {
    background-color: #e8f5e8;
    color: #2e7d2e;
}

.streak-medium {
    background-color: #fff3cd;
    color: #856404;
}

.streak-low {
    background-color: #f8d7da;
    color: #721c24;
}

.rate-high {
    background-color: #d1ecf1;
    color: #0c5460;
}

.rate-medium {
    background-color: #fff3cd;
    color: #856404;
}

.rate-low {
    background-color: #f8d7da;
    color: #721c24;
}

/* 響應式設計 */
@media (max-width: 992px) {
    .checkin-summary {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-display {
        width: 100%;
        justify-content: center;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .button-group {
        width: 100%;
    }
    
    .button-group .btn {
        flex: 1;
        text-align: center;
    }
    
    .search-box {
        width: 100%;
    }
    
    .checkin-summary {
        grid-template-columns: 1fr;
    }
    
    #checkinTable th,
    #checkinTable td {
        padding: 10px;
        font-size: 0.9rem;
    }
}

/* 動畫效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 1.5s infinite;
}

/* 時間欄位樣式 */
.time-column {
    font-family: 'Monaco', 'Consolas', monospace;
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
}

.time-column:not(:empty) {
    color: var(--primary-color);
    background-color: #f8f9fe;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
