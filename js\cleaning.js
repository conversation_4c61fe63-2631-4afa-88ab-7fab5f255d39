// 打掃登記功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const cleaningTableBody = document.getElementById('cleaningTableBody');
    const markAllExcellentBtn = document.getElementById('markAllExcellentBtn');
    const markAllGoodBtn = document.getElementById('markAllGoodBtn');
    const unmarkAllBtn = document.getElementById('unmarkAllBtn');
    const searchInput = document.getElementById('searchInput');
    const confirmModal = document.getElementById('confirmModal');
    const editModal = document.getElementById('editModal');
    const confirmMessage = document.getElementById('confirmMessage');
    const studentNameSpan = document.getElementById('studentNameSpan');
    const editStudentNameSpan = document.getElementById('editStudentNameSpan');
    const currentRecord = document.getElementById('currentRecord');
    const currentTime = document.getElementById('currentTime');
    const confirmCleaningBtn = document.getElementById('confirmCleaningBtn');
    const confirmEditBtn = document.getElementById('confirmEditBtn');
    const cancelCleaningBtn = document.getElementById('cancelCleaningBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const closeModalBtns = document.querySelectorAll('.close');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');
    const currentDateElement = document.getElementById('currentDate');
    const totalStudentsElement = document.getElementById('totalStudents');
    const excellentCountElement = document.getElementById('excellentCount');
    const goodCountElement = document.getElementById('goodCount');
    const notCleanedCountElement = document.getElementById('notCleanedCount');
    const paginationElement = document.getElementById('pagination');
    
    // 常數設定
    const ITEMS_PER_PAGE = 10; // 每頁顯示的學生數量
    const EXCELLENT_REWARD = 5; // 認真打掃獎勵分數
    const GOOD_REWARD = 3; // 打掃完成獎勵分數
    
    // 狀態變數
    let students = [];
    let filteredStudents = [];
    let currentPage = 1;
    let selectedStudent = null;
    let selectedRating = null;
    let isEditMode = false; // 是否為修改模式
    let originalRecord = null; // 原始記錄
    let today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    
    // 初始化頁面
    function initPage() {
        // 設置當前日期
        const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
        const todayFormatted = new Date().toLocaleDateString('zh-TW', options);
        currentDateElement.textContent = todayFormatted;
        
        // 載入學生資料
        loadStudents();
        
        // 事件監聽器
        markAllExcellentBtn.addEventListener('click', () => markAllStudents('excellent'));
        markAllGoodBtn.addEventListener('click', () => markAllStudents('good'));
        unmarkAllBtn.addEventListener('click', unmarkAllStudents);
        searchInput.addEventListener('input', filterStudents);
        confirmCleaningBtn.addEventListener('click', confirmCleaning);
        confirmEditBtn.addEventListener('click', confirmEdit);
        cancelCleaningBtn.addEventListener('click', closeModal);
        cancelEditBtn.addEventListener('click', closeEditModal);
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                closeModal();
                closeEditModal();
            });
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === confirmModal) {
                closeModal();
            }
            if (e.target === editModal) {
                closeEditModal();
            }
        });

        // 匯入匯出功能
        exportBtn.addEventListener('click', exportData);
        importBtn.addEventListener('click', () => importFile.click());
        importFile.addEventListener('change', importData);
        
        // 評分選項點擊事件 - 使用事件委託
        document.addEventListener('click', function(e) {
            if (e.target.closest('.rating-option')) {
                const option = e.target.closest('.rating-option');
                const modal = option.closest('.modal');
                const isEditModal = modal.id === 'editModal';
                
                // 只選中同一個模態框內的選項
                const modalOptions = modal.querySelectorAll('.rating-option');
                modalOptions.forEach(opt => opt.classList.remove('selected'));
                
                option.classList.add('selected');
                selectedRating = option.dataset.rating;
                
                if (isEditModal) {
                    confirmEditBtn.disabled = false;
                } else {
                    confirmCleaningBtn.disabled = false;
                }
            }
        });
    }
    
    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
            
            // 初始化每個學生的打掃記錄
            students.forEach(student => {
                if (!student.cleaning) {
                    student.cleaning = {};
                }
                
                // 如果今天還沒有打掃記錄，初始化為未打掃
                if (!student.cleaning[today]) {
                    student.cleaning[today] = {
                        status: 'none', // 'excellent', 'good', 'none'
                        timestamp: null,
                        score: 0
                    };
                }
            });
            
            // 保存更新後的學生資料
            saveStudents();
            
            // 過濾和顯示學生
            filterStudents();
            updateSummary();
        } else {
            alert('找不到學生資料，請先新增學生。');
            window.location.href = 'index.html';
        }
    }
    
    // 過濾學生
    function filterStudents() {
        const searchTerm = searchInput.value.toLowerCase();
        
        filteredStudents = students.filter(student => {
            return (
                student.name.toLowerCase().includes(searchTerm) ||
                student.number.toString().includes(searchTerm) ||
                student.class.toString().includes(searchTerm)
            );
        });
        
        // 重置到第一頁
        currentPage = 1;
        renderStudentTable();
        renderPagination();
        updateSummary();
    }
    
    // 渲染學生表格
    function renderStudentTable() {
        if (!filteredStudents.length) {
            cleaningTableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 30px 0;">沒有找到符合條件的學生</td></tr>';
            return;
        }
        
        // 計算當前頁的學生
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, filteredStudents.length);
        const currentPageStudents = filteredStudents.slice(startIndex, endIndex);
        
        cleaningTableBody.innerHTML = '';
        
        currentPageStudents.forEach(student => {
            const cleaningRecord = student.cleaning[today];
            const timestamp = cleaningRecord?.timestamp;
            const timeString = timestamp ? formatTime(new Date(timestamp)) : '-';
            const monthlyPerformance = calculateMonthlyPerformance(student);
            
            const row = document.createElement('tr');
            
            // 動物頭像 - 使用與主頁面相同的邏輯
            let animalDisplay = student.animal || '🐾';
            if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                animalDisplay = `
                    <div class="student-animal-avatar" title="${animalInfo.name} - ${animalInfo.personality}">
                        <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                        <span class="animal-emoji">${animalInfo.emoji}</span>
                    </div>
                `;
            } else if (typeof student.animal === 'string') {
                // 如果是emoji，直接顯示
                animalDisplay = `<div class="student-animal-emoji">${student.animal}</div>`;
            }
            
            // 狀態顯示
            let statusBadge = '';
            let actionButton = '';
            
            switch (cleaningRecord.status) {
                case 'excellent':
                    statusBadge = '<span class="status-badge status-excellent">認真打掃</span>';
                    actionButton = `
                        <button class="btn btn-sm edit-btn" data-id="${student.id}">
                            <i class="fas fa-edit"></i> 修改
                        </button>
                    `;
                    break;
                case 'good':
                    statusBadge = '<span class="status-badge status-good">打掃完成</span>';
                    actionButton = `
                        <button class="btn btn-sm edit-btn" data-id="${student.id}">
                            <i class="fas fa-edit"></i> 修改
                        </button>
                    `;
                    break;
                default:
                    statusBadge = '<span class="status-badge status-not-cleaned">未打掃</span>';
                    actionButton = `<button class="btn btn-sm btn-primary cleaning-btn" data-id="${student.id}">
                        <i class="fas fa-broom"></i> 登記打掃
                    </button>`;
            }
            
            row.innerHTML = `
                <td class="animal-cell">${animalDisplay}</td>
                <td>${student.number}</td>
                <td>${student.name}</td>
                <td class="score">${student.score || 0} 分</td>
                <td>${statusBadge}</td>
                <td class="time-column">${timeString}</td>
                <td class="monthly-performance ${getPerformanceClass(monthlyPerformance)}">${monthlyPerformance}%</td>
                <td>${actionButton}</td>
            `;
            
            cleaningTableBody.appendChild(row);
        });
        
        // 添加打掃按鈕事件監聽器
        document.querySelectorAll('.cleaning-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const studentId = parseInt(this.getAttribute('data-id'));
                showCleaningConfirmation(studentId);
            });
        });
        
        // 添加修改按鈕事件監聽器
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const studentId = parseInt(this.getAttribute('data-id'));
                showEditConfirmation(studentId);
            });
        });
    }
    
    // 計算學生本月打掃表現百分比
    function calculateMonthlyPerformance(student) {
        if (!student.cleaning) return 0;
        
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        let totalWorkdays = 0;
        let cleanedDays = 0;
        
        for (let d = new Date(firstDay); d <= lastDay && d <= now; d.setDate(d.getDate() + 1)) {
            // 跳過週末
            if (d.getDay() === 0 || d.getDay() === 6) continue;
            
            totalWorkdays++;
            const dateStr = d.toISOString().split('T')[0];
            
            if (student.cleaning[dateStr] && student.cleaning[dateStr].status !== 'none') {
                cleanedDays++;
            }
        }
        
        return totalWorkdays === 0 ? 0 : Math.round((cleanedDays / totalWorkdays) * 100);
    }
    
    // 獲取表現等級的CSS類別
    function getPerformanceClass(percentage) {
        if (percentage >= 80) return 'performance-excellent';
        if (percentage >= 60) return 'performance-good';
        return 'performance-average';
    }
    
    // 渲染分頁
    function renderPagination() {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        
        if (totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一頁按鈕
        paginationHTML += `
            <button class="page-btn" id="prevPage" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 頁碼按鈕
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        if (startPage > 1) {
            paginationHTML += `
                <button class="page-btn" data-page="1">1</button>
                ${startPage > 2 ? '<span class="ellipsis">...</span>' : ''}
            `;
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        if (endPage < totalPages) {
            paginationHTML += `
                ${endPage < totalPages - 1 ? '<span class="ellipsis">...</span>' : ''}
                <button class="page-btn" data-page="${totalPages}">${totalPages}</button>
            `;
        }
        
        // 下一頁按鈕
        paginationHTML += `
            <button class="page-btn" id="nextPage" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        paginationElement.innerHTML = paginationHTML;
        
        // 添加頁面按鈕事件監聽器
        document.querySelectorAll('.page-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.id === 'prevPage') {
                    if (currentPage > 1) changePage(currentPage - 1);
                } else if (this.id === 'nextPage') {
                    if (currentPage < totalPages) changePage(currentPage + 1);
                } else {
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page && !isNaN(page)) changePage(page);
                }
            });
        });
    }
    
    // 切換頁面
    function changePage(page) {
        if (page < 1 || page > Math.ceil(filteredStudents.length / ITEMS_PER_PAGE)) return;
        
        currentPage = page;
        renderStudentTable();
        renderPagination();
        
        // 滾動到表格頂部
        cleaningTableBody.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
    
    // 顯示打掃確認彈窗
    function showCleaningConfirmation(studentId) {
        const student = students.find(s => s.id === studentId);
        if (!student) return;
        
        isEditMode = false;
        selectedStudent = student;
        selectedRating = null;
        studentNameSpan.textContent = student.name;
        confirmCleaningBtn.disabled = true;
        
        // 重置評分選項
        confirmModal.querySelectorAll('.rating-option').forEach(opt => opt.classList.remove('selected'));
        
        confirmModal.style.display = 'flex';
    }
    
    // 顯示修改確認彈窗
    function showEditConfirmation(studentId) {
        const student = students.find(s => s.id === studentId);
        if (!student) return;
        
        const cleaningRecord = student.cleaning[today];
        if (!cleaningRecord || cleaningRecord.status === 'none') return;
        
        isEditMode = true;
        selectedStudent = student;
        selectedRating = null;
        originalRecord = { ...cleaningRecord }; // 保存原始記錄
        
        editStudentNameSpan.textContent = student.name;
        confirmEditBtn.disabled = true;
        
        // 顯示目前記錄
        const statusText = cleaningRecord.status === 'excellent' ? '認真打掃 (+5分)' : '打掃完成 (+3分)';
        currentRecord.textContent = statusText;
        currentTime.textContent = cleaningRecord.timestamp ? formatTime(new Date(cleaningRecord.timestamp)) : '-';
        
        // 重置評分選項
        editModal.querySelectorAll('.rating-option').forEach(opt => opt.classList.remove('selected'));
        
        editModal.style.display = 'flex';
    }
    
    // 確認打掃登記
    function confirmCleaning() {
        if (!selectedStudent || !selectedRating) {
            closeModal();
            return;
        }
        
        const student = students.find(s => s.id === selectedStudent.id);
        if (student) {
            const score = selectedRating === 'excellent' ? EXCELLENT_REWARD : GOOD_REWARD;
            
            student.cleaning[today] = {
                status: selectedRating,
                timestamp: new Date().toISOString(),
                score: score
            };
            
            // 增加分數
            student.score = (student.score || 0) + score;
            
            // 保存更新
            saveStudents();
            
            // 更新UI
            renderStudentTable();
            updateSummary();
            
            // 顯示成功消息
            const statusText = selectedRating === 'excellent' ? '認真打掃' : '打掃完成';
            showNotification(`${student.name} ${statusText}登記成功！獲得 ${score} 分`, 'success');
        }
        
        closeModal();
    }
    
    // 確認修改打掃記錄
    function confirmEdit() {
        if (!selectedStudent || !selectedRating) {
            closeEditModal();
            return;
        }
        
        const student = students.find(s => s.id === selectedStudent.id);
        if (student) {
            // 撤銷原始分數
            if (originalRecord && originalRecord.score) {
                student.score = Math.max(0, (student.score || 0) - originalRecord.score);
            }
            
            // 設定新的記錄
            if (selectedRating === 'none') {
                // 取消記錄
                student.cleaning[today] = {
                    status: 'none',
                    timestamp: null,
                    score: 0
                };
            } else {
                // 修改為新的評分
                const score = selectedRating === 'excellent' ? EXCELLENT_REWARD : GOOD_REWARD;
                student.cleaning[today] = {
                    status: selectedRating,
                    timestamp: new Date().toISOString(),
                    score: score
                };
                // 增加新分數
                student.score = (student.score || 0) + score;
            }
            
            // 保存更新
            saveStudents();
            
            // 更新UI
            renderStudentTable();
            updateSummary();
            
            // 顯示成功消息
            let statusText;
            if (selectedRating === 'none') {
                statusText = '已取消打掃記錄';
            } else {
                statusText = selectedRating === 'excellent' ? '認真打掃' : '打掃完成';
            }
            showNotification(`${student.name} ${statusText}修改成功！`, 'success');
        }
        
        closeEditModal();
    }
    
    // 全部登記打掃
    function markAllStudents(rating) {
        const statusText = rating === 'excellent' ? '認真打掃' : '打掃完成';
        const score = rating === 'excellent' ? EXCELLENT_REWARD : GOOD_REWARD;
        
        if (confirm(`確定要為所有未登記的學生標記為「${statusText}」嗎？`)) {
            let markedCount = 0;
            
            students.forEach(student => {
                if (student.cleaning[today].status === 'none') {
                    student.cleaning[today] = {
                        status: rating,
                        timestamp: new Date().toISOString(),
                        score: score
                    };
                    student.score = (student.score || 0) + score;
                    markedCount++;
                }
            });
            
            if (markedCount > 0) {
                saveStudents();
                renderStudentTable();
                updateSummary();
                showNotification(`已為 ${markedCount} 名學生登記「${statusText}」，每人獲得 ${score} 分`, 'success');
            } else {
                showNotification('所有學生都已經登記過打掃了！', 'info');
            }
        }
    }
    
    // 全部取消打掃登記
    function unmarkAllStudents() {
        if (confirm('確定要取消所有學生的打掃登記嗎？這將撤銷他們今天獲得的積分！')) {
            let unmarkedCount = 0;
            let totalScoreDeducted = 0;
            
            students.forEach(student => {
                if (student.cleaning[today].status !== 'none') {
                    const deductedScore = student.cleaning[today].score;
                    student.cleaning[today] = {
                        status: 'none',
                        timestamp: null,
                        score: 0
                    };
                    student.score = Math.max(0, (student.score || 0) - deductedScore);
                    totalScoreDeducted += deductedScore;
                    unmarkedCount++;
                }
            });
            
            if (unmarkedCount > 0) {
                saveStudents();
                renderStudentTable();
                updateSummary();
                showNotification(`已取消 ${unmarkedCount} 名學生的打掃登記，扣除總分 ${totalScoreDeducted} 分`, 'warning');
            } else {
                showNotification('沒有學生需要取消打掃登記', 'info');
            }
        }
    }
    
    // 更新摘要信息
    function updateSummary() {
        const total = students.length;
        const excellent = students.filter(s => s.cleaning[today]?.status === 'excellent').length;
        const good = students.filter(s => s.cleaning[today]?.status === 'good').length;
        const notCleaned = total - excellent - good;
        
        totalStudentsElement.textContent = total;
        excellentCountElement.textContent = excellent;
        goodCountElement.textContent = good;
        notCleanedCountElement.textContent = notCleaned;
    }
    
    // 格式化時間顯示
    function formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
    
    // 保存學生資料到本地存儲
    function saveStudents() {
        localStorage.setItem('students', JSON.stringify(students));
    }
    
    // 關閉彈窗
    function closeModal() {
        confirmModal.style.display = 'none';
        selectedStudent = null;
        selectedRating = null;
        isEditMode = false;
    }
    
    // 關閉修改彈窗
    function closeEditModal() {
        editModal.style.display = 'none';
        selectedStudent = null;
        selectedRating = null;
        originalRecord = null;
        isEditMode = false;
    }
    
    // 顯示通知消息
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="close-notification">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }
    
    // 獲取通知圖標
    function getNotificationIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    }
    
    // 匯出資料
    function exportData() {
        const dataStr = JSON.stringify(students, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `學生名單_${new Date().toISOString().split('T')[0]}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    }

    // 匯入資料
    function importData(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedStudents = JSON.parse(e.target.result);
                if (Array.isArray(importedStudents)) {
                    if (confirm(`確定要匯入 ${importedStudents.length} 筆學生資料嗎？這將覆蓋現有資料。`)) {
                        students = importedStudents;

                        // 初始化每個學生的打掃記錄
                        students.forEach(student => {
                            if (!student.cleaning) {
                                student.cleaning = {};
                            }

                            if (!student.cleaning[today]) {
                                student.cleaning[today] = {
                                    status: 'none',
                                    timestamp: null,
                                    score: 0
                                };
                            }
                        });

                        saveStudents();
                        filterStudents();
                        updateSummary();
                        alert('資料匯入成功！');
                    }
                } else {
                    alert('匯入的檔案格式不正確！');
                }
            } catch (error) {
                console.error('匯入錯誤:', error);
                alert('匯入檔案時發生錯誤！');
            }
            importFile.value = '';
        };
        reader.readAsText(file);
    }
    
    // 初始化頁面
    initPage();
});