// 不妄語功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const honestyTableBody = document.getElementById('honestyTableBody');
    const searchInput = document.getElementById('searchInput');
    const markAllHonestBtn = document.getElementById('markAllHonestBtn');
    const resetHonestyBtn = document.getElementById('resetHonestyBtn');
    const honestyModal = document.getElementById('honestyModal');
    const editHonestyModal = document.getElementById('editHonestyModal');
    const studentNameSpan = document.getElementById('studentNameSpan');
    const confirmHonestyBtn = document.getElementById('confirmHonestyBtn');
    const cancelHonestyBtn = document.getElementById('cancelHonestyBtn');
    const editStudentNameSpan = document.getElementById('editStudentNameSpan');
    const editDateSpan = document.getElementById('editDateSpan');
    const confirmEditHonestyBtn = document.getElementById('confirmEditHonestyBtn');
    const cancelEditHonestyBtn = document.getElementById('cancelEditHonestyBtn');
    const honestyNote = document.getElementById('honestyNote');
    const editHonestyNote = document.getElementById('editHonestyNote');
    const closeModalBtns = document.querySelectorAll('.close');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const exportHonestyBtn = document.getElementById('exportHonestyBtn');
    const importFile = document.getElementById('importFile');
    const currentDateElement = document.getElementById('currentDate');
    const totalStudentsElement = document.getElementById('totalStudents');
    const todayHonestElement = document.getElementById('todayHonest');
    const todayDishonestElement = document.getElementById('todayDishonest');
    const totalHonestyScoreElement = document.getElementById('totalHonestyScore');
    const paginationElement = document.getElementById('pagination');
    
    // 常數設定
    const ITEMS_PER_PAGE = 10; // 每頁顯示的學生數量
    const HONESTY_SCORE = 5; // 誠實加分
    const DISHONESTY_PENALTY = -5; // 不誠實扣分
    
    // 狀態變數
    let students = [];
    let filteredStudents = [];
    let honestyData = {}; // 不妄語記錄資料
    let currentPage = 1;
    let selectedStudent = null;
    let selectedType = null;
    let editingRecord = null;
    let editSelectedType = null;
    let today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    
    // 初始化頁面
    function init() {
        loadStudents();
        loadHonestyData();
        displayCurrentDate();
        displayStudents();
        updateStatistics();
        setupEventListeners();
    }
    
    // 載入學生資料
    function loadStudents() {
        const storedStudents = localStorage.getItem('students');
        if (storedStudents) {
            students = JSON.parse(storedStudents);
        }
        filteredStudents = [...students];
    }
    
    // 載入不妄語記錄資料
    function loadHonestyData() {
        const storedData = localStorage.getItem('honestyData');
        if (storedData) {
            honestyData = JSON.parse(storedData);
        }
        
        // 確保每個學生都有記錄結構
        students.forEach(student => {
            if (!honestyData[student.id]) {
                honestyData[student.id] = {};
            }
        });
    }
    
    // 儲存不妄語記錄資料
    function saveHonestyData() {
        localStorage.setItem('honestyData', JSON.stringify(honestyData));
    }
    
    // 儲存學生資料
    function saveStudents() {
        localStorage.setItem('students', JSON.stringify(students));
    }
    
    // 顯示當前日期
    function displayCurrentDate() {
        const now = new Date();
        const dateString = now.toLocaleDateString('zh-TW', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        if (currentDateElement) {
            currentDateElement.textContent = dateString;
        }
    }
    
    // 顯示學生表格
    function displayStudents() {
        if (!honestyTableBody) return;
        
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        const studentsToShow = filteredStudents.slice(startIndex, endIndex);
        
        honestyTableBody.innerHTML = '';
        
        studentsToShow.forEach(student => {
            const row = document.createElement('tr');
            const todayRecord = honestyData[student.id] && honestyData[student.id][today];
            const honestyCount = getHonestyCount(student.id);
            const dishonestyCount = getDishonestyCount(student.id);
            const totalHonestyScore = getTotalHonestyScore(student.id);
            
            let todayDisplay = '';
            if (todayRecord) {
                const type = todayRecord.type;
                const time = new Date(todayRecord.timestamp).toLocaleTimeString('zh-TW', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                if (type === 'honest') {
                    todayDisplay = `<span class="badge honest">誠實 (${time})</span>`;
                } else if (type === 'dishonest') {
                    todayDisplay = `<span class="badge dishonest">不誠實 (${time})</span>`;
                }
            } else {
                todayDisplay = '<span class="badge none">未記錄</span>';
            }
            
            let animalDisplay = student.animal || '🐱';
            if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                animalDisplay = `<div class="student-animal-avatar">
                    <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                    <span class="animal-emoji">${animalInfo.emoji}</span>
                </div>`;
            }

            row.innerHTML = `
                <td>${animalDisplay}</td>
                <td>${student.number}</td>
                <td>${student.name}</td>
                <td class="score">${student.score}</td>
                <td>${todayDisplay}</td>
                <td class="count honest">${honestyCount}</td>
                <td class="count dishonest">${dishonestyCount}</td>
                <td class="score ${totalHonestyScore >= 0 ? 'positive' : 'negative'}">${totalHonestyScore >= 0 ? '+' : ''}${totalHonestyScore}</td>
                <td>
                    <button class="btn honesty-btn honest" onclick="openHonestyModal(${student.id}, 'honest')">
                        ✅ 誠實
                    </button>
                    <button class="btn honesty-btn dishonest" onclick="openHonestyModal(${student.id}, 'dishonest')">
                        ❌ 不誠實
                    </button>
                    ${todayRecord ? `<button class="btn btn-sm btn-secondary" onclick="editRecord(${student.id}, '${today}')">編輯</button>` : ''}
                </td>
            `;
            
            honestyTableBody.appendChild(row);
        });
        
        displayPagination();
    }
    
    // 獲取學生誠實次數
    function getHonestyCount(studentId) {
        let count = 0;
        const studentData = honestyData[studentId];
        if (studentData) {
            for (const date in studentData) {
                if (studentData[date].type === 'honest') {
                    count++;
                }
            }
        }
        return count;
    }
    
    // 獲取學生不誠實次數
    function getDishonestyCount(studentId) {
        let count = 0;
        const studentData = honestyData[studentId];
        if (studentData) {
            for (const date in studentData) {
                if (studentData[date].type === 'dishonest') {
                    count++;
                }
            }
        }
        return count;
    }
    
    // 獲取學生誠實總分
    function getTotalHonestyScore(studentId) {
        let score = 0;
        const studentData = honestyData[studentId];
        if (studentData) {
            for (const date in studentData) {
                if (studentData[date].type === 'honest') {
                    score += HONESTY_SCORE;
                } else if (studentData[date].type === 'dishonest') {
                    score += DISHONESTY_PENALTY;
                }
            }
        }
        return score;
    }
    
    // 更新統計資訊
    function updateStatistics() {
        let todayHonest = 0;
        let todayDishonest = 0;
        let totalHonestyScore = 0;
        
        students.forEach(student => {
            const studentData = honestyData[student.id];
            if (studentData) {
                // 今日統計
                const todayRecord = studentData[today];
                if (todayRecord) {
                    if (todayRecord.type === 'honest') {
                        todayHonest++;
                    } else if (todayRecord.type === 'dishonest') {
                        todayDishonest++;
                    }
                }
                
                // 總分統計
                for (const date in studentData) {
                    if (studentData[date].type === 'honest') {
                        totalHonestyScore += HONESTY_SCORE;
                    } else if (studentData[date].type === 'dishonest') {
                        totalHonestyScore += DISHONESTY_PENALTY;
                    }
                }
            }
        });
        
        if (totalStudentsElement) totalStudentsElement.textContent = students.length;
        if (todayHonestElement) todayHonestElement.textContent = todayHonest;
        if (todayDishonestElement) todayDishonestElement.textContent = todayDishonest;
        if (totalHonestyScoreElement) totalHonestyScoreElement.textContent = totalHonestyScore >= 0 ? `+${totalHonestyScore}` : totalHonestyScore;
    }
    
    // 顯示分頁
    function displayPagination() {
        if (!paginationElement) return;
        
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        paginationElement.innerHTML = '';
        
        if (totalPages <= 1) return;
        
        // 上一頁按鈕
        const prevBtn = document.createElement('button');
        prevBtn.textContent = '‹';
        prevBtn.disabled = currentPage === 1;
        prevBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                displayStudents();
            }
        });
        paginationElement.appendChild(prevBtn);
        
        // 頁碼按鈕
        for (let i = 1; i <= totalPages; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.classList.toggle('active', i === currentPage);
            pageBtn.addEventListener('click', () => {
                currentPage = i;
                displayStudents();
            });
            paginationElement.appendChild(pageBtn);
        }
        
        // 下一頁按鈕
        const nextBtn = document.createElement('button');
        nextBtn.textContent = '›';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                displayStudents();
            }
        });
        paginationElement.appendChild(nextBtn);
    }
    
    // 開啟記錄彈窗
    window.openHonestyModal = function(studentId, type = null) {
        selectedStudent = students.find(s => s.id === studentId);
        selectedType = type;
        
        if (!selectedStudent) return;
        
        studentNameSpan.textContent = `${selectedStudent.animal} ${selectedStudent.name} (${selectedStudent.number})`;
        
        // 清除之前的選擇
        document.querySelectorAll('.honesty-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        // 如果有預設類型，自動選擇
        if (type) {
            const option = document.querySelector(`.honesty-option[data-type="${type}"]`);
            if (option) {
                option.classList.add('selected');
                confirmHonestyBtn.disabled = false;
            }
        } else {
            confirmHonestyBtn.disabled = true;
        }
        
        // 清空備註
        honestyNote.value = '';
        
        honestyModal.style.display = 'block';
    };
    
    // 編輯記錄
    window.editRecord = function(studentId, date) {
        selectedStudent = students.find(s => s.id === studentId);
        const record = honestyData[studentId] && honestyData[studentId][date];
        
        if (!selectedStudent || !record) return;
        
        editingRecord = { studentId, date };
        editSelectedType = record.type;
        
        editStudentNameSpan.textContent = `${selectedStudent.animal} ${selectedStudent.name} (${selectedStudent.number})`;
        editDateSpan.textContent = new Date(date).toLocaleDateString('zh-TW');
        
        // 清除之前的選擇
        document.querySelectorAll('.edit-honesty-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        // 選擇當前類型
        const option = document.querySelector(`.edit-honesty-option[data-type="${record.type}"]`);
        if (option) {
            option.classList.add('selected');
            confirmEditHonestyBtn.disabled = false;
        }
        
        // 設置備註
        editHonestyNote.value = record.note || '';
        
        editHonestyModal.style.display = 'block';
    };
    
    // 記錄誠實表現
    function recordHonesty() {
        if (!selectedStudent || !selectedType) return;
        
        const timestamp = new Date().toISOString();
        const note = honestyNote.value.trim();
        
        // 更新學生分數
        const scoreChange = selectedType === 'honest' ? HONESTY_SCORE : DISHONESTY_PENALTY;
        selectedStudent.score += scoreChange;
        
        // 記錄資料
        if (!honestyData[selectedStudent.id]) {
            honestyData[selectedStudent.id] = {};
        }
        
        honestyData[selectedStudent.id][today] = {
            type: selectedType,
            timestamp: timestamp,
            note: note,
            score: scoreChange
        };
        
        // 儲存資料
        saveStudents();
        saveHonestyData();
        
        // 更新顯示
        displayStudents();
        updateStatistics();
        
        // 關閉彈窗
        honestyModal.style.display = 'none';
        
        // 重置狀態
        selectedStudent = null;
        selectedType = null;
        
        // 顯示成功訊息
        showSuccessMessage(selectedType === 'honest' ? '誠實記錄已新增！' : '不誠實記錄已新增！');
    }
    
    // 全班標記為誠實
    function markAllAsHonest() {
        if (confirm('確定要將全班所有學生標記為誠實嗎？\n\n注意：這將會：\n- 為所有今日尚未記錄的學生加上誠實記錄\n- 每位學生獲得+5分\n- 已有記錄的學生不會重複記錄')) {
            let markedCount = 0;
            const timestamp = new Date().toISOString();
            
            students.forEach(student => {
                // 檢查今日是否已有記錄
                const hasRecord = honestyData[student.id] && honestyData[student.id][today];
                
                if (!hasRecord) {
                    // 更新學生分數
                    student.score += HONESTY_SCORE;
                    
                    // 記錄資料
                    if (!honestyData[student.id]) {
                        honestyData[student.id] = {};
                    }
                    
                    honestyData[student.id][today] = {
                        type: 'honest',
                        timestamp: timestamp,
                        note: '全班誠實記錄',
                        score: HONESTY_SCORE
                    };
                    
                    markedCount++;
                }
            });
            
            if (markedCount > 0) {
                // 儲存資料
                saveStudents();
                saveHonestyData();
                
                // 更新顯示
                displayStudents();
                updateStatistics();
                
                // 顯示成功訊息
                showSuccessMessage(`已為 ${markedCount} 位學生標記誠實記錄！`);
            } else {
                showSuccessMessage('所有學生今日都已有記錄了！');
            }
        }
    }
    
    // 重置所有誠實記錄
    function resetAllHonestyRecords() {
        const password = prompt('請輸入重置密碼：');
        if (password === '0718') {
            if (confirm('確定要重置所有誠實記錄嗎？這個操作無法復原！\n\n這將會：\n- 清除所有學生的誠實記錄\n- 恢復所有學生因誠實/不誠實獲得或被扣的分數\n- 清除所有誠實歷史記錄')) {
                try {
                    // 記錄重置前的統計
                    let totalRecords = 0;
                    let totalScoreAdjustment = 0;
                    
                    // 恢復所有學生的分數並清除記錄
                    students.forEach(student => {
                        const studentData = honestyData[student.id];
                        if (studentData) {
                            for (const date in studentData) {
                                const record = studentData[date];
                                // 恢復分數（反向操作）
                                student.score -= record.score;
                                totalScoreAdjustment += record.score;
                                totalRecords++;
                            }
                        }
                    });
                    
                    // 清除所有誠實記錄
                    honestyData = {};
                    
                    // 確保每個學生都有空的記錄結構
                    students.forEach(student => {
                        honestyData[student.id] = {};
                    });
                    
                    // 儲存更新後的資料
                    saveStudents();
                    saveHonestyData();
                    
                    // 更新顯示
                    displayStudents();
                    updateStatistics();
                    
                    // 顯示重置結果
                    const message = `重置完成！\n\n統計資訊：\n- 清除記錄數：${totalRecords} 筆\n- 調整分數：${totalScoreAdjustment > 0 ? '+' : ''}${totalScoreAdjustment} 分\n- 所有學生的誠實記錄已清空`;
                    alert(message);
                    
                    console.log('誠實記錄重置完成:', {
                        totalRecords,
                        totalScoreAdjustment,
                        timestamp: new Date().toISOString()
                    });
                    
                } catch (error) {
                    console.error('重置過程中發生錯誤:', error);
                    alert('重置過程中發生錯誤，請檢查瀏覽器控制台！');
                }
            }
        } else if (password !== null) { // 使用者有輸入密碼但錯誤
            alert('密碼錯誤！');
        }
    }
    
    // 更新記錄
    function updateRecord() {
        if (!editingRecord || !editSelectedType) return;
        
        const { studentId, date } = editingRecord;
        const student = students.find(s => s.id === studentId);
        const oldRecord = honestyData[studentId][date];
        
        if (!student || !oldRecord) return;
        
        // 恢復舊分數
        student.score -= oldRecord.score;
        
        // 添加新分數
        const newScore = editSelectedType === 'honest' ? HONESTY_SCORE : DISHONESTY_PENALTY;
        student.score += newScore;
        
        // 更新記錄
        honestyData[studentId][date] = {
            ...oldRecord,
            type: editSelectedType,
            note: editHonestyNote.value.trim(),
            score: newScore,
            updatedAt: new Date().toISOString()
        };
        
        // 儲存資料
        saveStudents();
        saveHonestyData();
        
        // 更新顯示
        displayStudents();
        updateStatistics();
        
        // 關閉彈窗
        editHonestyModal.style.display = 'none';
        
        // 重置狀態
        editingRecord = null;
        editSelectedType = null;
        
        // 顯示成功訊息
        showSuccessMessage('記錄已更新！');
    }
    
    // 顯示成功訊息
    function showSuccessMessage(message) {
        // 創建成功訊息元素
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        successDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #00b894, #55a3ff);
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            font-weight: 600;
            font-size: 1.1rem;
        `;
        
        document.body.appendChild(successDiv);
        
        // 2秒後移除訊息
        setTimeout(() => {
            document.body.removeChild(successDiv);
        }, 2000);
    }
    
    // 搜尋學生
    function searchStudents() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        
        if (searchTerm === '') {
            filteredStudents = [...students];
        } else {
            filteredStudents = students.filter(student =>
                student.name.toLowerCase().includes(searchTerm) ||
                student.number.toString().includes(searchTerm)
            );
        }
        
        currentPage = 1;
        displayStudents();
    }
    
    // 匯出資料
    function exportData() {
        const data = {
            students: students,
            honestyData: honestyData,
            exportDate: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `honesty-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    // 匯入資料
    function importData(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);
                
                if (data.students && data.honestyData) {
                    if (confirm('確定要匯入資料嗎？這將覆蓋現有資料！')) {
                        students = data.students;
                        honestyData = data.honestyData;
                        filteredStudents = [...students];
                        
                        saveStudents();
                        saveHonestyData();
                        
                        displayStudents();
                        updateStatistics();
                        
                        alert('資料匯入成功！');
                    }
                } else {
                    alert('檔案格式不正確！');
                }
            } catch (error) {
                alert('檔案讀取失敗！');
                console.error('Import error:', error);
            }
        };
        reader.readAsText(file);
        
        // 清空file input
        event.target.value = '';
    }
    
    // 匯出誠實記錄
    function exportHonestyRecords() {
        const records = [];
        
        // 收集所有記錄
        students.forEach(student => {
            const studentData = honestyData[student.id];
            if (studentData) {
                for (const date in studentData) {
                    const record = studentData[date];
                    records.push({
                        日期: date,
                        時間: new Date(record.timestamp).toLocaleTimeString('zh-TW'),
                        座號: student.number,
                        姓名: student.name,
                        動物: student.animal,
                        表現類型: record.type === 'honest' ? '誠實' : '不誠實',
                        分數: record.score,
                        備註: record.note || '',
                        記錄時間: record.timestamp
                    });
                }
            }
        });
        
        // 按日期排序
        records.sort((a, b) => new Date(b.記錄時間) - new Date(a.記錄時間));
        
        // 轉換為CSV
        if (records.length === 0) {
            alert('沒有記錄可以匯出！');
            return;
        }
        
        const headers = Object.keys(records[0]);
        const csvContent = [
            headers.join(','),
            ...records.map(record => headers.map(header => `"${record[header]}"`).join(','))
        ].join('\n');
        
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `honesty-records-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    // 設置事件監聽器
    function setupEventListeners() {
        // 搜尋功能
        if (searchInput) {
            searchInput.addEventListener('input', searchStudents);
        }
        
        // 全班誠實按鈕
        if (markAllHonestBtn) {
            markAllHonestBtn.addEventListener('click', markAllAsHonest);
        }
        
        // 重置記錄按鈕
        if (resetHonestyBtn) {
            resetHonestyBtn.addEventListener('click', resetAllHonestyRecords);
        }
        
        // 記錄選項點擊
        document.addEventListener('click', function(e) {
            if (e.target.closest('.honesty-option')) {
                const option = e.target.closest('.honesty-option');
                const type = option.dataset.type;
                
                // 移除其他選項的選中狀態
                document.querySelectorAll('.honesty-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // 選中當前選項
                option.classList.add('selected');
                selectedType = type;
                confirmHonestyBtn.disabled = false;
            }
            
            if (e.target.closest('.edit-honesty-option')) {
                const option = e.target.closest('.edit-honesty-option');
                const type = option.dataset.type;
                
                // 移除其他選項的選中狀態
                document.querySelectorAll('.edit-honesty-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // 選中當前選項
                option.classList.add('selected');
                editSelectedType = type;
                confirmEditHonestyBtn.disabled = false;
            }
        });
        
        // 確認按鈕
        if (confirmHonestyBtn) {
            confirmHonestyBtn.addEventListener('click', recordHonesty);
        }
        
        if (confirmEditHonestyBtn) {
            confirmEditHonestyBtn.addEventListener('click', updateRecord);
        }
        
        // 取消按鈕
        if (cancelHonestyBtn) {
            cancelHonestyBtn.addEventListener('click', () => {
                honestyModal.style.display = 'none';
            });
        }
        
        if (cancelEditHonestyBtn) {
            cancelEditHonestyBtn.addEventListener('click', () => {
                editHonestyModal.style.display = 'none';
            });
        }
        
        // 關閉彈窗
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const modal = btn.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });
        
        // 點擊彈窗外部關閉
        window.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
        
        // 資料管理按鈕
        if (exportBtn) {
            exportBtn.addEventListener('click', exportData);
        }
        
        if (importBtn) {
            importBtn.addEventListener('click', () => importFile.click());
        }
        
        if (exportHonestyBtn) {
            exportHonestyBtn.addEventListener('click', exportHonestyRecords);
        }
        
        if (importFile) {
            importFile.addEventListener('change', importData);
        }
    }
    
    // 初始化應用程式
    init();
});