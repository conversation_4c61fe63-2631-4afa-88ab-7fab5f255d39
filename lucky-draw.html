<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 幸運轉盤</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/lucky-draw.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li class="active"><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>
            <div class="data-management">
                <h3>📊 資料管理</h3>
                <button id="exportBtn" class="btn"><i class="fas fa-file-export"></i> 匯出資料</button>
                <button id="importBtn" class="btn"><i class="fas fa-file-import"></i> 匯入資料</button>
                <input type="file" id="importFile" accept=".json" style="display: none;">
            </div>
        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <h2><i class="fas fa-dharmachakra"></i> 幸運轉盤</h2>

            <div class="lucky-draw-container">
                <div class="student-selection">
                    <label for="studentSelect">選擇學生：</label>
                    <select id="studentSelect" class="form-control">
                        <option value="" disabled selected>-- 請選擇一位學生 --</option>
                        <!-- 學生選項將由 JavaScript 動態生成 -->
                    </select>
                </div>

                <div class="wheel-container">
                    <!-- 轉盤主體 -->
                    <div class="wheel-wrapper">
                        <!-- 小動物裝飾 -->
                        <div class="animals-decoration">
                            <div class="animal-icon" data-animal="rabbit" style="--angle: 0deg;">🐰</div>
                            <div class="animal-icon" data-animal="fox" style="--angle: 60deg;">🦊</div>
                            <div class="animal-icon" data-animal="cat" style="--angle: 120deg;">🐱</div>
                            <div class="animal-icon" data-animal="panda" style="--angle: 180deg;">🐼</div>
                            <div class="animal-icon" data-animal="bear" style="--angle: 240deg;">🐻</div>
                            <div class="animal-icon" data-animal="koala" style="--angle: 300deg;">🐨</div>
                        </div>

                        <div class="wheel" id="wheel">
                            <!-- 轉盤扇形區域將由 JavaScript 動態生成 -->
                        </div>

                        <!-- 轉盤中心點 -->
                        <div class="wheel-center">
                            <div class="center-circle">
                                <span class="center-text">LUCKY</span>
                            </div>
                        </div>

                        <!-- 指針 -->
                        <div class="wheel-pointer">
                            <div class="pointer-triangle">
                                <div class="go-inner-circle">GO</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <button id="spinBtn" class="btn btn-primary spin-btn" disabled>
                        <i class="fas fa-play"></i> 開始轉盤
                    </button>

                    <div class="spin-status" id="spinStatus">
                        請先選擇一位學生
                    </div>
                </div>

                <div class="result-container" id="resultContainer" style="display: none;">
                    <h3>轉盤結果</h3>
                    <p id="resultText"></p>
                </div>

                <div class="prize-info">
                    <h3>轉盤獎項與機率：</h3>
                    <div class="prize-grid">
                        <div class="prize-item">
                            <div class="prize-color" style="background: #ff6b6b;"></div>
                            <div class="prize-details">
                                <span class="prize-value">+10分</span>
                                <span class="prize-probability">10%</span>
                            </div>
                        </div>
                        <div class="prize-item">
                            <div class="prize-color" style="background: #4ecdc4;"></div>
                            <div class="prize-details">
                                <span class="prize-value">+5分</span>
                                <span class="prize-probability">20%</span>
                            </div>
                        </div>
                        <div class="prize-item">
                            <div class="prize-color" style="background: #45b7d1;"></div>
                            <div class="prize-details">
                                <span class="prize-value">+1分</span>
                                <span class="prize-probability">50%</span>
                            </div>
                        </div>
                        <div class="prize-item">
                            <div class="prize-color" style="background: #f9ca24;"></div>
                            <div class="prize-details">
                                <span class="prize-value">-1分</span>
                                <span class="prize-probability">20%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 結果彈窗 -->
    <div id="resultModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>轉盤結果</h3>
            <div class="result-content" id="modalResultContent">
                <!-- 結果內容將由 JavaScript 動態生成 -->
            </div>
            <div class="modal-buttons">
                <button id="confirmResultBtn" class="btn btn-primary">確定</button>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script src="js/lucky-draw.js"></script>
</body>
</html>
