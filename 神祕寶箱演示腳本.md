# 🎁 神祕寶箱功能演示腳本

## 演示準備

### 1. 開啟測試頁面
- 打開 `mystery-box-test.html` 進行功能測試
- 打開 `manual-score.html` 進行實際操作演示

### 2. 檢查測試資料
- 確認有多個測試學生
- 確認學生有不同的分數
- 重置測試資料（如需要）

## 演示流程

### 第一部分：功能介紹 (2分鐘)

**腳本：**
"歡迎來到記分小達人系統的神祕寶箱功能演示。神祕寶箱是一個隨機事件系統，當學生獲得加分時會自動觸發，為學習過程增添趣味性。

系統包含五種不同的隨機事件：
- 🍀 強運：分數翻倍
- 💰 掠奪：偷取其他學生的分數
- 😇 天使：獲得保護狀態
- 😈 惡魔：分數減半
- 🔄 交換：與其他學生交換分數"

**操作：**
1. 展示測試頁面的事件說明區域
2. 點擊各個事件卡片展示顏色和圖示

### 第二部分：基本觸發演示 (3分鐘)

**腳本：**
"現在讓我們看看神祕寶箱是如何觸發的。當我們為學生加分時，系統會自動檢測並顯示神祕寶箱彈窗。"

**操作：**
1. 在 `manual-score.html` 中選擇一個學生
2. 使用快速加分按鈕（+5分）
3. 展示神祕寶箱彈窗出現
4. 點擊「開啟寶箱」按鈕
5. 觀看開箱動畫
6. 展示隨機事件結果

**重點說明：**
- 彈窗的視覺設計
- 開箱動畫效果
- 結果顯示方式

### 第三部分：各種事件效果演示 (5分鐘)

#### 3.1 強運事件
**腳本：**
"強運事件會讓學生獲得的分數翻倍。這是最受歡迎的事件之一。"

**操作：**
1. 在測試頁面點擊「強運」事件卡片
2. 展示分數翻倍效果
3. 檢查分數歷史記錄

#### 3.2 掠奪事件
**腳本：**
"掠奪事件會從其他隨機學生身上偷取5分。這增加了競爭的趣味性。"

**操作：**
1. 確保有其他學生分數≥5分
2. 觸發掠奪事件
3. 展示分數變化
4. 指出受影響的學生

#### 3.3 天使事件
**腳本：**
"天使事件會給學生一個特殊的保護狀態，可以免除一次扣分。"

**操作：**
1. 觸發天使事件
2. 展示學生卡片上的天使圖示
3. 說明保護狀態的作用

#### 3.4 惡魔事件
**腳本：**
"惡魔事件會讓學生的分數減半。這是一個風險事件，增加了不確定性。"

**操作：**
1. 選擇分數較高的學生
2. 觸發惡魔事件
3. 展示分數減半效果

#### 3.5 交換事件
**腳本：**
"交換事件會讓兩個學生的分數完全交換。這可能帶來意想不到的結果。"

**操作：**
1. 記錄兩個學生的原始分數
2. 觸發交換事件
3. 展示分數交換結果

### 第四部分：進階功能演示 (3分鐘)

#### 4.1 批量操作觸發
**腳本：**
"神祕寶箱也支援批量操作。當我們為多個學生同時加分時，系統會為第一個學生觸發神祕寶箱。"

**操作：**
1. 在手動加減分頁面選擇批量操作
2. 選擇多個學生
3. 設定加分數值
4. 確認操作並觀察神祕寶箱觸發

#### 4.2 歷史記錄查看
**腳本：**
"所有神祕寶箱事件都會詳細記錄在操作歷史中，方便追蹤和管理。"

**操作：**
1. 點擊「操作記錄」按鈕
2. 展示神祕寶箱相關的歷史記錄
3. 說明記錄的詳細資訊

#### 4.3 特殊狀態管理
**腳本：**
"系統會自動管理學生的特殊狀態，如天使保護。這些狀態會在學生卡片上清楚顯示。"

**操作：**
1. 展示有天使保護的學生卡片
2. 說明狀態圖示的含義
3. 展示狀態的動畫效果

### 第五部分：技術特點說明 (2分鐘)

**腳本：**
"神祕寶箱功能具有以下技術特點：

1. **完全隨機**：每次觸發都是真正的隨機事件
2. **數據安全**：所有變更都會正確保存到本地存儲
3. **視覺豐富**：包含動畫、特效和音效支援
4. **響應式設計**：支援各種設備和螢幕大小
5. **完整記錄**：所有事件都有詳細的歷史記錄"

**操作：**
1. 展示不同設備上的顯示效果（如果可能）
2. 展示動畫和特效
3. 檢查本地存儲的數據

### 第六部分：使用建議 (1分鐘)

**腳本：**
"建議在以下情況使用神祕寶箱功能：

1. **課堂互動**：增加學習的趣味性
2. **獎勵機制**：讓加分更有期待感
3. **競爭遊戲**：增加不確定性和刺激感
4. **特殊活動**：在特定時間開啟此功能

記住，神祕寶箱是可選功能，可以根據需要開啟或關閉。"

## 演示總結

### 重點回顧
1. 神祕寶箱增加了學習的趣味性
2. 五種不同事件提供豐富的體驗
3. 完整的技術實現確保功能穩定
4. 詳細的記錄系統便於管理

### 後續步驟
1. 可以根據需要調整事件機率
2. 可以添加更多事件類型
3. 可以整合音效和背景音樂
4. 可以添加統計和分析功能

## 常見問題解答

**Q: 神祕寶箱會影響原有功能嗎？**
A: 不會。神祕寶箱是額外功能，不會影響原有的加減分邏輯。

**Q: 可以關閉神祕寶箱功能嗎？**
A: 可以。只需要在代碼中註釋掉觸發函數即可。

**Q: 事件機率可以調整嗎？**
A: 可以。通過修改事件選擇邏輯可以調整各事件的機率。

**Q: 數據會丟失嗎？**
A: 不會。所有變更都會正確保存到本地存儲中。
