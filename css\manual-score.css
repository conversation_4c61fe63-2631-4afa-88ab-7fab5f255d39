/* 手動加減分頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.quick-actions {
    display: flex;
    gap: 10px;
}

/* 快速操作區域 */
.quick-score-section {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 25px;
}

.quick-score-section h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.quick-score-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.quick-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px solid transparent;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.quick-btn.positive {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
    border-color: #00b894;
}

.quick-btn.positive:hover {
    background: linear-gradient(135deg, #00a085, #008f76);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 184, 148, 0.3);
}

.quick-btn.negative {
    background: linear-gradient(135deg, #ff7675, #e84393);
    color: white;
    border-color: #ff7675;
}

.quick-btn.negative:hover {
    background: linear-gradient(135deg, #e84393, #d63384);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 118, 117, 0.3);
}

.quick-btn.active {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* 過濾區域 */
.filter-section {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    align-items: center;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid #ddd;
    border-radius: 50px;
    font-size: 1rem;
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2);
}

.form-control {
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    min-width: 150px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2);
}

/* 學生網格 */
.students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.student-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.student-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.student-card.quick-mode {
    border: 2px dashed var(--primary-color);
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.05), rgba(108, 92, 231, 0.1));
}

.student-card.quick-mode:hover {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(108, 92, 231, 0.15));
    border-style: solid;
}

.student-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 1.5rem;
}

.student-info {
    text-align: center;
}

.student-info h4 {
    margin-bottom: 8px;
    color: var(--dark-color);
    font-size: 1.1rem;
}

.student-info p {
    margin: 4px 0;
    color: #666;
    font-size: 0.9rem;
}

.score-display {
    margin: 15px 0;
    padding: 10px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.score-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
}

.score-label {
    font-size: 1rem;
    color: #666;
}

.student-actions {
    margin-top: 15px;
}

.quick-score-indicator {
    background: var(--primary-color);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    text-align: center;
    font-weight: 500;
}

/* 彈窗樣式增強 */
.modal-content.large {
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

/* 學生資訊區域 */
.student-profile {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.student-profile .student-avatar {
    width: 50px;
    height: 50px;
    margin: 0;
    font-size: 1.2rem;
}

.student-details h4 {
    margin: 0 0 5px 0;
    color: var(--primary-color);
}

.student-details p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* 分數輸入組 */
.score-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
}

.score-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1.2rem;
}

.score-btn.plus {
    background: var(--success-color);
    color: white;
}

.score-btn.plus:hover {
    background: #00a085;
    transform: scale(1.1);
}

.score-btn.minus {
    background: var(--danger-color);
    color: white;
}

.score-btn.minus:hover {
    background: #e74c3c;
    transform: scale(1.1);
}

.score-input-group input {
    width: 80px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
}

/* 分數預覽 */
.score-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

.current-score, .new-score {
    text-align: center;
}

.current-score span, .new-score span {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    margin-top: 5px;
}

.arrow {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* 批量操作樣式 */
.batch-selection {
    margin-bottom: 25px;
}

.batch-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.batch-student-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
}

.batch-student-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.batch-student-item:last-child {
    border-bottom: none;
}

.batch-student-item label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.9rem;
}

.batch-student-item input[type="checkbox"] {
    margin: 0;
}

.batch-action {
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* 操作記錄樣式 */
.history-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 10px;
    background: white;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.student-name {
    font-weight: bold;
    color: var(--primary-color);
}

.history-date {
    font-size: 0.8rem;
    color: #999;
}

.history-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.score-change {
    font-weight: bold;
}

.score-change.positive {
    color: var(--success-color);
}

.score-change.negative {
    color: var(--danger-color);
}

.reason {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #666;
}

.history-note {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
    margin-top: 5px;
    padding-top: 5px;
    border-top: 1px solid #f0f0f0;
}

.empty-history {
    text-align: center;
    padding: 40px;
    color: #999;
    font-style: italic;
}

/* 空狀態 */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i {
    display: block;
    margin-bottom: 15px;
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    min-width: 40px;
    text-align: center;
}

.page-btn:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.ellipsis {
    padding: 8px 4px;
    color: #999;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .quick-actions {
        justify-content: center;
    }
    
    .quick-score-buttons {
        justify-content: center;
    }
    
    .filter-section {
        flex-direction: column;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .students-grid {
        grid-template-columns: 1fr;
    }
    
    .score-preview {
        flex-direction: column;
        gap: 10px;
    }
    
    .arrow {
        transform: rotate(90deg);
    }
    
    .history-filters {
        flex-direction: column;
    }
    
    .batch-controls {
        flex-wrap: wrap;
    }

    .mystery-box-content {
        max-width: 90vw;
        margin: 20px;
    }

    .mystery-box-actions {
        flex-direction: column;
        gap: 10px;
    }

    .mystery-open-btn,
    .mystery-skip-btn {
        width: 100%;
    }
}

/* 神祕寶箱樣式 */
.mystery-box-modal {
    z-index: 2000;
}

.mystery-box-content {
    max-width: 500px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.mystery-box-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.3)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.4)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.2)"/></svg>');
    animation: sparkle 3s infinite;
    pointer-events: none;
}

@keyframes sparkle {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.mystery-box-header h3 {
    margin-bottom: 10px;
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.mystery-box-animation {
    margin: 30px 0;
    position: relative;
}

.treasure-box {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    position: relative;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.treasure-box:hover {
    transform: scale(1.1);
}

.box-body {
    width: 80px;
    height: 50px;
    background: linear-gradient(135deg, #8B4513, #A0522D);
    border-radius: 8px;
    position: absolute;
    bottom: 0;
    border: 3px solid #654321;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.box-lid {
    width: 84px;
    height: 25px;
    background: linear-gradient(135deg, #DAA520, #FFD700);
    border-radius: 8px 8px 4px 4px;
    position: absolute;
    top: 0;
    left: -2px;
    border: 3px solid #B8860B;
    transition: transform 0.5s ease;
    transform-origin: bottom;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.treasure-box.opening .box-lid {
    transform: rotateX(-45deg) translateY(-10px);
}

.sparkles {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.treasure-box.opening .sparkles {
    opacity: 1;
    animation: sparkleEffect 1s ease-out;
}

@keyframes sparkleEffect {
    0% {
        transform: translateX(-50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) scale(1);
        opacity: 0.8;
    }
}

.mystery-box-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.mystery-open-btn {
    background: linear-gradient(135deg, #00b894, #00a085);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 184, 148, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 184, 148, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 184, 148, 0); }
}

.mystery-open-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 184, 148, 0.4);
}

.mystery-skip-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.mystery-skip-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 神祕寶箱結果樣式 */
.mystery-box-result {
    padding: 20px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.mystery-box-result.result-show {
    opacity: 1;
    transform: translateY(0);
}

.event-icon {
    font-size: 4rem;
    margin-bottom: 15px;
    animation: bounceIn 0.8s ease;
}

@keyframes bounceIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.event-title {
    font-size: 1.8rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.event-description {
    font-size: 1rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

.result-message {
    background: rgba(255, 255, 255, 0.2);
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
    font-size: 1.1rem;
    font-weight: 600;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.affected-students {
    margin-top: 10px;
    font-style: italic;
    opacity: 0.8;
}

.result-actions {
    margin-top: 25px;
}

.close-result-btn {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.close-result-btn:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 特殊狀態樣式 */
.angel-protection {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: angelGlow 2s infinite alternate;
}

@keyframes angelGlow {
    0% { box-shadow: 0 2px 4px rgba(0,0,0,0.2), 0 0 0 0 rgba(116, 185, 255, 0.4); }
    100% { box-shadow: 0 2px 4px rgba(0,0,0,0.2), 0 0 0 8px rgba(116, 185, 255, 0); }
}

.special-status {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-top: 8px;
    display: inline-block;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.angel-status {
    animation: angelPulse 2s infinite;
}

@keyframes angelPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}
