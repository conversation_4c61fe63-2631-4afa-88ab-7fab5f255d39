/* 幸運轉盤頁面樣式 */

.lucky-draw-container {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
}

.student-selection {
    margin-bottom: 40px;
}

.student-selection label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.form-control {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 25px;
    font-size: 1rem;
    transition: var(--transition);
    background: white;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2);
}

/* 轉盤容器 */
.wheel-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px auto;
    padding: 20px;
}

.wheel-wrapper {
    position: relative;
    width: 400px;
    height: 400px;
}

/* 小動物裝飾 */
.animals-decoration {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 5;
}

.animal-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    font-size: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 3px solid #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: float 3s ease-in-out infinite;

    /* 使用 CSS 變數來定位動物 */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
               rotate(var(--angle))
               translateY(-220px)
               rotate(calc(-1 * var(--angle)));
}

.animal-icon:hover {
    transform: translate(-50%, -50%)
               rotate(var(--angle))
               translateY(-220px)
               rotate(calc(-1 * var(--angle)))
               scale(1.2);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    background: rgba(255, 255, 255, 1);
}

/* 浮動動畫 */
@keyframes float {
    0%, 100% {
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-220px)
                   rotate(calc(-1 * var(--angle)))
                   translateY(0px);
    }
    50% {
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-220px)
                   rotate(calc(-1 * var(--angle)))
                   translateY(-8px);
    }
}

/* 為每個動物設定不同的動畫延遲 */
.animal-icon[data-animal="rabbit"] { animation-delay: 0s; }
.animal-icon[data-animal="fox"] { animation-delay: 0.5s; }
.animal-icon[data-animal="cat"] { animation-delay: 1s; }
.animal-icon[data-animal="panda"] { animation-delay: 1.5s; }
.animal-icon[data-animal="bear"] { animation-delay: 2s; }
.animal-icon[data-animal="koala"] { animation-delay: 2.5s; }

/* 小動物點擊動畫 */
@keyframes animalClick {
    0% {
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-220px)
                   rotate(calc(-1 * var(--angle)))
                   scale(1);
    }
    50% {
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-220px)
                   rotate(calc(-1 * var(--angle)))
                   scale(1.4) rotate(15deg);
    }
    100% {
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-220px)
                   rotate(calc(-1 * var(--angle)))
                   scale(1);
    }
}

/* 提示動畫 */
@keyframes tooltipFade {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
    30% {
        transform: translate(-50%, -50%) scale(1);
    }
    70% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

/* 轉盤主體 */
.wheel {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: relative;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    transition: transform 4s cubic-bezier(0.17, 0.67, 0.12, 0.99);
    border: 8px solid #fff;
    background: conic-gradient(
        from 0deg,
        #ff6b6b 0deg 36deg,
        #4ecdc4 36deg 108deg,
        #45b7d1 108deg 288deg,
        #f9ca24 288deg 360deg
    );
    display: flex;
    align-items: center;
    justify-content: center;
}



/* 轉盤中心 */
.wheel-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 35;
}

.center-circle {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    border: 6px solid white;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
}

.center-text {
    font-weight: bold;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

/* 轉盤指針 */
.wheel-pointer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 30;
    width: 100px;
    height: 100px;
    pointer-events: none;
}

.pointer-triangle {
    position: relative;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 指針頂部三角形 */
.pointer-triangle::before {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 25px solid #ffd700;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    z-index: 1;
}

/* GO 按鈕主體 - 黃色外圈 */
.pointer-triangle::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
    border: 3px solid #ffd700;
    border-radius: 50%;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.2),
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        inset 0 -2px 4px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

/* 內圈紅色圓圈 */
.go-inner-circle {
    position: absolute;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff4444 0%, #ff6666 50%, #ff4444 100%);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        inset 0 -2px 4px rgba(0, 0, 0, 0.2);
    z-index: 3;
}

/* 控制區域 */
.control-section {
    margin: 30px 0;
}

.spin-btn {
    font-size: 1.3rem;
    padding: 18px 40px;
    border-radius: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(108, 92, 231, 0.3);
    margin-bottom: 15px;
}

.spin-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(108, 92, 231, 0.4);
}

.spin-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.spin-status {
    font-size: 1rem;
    color: #666;
    font-style: italic;
}

/* 結果容器 */
.result-container {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 30px auto;
    max-width: 450px;
}

.result-container h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

/* 獎項資訊 */
.prize-info {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 40px auto;
    max-width: 600px;
}

.prize-info h3 {
    color: var(--primary-color);
    margin-bottom: 25px;
    text-align: center;
}

.prize-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 20px;
}

.prize-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: var(--transition);
}

.prize-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.prize-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
}

.prize-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.prize-value {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1rem;
}

.prize-probability {
    font-size: 0.85rem;
    color: #666;
}

/* 旋轉動畫 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.wheel.spinning {
    animation: spin 0.1s linear infinite;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .wheel-wrapper {
        width: 320px;
        height: 320px;
    }

    .animal-icon {
        width: 50px;
        height: 50px;
        font-size: 2rem;
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-175px)
                   rotate(calc(-1 * var(--angle)));
    }

    .animal-icon:hover {
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-175px)
                   rotate(calc(-1 * var(--angle)))
                   scale(1.2);
    }

    @keyframes float {
        0%, 100% {
            transform: translate(-50%, -50%)
                       rotate(var(--angle))
                       translateY(-175px)
                       rotate(calc(-1 * var(--angle)))
                       translateY(0px);
        }
        50% {
            transform: translate(-50%, -50%)
                       rotate(var(--angle))
                       translateY(-175px)
                       rotate(calc(-1 * var(--angle)))
                       translateY(-6px);
        }
    }

    .center-circle {
        width: 60px;
        height: 60px;
        font-size: 1.4rem;
    }

    .center-text {
        font-size: 0.7rem;
    }

    .wheel-pointer {
        width: 80px;
        height: 80px;
    }

    .pointer-triangle {
        width: 80px;
        height: 80px;
    }

    .pointer-triangle::before {
        top: -12px;
        border-left-width: 12px;
        border-right-width: 12px;
        border-bottom-width: 20px;
    }

    .pointer-triangle::after {
        width: 64px;
        height: 64px;
    }

    .go-inner-circle {
        width: 48px;
        height: 48px;
        font-size: 0.9rem;
    }

    .wheel-segment-content i {
        font-size: 1.4rem;
    }

    .wheel-segment-content span {
        font-size: 0.9rem;
    }

    .spin-btn {
        font-size: 1.1rem;
        padding: 15px 30px;
    }

    .prize-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .wheel-wrapper {
        width: 280px;
        height: 280px;
    }

    .animal-icon {
        width: 40px;
        height: 40px;
        font-size: 1.6rem;
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-150px)
                   rotate(calc(-1 * var(--angle)));
    }

    .animal-icon:hover {
        transform: translate(-50%, -50%)
                   rotate(var(--angle))
                   translateY(-150px)
                   rotate(calc(-1 * var(--angle)))
                   scale(1.2);
    }

    @keyframes float {
        0%, 100% {
            transform: translate(-50%, -50%)
                       rotate(var(--angle))
                       translateY(-150px)
                       rotate(calc(-1 * var(--angle)))
                       translateY(0px);
        }
        50% {
            transform: translate(-50%, -50%)
                       rotate(var(--angle))
                       translateY(-150px)
                       rotate(calc(-1 * var(--angle)))
                       translateY(-5px);
        }
    }

    .wheel-pointer {
        width: 70px;
        height: 70px;
    }

    .pointer-triangle {
        width: 70px;
        height: 70px;
    }

    .pointer-triangle::before {
        top: -10px;
        border-left-width: 10px;
        border-right-width: 10px;
        border-bottom-width: 18px;
    }

    .pointer-triangle::after {
        width: 56px;
        height: 56px;
    }

    .go-inner-circle {
        width: 42px;
        height: 42px;
        font-size: 0.8rem;
    }

    .prize-grid {
        grid-template-columns: 1fr;
    }
}
