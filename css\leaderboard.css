/* 榮譽榜頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.controls-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-end;
}

/* 時段選擇器 */
.period-selector {
    display: flex;
    gap: 8px;
    background: white;
    padding: 6px;
    border-radius: 50px;
    box-shadow: var(--box-shadow);
    flex-wrap: wrap;
}

.period-btn {
    border: none;
    background: transparent;
    padding: 10px 16px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 6px;
}

.period-btn:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

.period-btn.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 8px rgba(108, 92, 231, 0.3);
}

.period-btn i {
    font-size: 0.85em;
}

.view-options {
    display: flex;
    gap: 10px;
    background: white;
    padding: 5px;
    border-radius: 50px;
    box-shadow: var(--box-shadow);
}

.view-options .btn {
    border-radius: 50px;
    padding: 8px 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.view-options .btn.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 8px rgba(108, 92, 231, 0.3);
}

/* 時段資訊區域 */
.period-info {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 30px;
}

.period-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-card i {
    font-size: 2rem;
    color: var(--primary-color);
    width: 40px;
    text-align: center;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* 獎台樣式 */
.podium-container {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    gap: 8px;
    margin: 40px 0 60px;
    height: 300px;
    flex-wrap: wrap;
}

.podium-place {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 18%;
    max-width: 160px;
    min-width: 120px;
}

.podium-place.first {
    height: 100%;
}

.podium-place.second {
    height: 80%;
}

.podium-place.third {
    height: 65%;
}

.podium-place.fourth {
    height: 50%;
}

.podium-place.fifth {
    height: 40%;
}

.podium-stand {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    padding-top: 20px;
    border-radius: 10px 10px 0 0;
    background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.podium-place.first .podium-stand {
    background: linear-gradient(180deg, #ffd700 0%, #ffc107 100%);
    z-index: 3;
}

.podium-place.second .podium-stand {
    background: linear-gradient(180deg, #c0c0c0 0%, #a0a0a0 100%);
    z-index: 2;
}

.podium-place.third .podium-stand {
    background: linear-gradient(180deg, #cd7f32 0%, #b87333 100%);
    z-index: 1;
}

.podium-place.fourth .podium-stand {
    background: linear-gradient(180deg, #87ceeb 0%, #6bb6ff 100%);
    z-index: 1;
}

.podium-place.fifth .podium-stand {
    background: linear-gradient(180deg, #dda0dd 0%, #ba55d3 100%);
    z-index: 1;
}

.podium-rank {
    position: absolute;
    top: -25px;
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--dark-color);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    z-index: 4;
}

.podium-avatar {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.podium-avatar i {
    font-size: 2rem;
    color: #6c757d;
}

.podium-place.first .podium-avatar {
    width: 90px;
    height: 90px;
    margin-top: -30px;
    border: 4px solid #ffd700;
    background: white;
}

.podium-place.first .podium-avatar i {
    color: #ffd700;
    font-size: 2.5rem;
}

.podium-place.second .podium-avatar {
    border: 3px solid #c0c0c0;
}

.podium-place.second .podium-avatar i {
    color: #a0a0a0;
}

.podium-place.third .podium-avatar {
    border: 3px solid #cd7f32;
}

.podium-place.third .podium-avatar i {
    color: #b87333;
}

.podium-place.fourth .podium-avatar {
    border: 2px solid #87ceeb;
}

.podium-place.fourth .podium-avatar i {
    color: #6bb6ff;
}

.podium-place.fifth .podium-avatar {
    border: 2px solid #dda0dd;
}

.podium-place.fifth .podium-avatar i {
    color: #ba55d3;
}

.podium-info {
    text-align: center;
    margin-top: 10px;
    padding: 0 10px;
    width: 100%;
}

.podium-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #343a40;
}

.podium-score {
    font-size: 1.2rem;
    font-weight: bold;
    color: #495057;
}

.podium-place.first .podium-score {
    color: #d4af37;
    font-size: 1.4rem;
}

/* 排行榜容器 */
.leaderboard-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-bottom: 30px;
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.leaderboard-filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 50px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2);
}

/* 表格樣式 */
#leaderboardTable {
    width: 100%;
    border-collapse: collapse;
}

#leaderboardTable th,
#leaderboardTable td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

#leaderboardTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
    white-space: nowrap;
}

#leaderboardTable tbody tr {
    transition: all 0.2s ease;
}

#leaderboardTable tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

/* 排名樣式 */
.rank-cell {
    font-weight: bold;
    text-align: center;
    width: 60px;
}

.rank-1 {
    color: #ffd700;
    font-size: 1.2rem;
}

.rank-2 {
    color: #c0c0c0;
    font-size: 1.1rem;
}

.rank-3 {
    color: #cd7f32;
    font-size: 1.1rem;
}

.rank-4 {
    color: #87ceeb;
    font-size: 1rem;
}

.rank-5 {
    color: #dda0dd;
    font-size: 1rem;
}

/* 獎章樣式 */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    color: white;
    font-size: 0.9rem;
    margin-right: 5px;
}

.badge i {
    font-size: 0.9em;
}

.gold {
    background: linear-gradient(135deg, #ffd700, #ffa500);
    box-shadow: 0 3px 6px rgba(255, 215, 0, 0.3);
}

.silver {
    background: linear-gradient(135deg, #c0c0c0, #a0a0a0);
    box-shadow: 0 3px 6px rgba(192, 192, 192, 0.3);
}

.bronze {
    background: linear-gradient(135deg, #cd7f32, #b87333);
    box-shadow: 0 3px 6px rgba(205, 127, 50, 0.3);
}

.star {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
    box-shadow: 0 3px 6px rgba(108, 92, 231, 0.3);
}

.fire {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
    box-shadow: 0 3px 6px rgba(255, 118, 117, 0.3);
}

.fourth {
    background: linear-gradient(135deg, #87ceeb, #6bb6ff);
    box-shadow: 0 3px 6px rgba(135, 206, 235, 0.3);
}

.fifth {
    background: linear-gradient(135deg, #dda0dd, #ba55d3);
    box-shadow: 0 3px 6px rgba(221, 160, 221, 0.3);
}

/* 獎章說明區域 */
.badges-info {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-top: 30px;
}

.badges-info h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.badges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 20px;
}

.badge-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.badge-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.badge-item .badge {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.badge-item span {
    font-size: 0.9rem;
    color: #555;
    text-align: center;
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
    flex-wrap: wrap;
}

.pagination button {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
    background-color: #f0f0f0;
    border-color: #ccc;
}

.pagination button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 學生詳情彈窗 */
.student-details {
    padding: 15px 0;
}

.student-profile {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    margin-bottom: 25px;
}

.student-profile .student-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0;
}

.student-info h4 {
    margin: 0 0 8px 0;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.student-info p {
    margin: 0;
    color: #666;
    font-size: 0.95rem;
}

.student-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    transition: var(--transition);
}

.stat-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.stat-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-value {
    display: block;
    font-size: 1.4rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-top: 2px;
}

.badges-earned {
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.badges-earned h5 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.badges-list {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.badges-list .badge {
    margin-right: 0;
}

.student-detail-item {
    display: flex;
    margin-bottom: 15px;
    align-items: center;
}

.student-detail-label {
    font-weight: 600;
    width: 100px;
    color: #666;
}

.student-detail-value {
    flex: 1;
}

/* 響應式設計 */
@media (max-width: 1024px) {
    .podium-place {
        width: 19%;
        min-width: 90px;
    }

    .podium-name {
        font-size: 0.95rem;
    }

    .podium-score {
        font-size: 1rem;
    }

    .podium-place.first .podium-score {
        font-size: 1.2rem;
    }
}

@media (max-width: 992px) {
    .podium-container {
        height: auto;
        flex-direction: column;
        align-items: center;
        gap: 0;
    }
    
    .podium-place {
        width: 80%;
        max-width: 300px;
        margin-bottom: 20px;
    }
    
    .podium-place.first {
        order: 1;
    }
    
    .podium-place.second {
        order: 2;
    }
    
    .podium-place.third {
        order: 3;
    }

    .podium-place.fourth {
        order: 4;
    }

    .podium-place.fifth {
        order: 5;
    }
    
    .leaderboard-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .leaderboard-filters {
        width: 100%;
    }
    
    .search-box {
        flex: 1;
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .controls-container {
        width: 100%;
        align-items: stretch;
    }

    .period-selector {
        justify-content: center;
        padding: 4px;
    }

    .period-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .view-options {
        width: 100%;
        justify-content: center;
    }

    .period-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-card i {
        font-size: 1.5rem;
        width: 30px;
    }

    .stat-value {
        font-size: 1.1rem;
    }
    
    .leaderboard-filters {
        flex-direction: column;
        gap: 10px;
    }
    
    .search-box {
        width: 100%;
    }
    
    #leaderboardTable th,
    #leaderboardTable td {
        padding: 10px 5px;
        font-size: 0.9rem;
    }
    
    .badges-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}
