<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匯入匯出功能測試</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a4fcf;
        }
        .nav-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .nav-links a {
            background: #6c5ce7;
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #5a4fcf;
        }
        .data-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
        .function-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .function-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .function-card h4 {
            margin: 0 0 10px 0;
            color: #6c5ce7;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>匯入匯出功能測試</h1>
    
    <div class="nav-links">
        <a href="index.html">學生名單</a>
        <a href="lucky-draw.html">幸運轉盤</a>
        <a href="daily-checkin.html">每日簽到</a>
        <a href="leaderboard.html">榮譽榜</a>
        <a href="manual-score.html">手動加減分</a>
        <a href="test.html">系統測試</a>
    </div>
    
    <div class="test-section">
        <h2>📊 本地儲存資料檢查</h2>
        <div id="storageCheck"></div>
        <button onclick="checkLocalStorage()">檢查本地資料</button>
        <button onclick="clearLocalStorage()">清除本地資料</button>
    </div>
    
    <div class="test-section">
        <h2>📤 匯出功能測試</h2>
        <div class="function-test">
            <div class="function-card">
                <h4>學生名單匯出</h4>
                <button onclick="testExport()">測試匯出</button>
            </div>
            <div class="function-card">
                <h4>建立測試資料</h4>
                <button onclick="createTestData()">建立測試學生</button>
            </div>
        </div>
        <div id="exportResult"></div>
    </div>
    
    <div class="test-section">
        <h2>📥 匯入功能測試</h2>
        <div>
            <h4>選擇 JSON 檔案匯入：</h4>
            <input type="file" id="testImportFile" accept=".json">
            <button onclick="testImport()">測試匯入</button>
        </div>
        <div id="importResult"></div>
    </div>
    
    <div class="test-section">
        <h2>🔄 跨頁面功能測試</h2>
        <p>測試各頁面的匯入匯出按鈕是否正常運作：</p>
        <div class="function-test">
            <div class="function-card">
                <h4>學生名單頁面</h4>
                <a href="index.html" target="_blank">測試頁面</a>
            </div>
            <div class="function-card">
                <h4>幸運轉盤頁面</h4>
                <a href="lucky-draw.html" target="_blank">測試頁面</a>
            </div>
            <div class="function-card">
                <h4>每日簽到頁面</h4>
                <a href="daily-checkin.html" target="_blank">測試頁面</a>
            </div>
            <div class="function-card">
                <h4>榮譽榜頁面</h4>
                <a href="leaderboard.html" target="_blank">測試頁面</a>
            </div>
            <div class="function-card">
                <h4>手動加減分頁面</h4>
                <a href="manual-score.html" target="_blank">測試頁面</a>
            </div>
        </div>
    </div>

    <script>
        // 檢查本地儲存
        function checkLocalStorage() {
            const container = document.getElementById('storageCheck');
            let html = '<h3>本地儲存檢查結果：</h3>';
            
            try {
                const students = localStorage.getItem('students');
                const scoreHistory = localStorage.getItem('scoreHistory');
                
                if (students) {
                    const studentData = JSON.parse(students);
                    html += `<div class="test-result success">
                        ✅ 學生資料: 存在 (${studentData.length} 筆)
                    </div>`;
                    
                    html += '<div class="data-preview">';
                    html += '<strong>學生資料預覽：</strong><br>';
                    html += JSON.stringify(studentData.slice(0, 3), null, 2);
                    if (studentData.length > 3) {
                        html += `<br>... 還有 ${studentData.length - 3} 筆資料`;
                    }
                    html += '</div>';
                } else {
                    html += '<div class="test-result info">📝 學生資料: 不存在</div>';
                }
                
                if (scoreHistory) {
                    const historyData = JSON.parse(scoreHistory);
                    html += `<div class="test-result success">
                        ✅ 操作記錄: 存在 (${historyData.length} 筆)
                    </div>`;
                } else {
                    html += '<div class="test-result info">📝 操作記錄: 不存在</div>';
                }
                
                // 檢查儲存空間使用量
                let totalSize = 0;
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        totalSize += localStorage[key].length;
                    }
                }
                
                html += `<div class="test-result info">
                    💾 本地儲存使用量: ${(totalSize / 1024).toFixed(2)} KB
                </div>`;
                
            } catch (error) {
                html += `<div class="test-result error">❌ 檢查失敗: ${error.message}</div>`;
            }
            
            container.innerHTML = html;
        }
        
        // 清除本地儲存
        function clearLocalStorage() {
            if (confirm('確定要清除所有本地儲存資料嗎？這將刪除所有學生資料和操作記錄！')) {
                localStorage.clear();
                document.getElementById('storageCheck').innerHTML = 
                    '<div class="test-result info">🗑️ 所有本地儲存資料已清除</div>';
            }
        }
        
        // 建立測試資料
        function createTestData() {
            const testStudents = [
                { id: 1, number: '01', class: '603', name: '測試學生A', score: 85 },
                { id: 2, number: '02', class: '603', name: '測試學生B', score: 92 },
                { id: 3, number: '03', class: '603', name: '測試學生C', score: 78 },
                { id: 4, number: '04', class: '604', name: '測試學生D', score: 88 },
                { id: 5, number: '05', class: '604', name: '測試學生E', score: 95 }
            ];
            
            // 添加簽到記錄
            const today = new Date().toISOString().split('T')[0];
            testStudents.forEach(student => {
                student.checkins = {};
                student.checkins[today] = {
                    checkedIn: Math.random() > 0.5,
                    timestamp: new Date().toISOString()
                };
            });
            
            localStorage.setItem('students', JSON.stringify(testStudents));
            
            document.getElementById('exportResult').innerHTML = 
                '<div class="test-result success">✅ 已建立 5 筆測試學生資料</div>';
        }
        
        // 測試匯出
        function testExport() {
            const container = document.getElementById('exportResult');
            
            try {
                const students = localStorage.getItem('students');
                if (!students) {
                    container.innerHTML = '<div class="test-result error">❌ 沒有學生資料可匯出，請先建立測試資料</div>';
                    return;
                }
                
                const studentData = JSON.parse(students);
                const dataStr = JSON.stringify(studentData, null, 2);
                const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
                
                const exportFileDefaultName = `測試學生名單_${new Date().toISOString().split('T')[0]}.json`;
                
                const linkElement = document.createElement('a');
                linkElement.setAttribute('href', dataUri);
                linkElement.setAttribute('download', exportFileDefaultName);
                linkElement.click();
                
                container.innerHTML = `
                    <div class="test-result success">
                        ✅ 匯出成功！檔案名稱: ${exportFileDefaultName}
                    </div>
                    <div class="data-preview">
                        <strong>匯出資料預覽：</strong><br>
                        ${dataStr.substring(0, 500)}...
                    </div>
                `;
                
            } catch (error) {
                container.innerHTML = `<div class="test-result error">❌ 匯出失敗: ${error.message}</div>`;
            }
        }
        
        // 測試匯入
        function testImport() {
            const fileInput = document.getElementById('testImportFile');
            const container = document.getElementById('importResult');
            
            const file = fileInput.files[0];
            if (!file) {
                container.innerHTML = '<div class="test-result error">❌ 請先選擇一個 JSON 檔案</div>';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const importedData = JSON.parse(e.target.result);
                    
                    if (Array.isArray(importedData)) {
                        container.innerHTML = `
                            <div class="test-result success">
                                ✅ 檔案格式正確！包含 ${importedData.length} 筆學生資料
                            </div>
                            <div class="data-preview">
                                <strong>匯入資料預覽：</strong><br>
                                ${JSON.stringify(importedData.slice(0, 2), null, 2)}
                                ${importedData.length > 2 ? `<br>... 還有 ${importedData.length - 2} 筆資料` : ''}
                            </div>
                            <button onclick="confirmImport('${encodeURIComponent(JSON.stringify(importedData))}')">
                                確認匯入資料
                            </button>
                        `;
                    } else {
                        container.innerHTML = '<div class="test-result error">❌ 檔案格式不正確，應該是學生資料陣列</div>';
                    }
                } catch (error) {
                    container.innerHTML = `<div class="test-result error">❌ 檔案解析失敗: ${error.message}</div>`;
                }
            };
            
            reader.readAsText(file);
        }
        
        // 確認匯入
        function confirmImport(encodedData) {
            try {
                const importedData = JSON.parse(decodeURIComponent(encodedData));
                localStorage.setItem('students', JSON.stringify(importedData));
                
                document.getElementById('importResult').innerHTML = `
                    <div class="test-result success">
                        ✅ 資料匯入成功！已匯入 ${importedData.length} 筆學生資料
                    </div>
                `;
                
                // 自動更新本地儲存檢查
                checkLocalStorage();
                
            } catch (error) {
                document.getElementById('importResult').innerHTML = 
                    `<div class="test-result error">❌ 匯入失敗: ${error.message}</div>`;
            }
        }
        
        // 頁面載入時自動檢查
        window.addEventListener('DOMContentLoaded', function() {
            checkLocalStorage();
        });
    </script>
</body>
</html>
