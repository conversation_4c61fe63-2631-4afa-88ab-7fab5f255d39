{"permissions": {"allow": ["mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_evaluate", "Bash(claude mcp add:*)", "Bash(for file in cleaning.html lunch.html tooth-brushing.html lucky-draw.html)", "Bash(do echo \"Processing $file\")", "Bash(done)", "Bash(find:*)", "Bash(for file in daily-checkin.html tooth-brushing.html cleaning.html lunch.html discipline.html discipline-history.html leaderboard.html lucky-draw.html manual-score.html)", "Bash(do echo \"=== $file ===\")", "<PERSON><PERSON>(sed:*)", "Bash(do sed -i 's/<li><a href=\"\"seat-cleaning.html\"\".*清潔座位<\\/a><\\/li>/<li><a href=\"\"seat-cleaning.html\"\"><i class=\"\"fas fa-chair\"\"><\\/i> 🪑 清潔座位<\\/a><\\/li>\\n                    <li><a href=\"\"officer-duties.html\"\"><i class=\"\"fas fa-crown\"\"><\\/i> 👑 幹部工作<\\/a><\\/li>/' \"$file\")", "Bash(for:*)", "Bash(do sed -i 's/<li><a href=\"\"officer-duties.html\"\".*幹部工作<\\/a><\\/li>/<li><a href=\"\"officer-duties.html\"\"><i class=\"\"fas fa-crown\"\"><\\/i> 👑 幹部工作<\\/a><\\/li>\\n                    <li><a href=\"\"duty-student.html\"\"><i class=\"\"fas fa-clipboard-list\"\"><\\/i> 📋 值日生工作<\\/a><\\/li>/' \"$file\")", "Bash(grep:*)", "Bash(do)", "Bash(if [ -f \"$file\" ])", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(echo:*)", "Bash(fi)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(python3:*)", "mcp__puppeteer__puppeteer_fill", "Bash(node:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["puppeteer"]}