const { chromium } = require('playwright');

async function diagnoseHomework() {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    // 監聽所有 console 訊息
    page.on('console', msg => console.log('Console:', msg.text()));
    page.on('pageerror', err => console.error('Page Error:', err.message));
    
    try {
        // 假設您的頁面已經在瀏覽器中打開，我們直接檢查元素
        await page.goto('file://' + process.cwd() + '/homework-submission.html');
        
        // 等待頁面載入
        await page.waitForLoadState('networkidle');
        
        // 檢查關鍵元素和函式
        const diagnosis = await page.evaluate(() => {
            const input = document.getElementById('homeworkInput');
            const btn = document.getElementById('addHomeworkBtn');
            
            return {
                inputExists: !!input,
                btnExists: !!btn,
                inputValue: input ? input.value : null,
                addHomeworkItemExists: typeof addHomeworkItem === 'function',
                dailyHomeworkExists: typeof dailyHomework !== 'undefined',
                todayExists: typeof today !== 'undefined',
                btnHasEventListener: btn ? btn.onclick !== null : false,
                jsErrors: window.lastError || null
            };
        });
        
        console.log('🔍 診斷結果:', diagnosis);
        
        // 如果函式存在，嘗試手動執行
        if (diagnosis.addHomeworkItemExists) {
            console.log('🔧 嘗試手動執行 addHomeworkItem...');
            
            // 先清空輸入框，然後輸入測試內容
            await page.fill('#homeworkInput', '聯絡簿');
            
            // 手動調用函式
            await page.evaluate(() => {
                try {
                    addHomeworkItem();
                    console.log('✅ addHomeworkItem 執行成功');
                } catch (error) {
                    console.error('❌ addHomeworkItem 執行失敗:', error);
                }
            });
            
            await page.waitForTimeout(2000);
            
            // 檢查結果
            const result = await page.evaluate(() => {
                return {
                    inputValue: document.getElementById('homeworkInput').value,
                    homeworkItems: document.querySelectorAll('.homework-item').length,
                    todayHomework: typeof dailyHomework !== 'undefined' && typeof today !== 'undefined' ? dailyHomework[today] : null
                };
            });
            
            console.log('📋 執行結果:', result);
        }
        
        // 檢查事件監聽器
        console.log('🔧 重新綁定事件監聽器...');
        await page.evaluate(() => {
            const btn = document.getElementById('addHomeworkBtn');
            if (btn) {
                // 移除舊的事件監聽器
                btn.onclick = null;
                
                // 重新綁定
                btn.addEventListener('click', function() {
                    console.log('按鈕被點擊了！');
                    if (typeof addHomeworkItem === 'function') {
                        addHomeworkItem();
                    } else {
                        console.error('addHomeworkItem 函式不存在');
                    }
                });
                
                console.log('✅ 事件監聽器重新綁定完成');
            }
        });
        
        // 現在測試點擊
        console.log('🖱️ 測試按鈕點擊...');
        await page.fill('#homeworkInput', '測試作業');
        await page.click('#addHomeworkBtn');
        
        await page.waitForTimeout(2000);
        
    } catch (error) {
        console.error('❌ 診斷過程發生錯誤:', error);
    }
    
    console.log('⏰ 保持開啟供檢查...');
    await page.waitForTimeout(30000);
    await browser.close();
}

diagnoseHomework().catch(console.error);