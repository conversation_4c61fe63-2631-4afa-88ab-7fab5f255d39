/* 作業繳交歷史記錄頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 統計摘要區域 */
.summary-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.summary-card i {
    font-size: 2rem;
    padding: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), #ff69b4);
    color: white;
    box-shadow: 0 4px 10px rgba(255, 182, 193, 0.3);
}

.summary-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.summary-count {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-color);
}

.summary-label {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* 篩選控制區域 */
.filter-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
}

.filter-container h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

/* 表格樣式 */
.table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

#historyTable {
    width: 100%;
    border-collapse: collapse;
}

#historyTable th {
    background: linear-gradient(135deg, var(--primary-color), #ffa6c9);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    position: relative;
}

#historyTable th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

#historyTable td {
    padding: 12px;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.3s ease;
    font-size: 0.9rem;
}

#historyTable tr:hover td {
    background-color: rgba(255, 182, 193, 0.05);
}

#historyTable tr:last-child td {
    border-bottom: none;
}

/* 學生頭像 */
.student-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.student-avatar:hover {
    transform: scale(1.1) rotate(5deg);
}

/* 學生頭像 - Emoji版本 */
.student-avatar-cell {
    text-align: center;
    vertical-align: middle;
    padding: 8px;
}

.student-avatar-emoji {
    font-size: 28px;
    display: inline-block;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    border: 2px solid var(--primary-color);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.student-avatar-emoji:hover {
    transform: scale(1.1) rotate(5deg);
}

/* 日期和時間顯示 */
.date-display {
    font-weight: 500;
    color: var(--text-color);
}

.time-display {
    color: var(--text-light);
    font-size: 0.85rem;
    font-family: 'Courier New', monospace;
}

/* 學號和姓名 */
.student-id {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.student-name {
    font-weight: 500;
    color: var(--text-color);
}

/* 作業項目標籤 */
.homework-item-tag {
    background: linear-gradient(135deg, #17a2b8, #007bff);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

/* 分數顯示 */
.score-display {
    font-weight: bold;
    font-size: 1rem;
    color: #28a745;
    display: flex;
    align-items: center;
    gap: 5px;
}

.score-display::before {
    content: '+';
    color: #28a745;
}

/* 按鈕樣式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #ff69b4);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 182, 193, 0.6);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.5);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #007bff);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.5);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.5);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.5);
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination button {
    padding: 8px 12px;
    border: 2px solid var(--primary-color);
    background: white;
    color: var(--primary-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.pagination button:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.pagination button.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 彈窗樣式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    position: absolute;
    right: 20px;
    top: 20px;
}

.close:hover {
    color: var(--primary-color);
}

.modal h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.4rem;
}

/* 表單樣式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.1);
}

.form-group input:disabled,
.form-group select:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
}

/* 空狀態 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.3;
}

.empty-state h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #666;
}

.empty-state p {
    margin-bottom: 20px;
    line-height: 1.6;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .summary-container {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: unset;
    }
    
    .filter-buttons {
        margin-left: 0;
        justify-content: stretch;
    }
    
    .filter-buttons .btn {
        flex: 1;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    #historyTable {
        min-width: 800px;
    }
    
    .modal-content {
        margin: 2% auto;
        padding: 20px;
        width: 95%;
    }
}

@media (max-width: 480px) {
    .summary-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .summary-card i {
        font-size: 1.8rem;
        padding: 10px;
    }
    
    .summary-count {
        font-size: 1.5rem;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    .filter-buttons {
        flex-direction: column;
    }
}