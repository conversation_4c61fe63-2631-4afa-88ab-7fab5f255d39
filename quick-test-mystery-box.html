<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>神祕寶箱快速測試</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎁 神祕寶箱快速測試</h1>
        <p>點擊下方按鈕測試完整的神祕寶箱流程</p>
        
        <button class="test-btn" onclick="testCompleteFlow()">
            <i class="fas fa-play"></i> 測試完整流程
        </button>
        
        <div id="status" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; display: none;">
            <strong>狀態：</strong><span id="statusText">準備中...</span>
        </div>
    </div>

    <script>
        function updateStatus(text) {
            document.getElementById('status').style.display = 'block';
            document.getElementById('statusText').textContent = text;
            console.log('狀態:', text);
        }
        
        function testCompleteFlow() {
            updateStatus('開始測試...');
            
            // 模擬學生資料
            const testStudent = {
                id: 1,
                name: '拿破崙',
                score: 15
            };
            
            // 創建神祕寶箱彈窗
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 2000;
            `;
            
            modal.innerHTML = `
                <div style="
                    max-width: 500px;
                    text-align: center;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 15px;
                    padding: 30px;
                ">
                    <h3>🎁 神祕寶箱出現了！</h3>
                    <p>${testStudent.name} 獲得了 +5 分，觸發了神祕事件！</p>
                    
                    <div style="margin: 30px 0;">
                        <div style="width: 80px; height: 80px; margin: 0 auto; position: relative;">
                            <div style="
                                width: 80px;
                                height: 50px;
                                background: linear-gradient(135deg, #8B4513, #A0522D);
                                border-radius: 8px;
                                position: absolute;
                                bottom: 0;
                                border: 3px solid #654321;
                            "></div>
                            <div id="boxLid" style="
                                width: 84px;
                                height: 25px;
                                background: linear-gradient(135deg, #DAA520, #FFD700);
                                border-radius: 8px 8px 4px 4px;
                                position: absolute;
                                top: 0;
                                left: -2px;
                                border: 3px solid #B8860B;
                                transition: transform 0.5s ease;
                                transform-origin: bottom;
                            "></div>
                        </div>
                    </div>
                    
                    <div>
                        <button id="openBtn" style="
                            background: linear-gradient(135deg, #00b894, #00a085);
                            border: none;
                            padding: 12px 24px;
                            border-radius: 25px;
                            font-weight: 600;
                            color: white;
                            cursor: pointer;
                            margin: 0 10px;
                        ">
                            <i class="fas fa-gift"></i> 開啟寶箱
                        </button>
                        <button id="skipBtn" style="
                            background: rgba(255, 255, 255, 0.2);
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            color: white;
                            padding: 10px 20px;
                            border-radius: 25px;
                            cursor: pointer;
                            margin: 0 10px;
                        ">
                            <i class="fas fa-times"></i> 跳過
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            updateStatus('神祕寶箱已顯示');
            
            // 綁定開啟按鈕
            modal.querySelector('#openBtn').addEventListener('click', () => {
                updateStatus('開始開箱動畫...');
                
                // 隱藏按鈕
                modal.querySelector('#openBtn').style.display = 'none';
                modal.querySelector('#skipBtn').style.display = 'none';
                
                // 開箱動畫
                const lid = modal.querySelector('#boxLid');
                lid.style.transform = 'rotateX(-45deg) translateY(-10px)';
                
                // 2秒後顯示結果
                setTimeout(() => {
                    showResult(modal);
                }, 2000);
            });
            
            // 綁定跳過按鈕
            modal.querySelector('#skipBtn').addEventListener('click', () => {
                updateStatus('用戶選擇跳過');
                closeModal(modal);
            });
        }
        
        function showResult(modal) {
            updateStatus('顯示隨機事件結果...');
            
            const events = [
                { name: '強運', icon: '🍀', color: '#00b894', description: '分數翻倍' },
                { name: '掠奪', icon: '💰', color: '#fdcb6e', description: '偷取其他學生5分' },
                { name: '天使', icon: '😇', color: '#74b9ff', description: '獲得保護狀態' },
                { name: '惡魔', icon: '😈', color: '#ff7675', description: '分數減半' },
                { name: '交換', icon: '🔄', color: '#a29bfe', description: '與其他學生交換分數' }
            ];
            
            const randomEvent = events[Math.floor(Math.random() * events.length)];
            
            const content = modal.querySelector('div > div');
            content.innerHTML = `
                <div style="padding: 20px;">
                    <div style="font-size: 4rem; margin-bottom: 15px; color: ${randomEvent.color};">
                        ${randomEvent.icon}
                    </div>
                    <h3 style="font-size: 1.8rem; margin-bottom: 10px; color: ${randomEvent.color};">
                        ${randomEvent.name}
                    </h3>
                    <p style="margin-bottom: 20px; opacity: 0.9;">
                        ${randomEvent.description}
                    </p>
                    <div style="
                        background: rgba(255, 255, 255, 0.2);
                        padding: 15px;
                        border-radius: 10px;
                        margin: 20px 0;
                        font-size: 1.1rem;
                        font-weight: 600;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                    ">
                        恭喜！您獲得了 ${randomEvent.name} 事件！
                    </div>
                    <button onclick="closeModal(this.closest('div').parentElement.parentElement)" style="
                        background: rgba(255, 255, 255, 0.9);
                        color: #333;
                        border: none;
                        padding: 12px 30px;
                        border-radius: 25px;
                        font-weight: 600;
                        cursor: pointer;
                    ">
                        <i class="fas fa-check"></i> 確定
                    </button>
                </div>
            `;
            
            updateStatus(`隨機事件結果：${randomEvent.name}`);
        }
        
        function closeModal(modal) {
            updateStatus('關閉神祕寶箱');
            modal.style.opacity = '0';
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
                updateStatus('測試完成！');
            }, 300);
        }
    </script>
</body>
</html>
