const { chromium } = require('playwright');

async function openYahoo() {
    console.log('🚀 啟動 Playwright...');
    
    // 啟動瀏覽器
    const browser = await chromium.launch({ 
        headless: false,  // 顯示瀏覽器視窗
        slowMo: 1000      // 每個操作間隔1秒，方便觀察
    });
    
    console.log('📱 創建新頁面...');
    const page = await browser.newPage();
    
    // 設置視窗大小
    await page.setViewportSize({ width: 1280, height: 720 });
    
    console.log('🌐 正在前往 Yahoo 網站...');
    
    try {
        // 前往 Yahoo 網站
        await page.goto('https://tw.yahoo.com/', { 
            waitUntil: 'networkidle',  // 等待網路請求完成
            timeout: 30000             // 30秒超時
        });
        
        console.log('✅ 成功打開 Yahoo 網頁！');
        
        // 等待頁面完全載入
        await page.waitForLoadState('domcontentloaded');
        
        // 取得頁面標題
        const title = await page.title();
        console.log(`📄 頁面標題: ${title}`);
        
        // 截圖
        await page.screenshot({ 
            path: 'yahoo-screenshot.png',
            fullPage: true 
        });
        console.log('📸 已儲存截圖: yahoo-screenshot.png');
        
        // 保持瀏覽器開啟30秒讓您查看
        console.log('⏰ 瀏覽器將保持開啟30秒...');
        await page.waitForTimeout(30000);
        
    } catch (error) {
        console.error('❌ 發生錯誤:', error.message);
    } finally {
        console.log('🔚 關閉瀏覽器...');
        await browser.close();
        console.log('✨ 完成！');
    }
}

// 執行函數
openYahoo().catch(console.error);
