// 作業繳交歷史記錄功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const totalRecordsElement = document.getElementById('totalRecords');
    const todayRecordsElement = document.getElementById('todayRecords');
    const weekRecordsElement = document.getElementById('weekRecords');
    const totalPointsElement = document.getElementById('totalPoints');
    const dateStartInput = document.getElementById('dateStart');
    const dateEndInput = document.getElementById('dateEnd');
    const studentSelect = document.getElementById('studentSelect');
    const homeworkItemSelect = document.getElementById('homeworkItemSelect');
    const applyFilterBtn = document.getElementById('applyFilterBtn');
    const clearFilterBtn = document.getElementById('clearFilterBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const backToHomeworkBtn = document.getElementById('backToHomeworkBtn');
    const historyTableBody = document.getElementById('historyTableBody');
    const paginationElement = document.getElementById('pagination');
    const editModal = document.getElementById('editModal');
    const editForm = document.getElementById('editForm');
    const editStudent = document.getElementById('editStudent');
    const editHomeworkItem = document.getElementById('editHomeworkItem');
    const editDate = document.getElementById('editDate');
    const editScore = document.getElementById('editScore');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const closeModalBtns = document.querySelectorAll('.close');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');
    
    // 常數設定
    const ITEMS_PER_PAGE = 20;
    
    // 狀態變數
    let students = [];
    let homeworkHistory = [];
    let filteredHistory = [];
    let currentPage = 1;
    let editingRecordId = null;
    let today = new Date().toISOString().split('T')[0];
    
    // 初始化頁面
    function initPage() {
        loadData();
        setupEventListeners();
        updateSummary();
        populateFilters();
        applyFilters();
    }
    
    // 載入資料
    function loadData() {
        // 載入學生資料
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
        }
        
        // 載入作業繳交歷史記錄
        const savedHistory = localStorage.getItem('homeworkHistory');
        if (savedHistory) {
            homeworkHistory = JSON.parse(savedHistory);
        }
    }
    
    // 設置事件監聽器
    function setupEventListeners() {
        applyFilterBtn.addEventListener('click', applyFilters);
        clearFilterBtn.addEventListener('click', clearFilters);
        downloadBtn.addEventListener('click', downloadRecords);
        backToHomeworkBtn.addEventListener('click', () => {
            window.location.href = 'homework-submission.html';
        });
        
        editForm.addEventListener('submit', saveEditedRecord);
        cancelEditBtn.addEventListener('click', closeEditModal);
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', closeEditModal);
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === editModal) {
                closeEditModal();
            }
        });
        
        // 匯入匯出功能
        if (exportBtn) {
            exportBtn.addEventListener('click', exportData);
        }
        if (importBtn) {
            importBtn.addEventListener('click', () => importFile.click());
        }
        if (importFile) {
            importFile.addEventListener('change', importData);
        }
    }
    
    // 更新統計摘要
    function updateSummary() {
        const totalRecords = homeworkHistory.length;
        const todayRecords = homeworkHistory.filter(record => record.date === today).length;
        
        // 計算本週記錄
        const weekStart = getWeekStart(new Date());
        const weekRecords = homeworkHistory.filter(record => {
            const recordDate = new Date(record.date);
            return recordDate >= weekStart && recordDate <= new Date();
        }).length;
        
        const totalPoints = homeworkHistory.reduce((sum, record) => sum + (record.score || 0), 0);
        
        totalRecordsElement.textContent = totalRecords;
        todayRecordsElement.textContent = todayRecords;
        weekRecordsElement.textContent = weekRecords;
        totalPointsElement.textContent = totalPoints;
    }
    
    // 獲取週開始日期
    function getWeekStart(date) {
        const d = new Date(date);
        const day = d.getDay();
        const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 調整為週一開始
        return new Date(d.setDate(diff));
    }
    
    // 填充篩選選項
    function populateFilters() {
        // 填充學生選項
        studentSelect.innerHTML = '<option value="">全部學生</option>';
        students.forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = `${student.id} - ${student.name}`;
            studentSelect.appendChild(option);
        });
        
        // 填充編輯表單學生選項
        editStudent.innerHTML = '';
        students.forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = `${student.id} - ${student.name}`;
            editStudent.appendChild(option);
        });
        
        // 填充作業項目選項
        const homeworkItems = [...new Set(homeworkHistory.map(record => record.homeworkItem).filter(item => item))];
        homeworkItemSelect.innerHTML = '<option value="">全部項目</option>';
        homeworkItems.forEach(item => {
            const option = document.createElement('option');
            option.value = item;
            option.textContent = item;
            homeworkItemSelect.appendChild(option);
        });
    }
    
    // 應用篩選
    function applyFilters() {
        const startDate = dateStartInput.value;
        const endDate = dateEndInput.value;
        const selectedStudent = studentSelect.value;
        const selectedHomeworkItem = homeworkItemSelect.value;
        
        filteredHistory = homeworkHistory.filter(record => {
            // 日期篩選
            if (startDate && record.date < startDate) return false;
            if (endDate && record.date > endDate) return false;
            
            // 學生篩選
            if (selectedStudent && record.studentId !== selectedStudent) return false;
            
            // 作業項目篩選
            if (selectedHomeworkItem && record.homeworkItem !== selectedHomeworkItem) return false;
            
            return true;
        });
        
        // 按時間降序排列
        filteredHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        currentPage = 1;
        renderTable();
        renderPagination();
    }
    
    // 清除篩選
    function clearFilters() {
        dateStartInput.value = '';
        dateEndInput.value = '';
        studentSelect.value = '';
        homeworkItemSelect.value = '';
        applyFilters();
    }
    
    // 渲染表格
    function renderTable() {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        const pageRecords = filteredHistory.slice(startIndex, endIndex);
        
        historyTableBody.innerHTML = '';
        
        if (pageRecords.length === 0) {
            historyTableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>沒有找到相關記錄</h3>
                        <p>請嘗試調整篩選條件或確認是否有作業繳交記錄</p>
                    </td>
                </tr>
            `;
            return;
        }
        
        pageRecords.forEach(record => {
            const row = createRecordRow(record);
            historyTableBody.appendChild(row);
        });
    }
    
    // 創建記錄行
    function createRecordRow(record) {
        const row = document.createElement('tr');
        // 確保ID類型匹配（歷史記錄可能是字串，學生資料是數字）
        const student = students.find(s => s.id == record.studentId);
        const studentName = student ? student.name : '未知學生';
        const studentAvatar = student ? (() => {
            let animalDisplay = student.animal || '🐱';
            if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                animalDisplay = `<div class="student-animal-avatar">
                    <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                    <span class="animal-emoji">${animalInfo.emoji}</span>
                </div>`;
            }
            return animalDisplay;
        })() : '🐻'; // 使用emoji，預設為小熊

        row.innerHTML = `
            <td class="date-display">${record.date}</td>
            <td class="time-display">${formatTime(record.timestamp)}</td>
            <td class="student-avatar-cell">
                <span class="student-avatar-emoji">${studentAvatar}</span>
            </td>
            <td class="student-id">${record.studentId}</td>
            <td class="student-name">${studentName}</td>
            <td>
                <span class="homework-item-tag">${record.homeworkItem || '未指定'}</span>
            </td>
            <td class="score-display">${record.score || 3}</td>
            <td>
                <button class="btn btn-warning" onclick="editRecord(${record.id})">
                    <i class="fas fa-edit"></i> 編輯
                </button>
                <button class="btn btn-danger" onclick="deleteRecord(${record.id})">
                    <i class="fas fa-trash"></i> 刪除
                </button>
            </td>
        `;
        
        return row;
    }
    
    // 編輯記錄
    window.editRecord = function(recordId) {
        const record = homeworkHistory.find(r => r.id === recordId);
        if (!record) return;
        
        editingRecordId = recordId;
        editStudent.value = record.studentId;
        editHomeworkItem.value = record.homeworkItem || '';
        editDate.value = record.date;
        editScore.value = record.score || 3;
        
        editModal.style.display = 'block';
    };
    
    // 保存編輯的記錄
    function saveEditedRecord(e) {
        e.preventDefault();
        
        if (!editingRecordId) return;
        
        const recordIndex = homeworkHistory.findIndex(r => r.id === editingRecordId);
        if (recordIndex === -1) return;
        
        const oldRecord = homeworkHistory[recordIndex];
        const student = students.find(s => s.id === oldRecord.studentId);
        
        // 更新記錄
        homeworkHistory[recordIndex] = {
            ...oldRecord,
            homeworkItem: editHomeworkItem.value.trim(),
            date: editDate.value,
            score: parseInt(editScore.value)
        };
        
        // 更新學生記錄中的分數差異
        if (student && oldRecord.score !== parseInt(editScore.value)) {
            const scoreDifference = parseInt(editScore.value) - oldRecord.score;
            student.score += scoreDifference;
        }
        
        // 保存資料
        saveData();
        
        // 更新顯示
        updateSummary();
        populateFilters();
        applyFilters();
        
        closeEditModal();
        showSuccessMessage('記錄修改成功！');
    }
    
    // 刪除記錄
    window.deleteRecord = function(recordId) {
        if (!confirm('確定要刪除這筆記錄嗎？此操作無法復原，並會自動扣除學生相關分數！')) {
            return;
        }
        
        const recordIndex = homeworkHistory.findIndex(r => r.id === recordId);
        if (recordIndex === -1) return;
        
        const record = homeworkHistory[recordIndex];
        const student = students.find(s => s.id === record.studentId);
        
        // 扣除學生分數
        if (student) {
            student.score -= (record.score || 3);
            
            // 從學生的作業繳交記錄中移除
            if (student.homeworkSubmission && 
                student.homeworkSubmission[record.date] && 
                student.homeworkSubmission[record.date][record.homeworkItem]) {
                delete student.homeworkSubmission[record.date][record.homeworkItem];
            }
        }
        
        // 從歷史記錄中移除
        homeworkHistory.splice(recordIndex, 1);
        
        // 保存資料
        saveData();
        
        // 更新顯示
        updateSummary();
        populateFilters();
        applyFilters();
        
        showSuccessMessage('記錄已刪除並恢復相關分數！');
    };
    
    // 關閉編輯彈窗
    function closeEditModal() {
        editModal.style.display = 'none';
        editingRecordId = null;
        editForm.reset();
    }
    
    // 渲染分頁
    function renderPagination() {
        const totalPages = Math.ceil(filteredHistory.length / ITEMS_PER_PAGE);
        paginationElement.innerHTML = '';
        
        if (totalPages <= 1) return;
        
        // 上一頁按鈕
        const prevBtn = document.createElement('button');
        prevBtn.textContent = '上一頁';
        prevBtn.disabled = currentPage === 1;
        prevBtn.addEventListener('click', () => changePage(currentPage - 1));
        paginationElement.appendChild(prevBtn);
        
        // 頁碼按鈕
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);
        
        if (startPage > 1) {
            const firstBtn = document.createElement('button');
            firstBtn.textContent = '1';
            firstBtn.addEventListener('click', () => changePage(1));
            paginationElement.appendChild(firstBtn);
            
            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.style.padding = '8px';
                paginationElement.appendChild(ellipsis);
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.classList.toggle('active', i === currentPage);
            pageBtn.addEventListener('click', () => changePage(i));
            paginationElement.appendChild(pageBtn);
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.style.padding = '8px';
                paginationElement.appendChild(ellipsis);
            }
            
            const lastBtn = document.createElement('button');
            lastBtn.textContent = totalPages;
            lastBtn.addEventListener('click', () => changePage(totalPages));
            paginationElement.appendChild(lastBtn);
        }
        
        // 下一頁按鈕
        const nextBtn = document.createElement('button');
        nextBtn.textContent = '下一頁';
        nextBtn.disabled = currentPage === totalPages;
        nextBtn.addEventListener('click', () => changePage(currentPage + 1));
        paginationElement.appendChild(nextBtn);
    }
    
    // 切換頁面
    function changePage(page) {
        currentPage = page;
        renderTable();
        renderPagination();
        
        // 滾動到表格頂部
        document.querySelector('.table-container').scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
        });
    }
    
    // 下載記錄
    function downloadRecords() {
        if (filteredHistory.length === 0) {
            alert('沒有可下載的記錄！');
            return;
        }
        
        const csvContent = generateCSV();
        // 添加 BOM 以支援中文編碼
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `作業繳交歷史記錄_${today}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
    
    // 生成CSV
    function generateCSV() {
        const headers = ['日期', '時間', '學號', '姓名', '作業項目', '獲得分數'];
        const rows = [headers];

        filteredHistory.forEach(record => {
            // 確保ID類型匹配（歷史記錄可能是字串，學生資料是數字）
            const student = students.find(s => s.id == record.studentId);
            const studentName = student ? student.name : '未知學生';

            rows.push([
                record.date,
                formatTime(record.timestamp),
                record.studentId,
                studentName,
                record.homeworkItem || '未指定',
                record.score || 3
            ]);
        });

        // 對每個欄位加上雙引號並處理內容中的雙引號
        return rows.map(row =>
            row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    }
    
    // 匯出資料
    function exportData() {
        const data = {
            students: students,
            homeworkHistory: homeworkHistory,
            exportDate: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `作業繳交歷史資料_${today}.json`;
        link.click();
        URL.revokeObjectURL(url);
    }
    
    // 匯入資料
    function importData(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.students && data.homeworkHistory) {
                        if (confirm('匯入資料將覆蓋現有資料，確定要繼續嗎？')) {
                            students = data.students;
                            homeworkHistory = data.homeworkHistory;
                            saveData();
                            loadData();
                            updateSummary();
                            populateFilters();
                            applyFilters();
                            showSuccessMessage('資料匯入成功！');
                        }
                    } else {
                        alert('無效的資料格式！');
                    }
                } catch (error) {
                    alert('檔案格式錯誤！');
                }
            };
            reader.readAsText(file);
        }
        event.target.value = '';
    }
    
    // 保存資料
    function saveData() {
        localStorage.setItem('students', JSON.stringify(students));
        localStorage.setItem('homeworkHistory', JSON.stringify(homeworkHistory));
    }
    
    // 格式化時間
    function formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-TW', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        });
    }
    
    // 顯示成功消息
    function showSuccessMessage(message) {
        const messageEl = document.createElement('div');
        messageEl.className = 'success-message';
        messageEl.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(messageEl);
        
        setTimeout(() => {
            messageEl.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 300);
        }, 3000);
    }
    
    // 添加動畫樣式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    `;
    document.head.appendChild(style);
    
    // 初始化頁面
    initPage();
});