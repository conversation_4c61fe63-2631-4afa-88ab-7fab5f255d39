// 幸運轉盤功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const studentSelect = document.getElementById('studentSelect');
    const spinBtn = document.getElementById('spinBtn');
    const wheel = document.getElementById('wheel');
    const resultText = document.getElementById('resultText');
    const resultContainer = document.getElementById('resultContainer');
    const resultModal = document.getElementById('resultModal');
    const modalResultContent = document.getElementById('modalResultContent');
    const confirmResultBtn = document.getElementById('confirmResultBtn');
    const closeModalBtns = document.querySelectorAll('.close');
    const spinStatus = document.getElementById('spinStatus');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');

    // 獎項配置：值、機率、顏色、圖標、描述、角度範圍
    const prizes = [
        {
            value: 10,
            probability: 10,
            color: '#ff6b6b',
            icon: 'fa-star',
            label: '太棒了！獲得 10 分！',
            startAngle: 0,
            endAngle: 36
        },
        {
            value: 5,
            probability: 20,
            color: '#4ecdc4',
            icon: 'fa-rocket',
            label: '恭喜！獲得 5 分！',
            startAngle: 36,
            endAngle: 108
        },
        {
            value: 1,
            probability: 50,
            color: '#45b7d1',
            icon: 'fa-thumbs-up',
            label: '獲得 1 分！',
            startAngle: 108,
            endAngle: 288
        },
        {
            value: -1,
            probability: 20,
            color: '#f9ca24',
            icon: 'fa-sad-tear',
            label: '可惜！扣 1 分！',
            startAngle: 288,
            endAngle: 360
        }
    ];

    let isSpinning = false;
    let currentRotation = 0;
    let selectedStudent = null;
    
    // 初始化轉盤
    function initWheel() {
        // 轉盤不需要文字標籤，保持純色彩區域
        // 轉盤已經通過 CSS conic-gradient 設定好顏色區域

        // 初始化小動物互動
        initAnimalInteractions();
    }

    // 初始化小動物互動功能
    function initAnimalInteractions() {
        const animalIcons = document.querySelectorAll('.animal-icon');

        animalIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                // 添加點擊動畫效果
                this.style.animation = 'none';
                this.offsetHeight; // 觸發重排
                this.style.animation = 'animalClick 0.6s ease-out, float 3s ease-in-out infinite';

                // 播放音效提示（可選）
                playAnimalSound(this.dataset.animal);
            });
        });
    }

    // 播放小動物音效（模擬）
    function playAnimalSound(animal) {
        // 這裡可以添加實際的音效播放邏輯
        console.log(`${animal} 被點擊了！`);

        // 顯示小動物名稱提示
        showAnimalTooltip(animal);
    }

    // 顯示小動物名稱提示
    function showAnimalTooltip(animal) {
        const animalNames = {
            'rabbit': '小兔子 🐰',
            'fox': '小狐狸 🦊',
            'cat': '小貓咪 🐱',
            'panda': '熊貓 🐼',
            'bear': '小熊 🐻',
            'koala': '無尾熊 🐨'
        };

        // 創建提示元素
        const tooltip = document.createElement('div');
        tooltip.className = 'animal-tooltip';
        tooltip.textContent = animalNames[animal] || animal;
        tooltip.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1.2rem;
            z-index: 1000;
            animation: tooltipFade 1.5s ease-out forwards;
            pointer-events: none;
        `;

        document.body.appendChild(tooltip);

        // 1.5秒後移除提示
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 1500);
    }

    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            const students = JSON.parse(savedStudents);
            updateStudentSelect(students);
        }
    }

    // 更新學生選擇下拉選單
    function updateStudentSelect(students) {
        // 清空現有選項
        studentSelect.innerHTML = '<option value="" disabled selected>-- 請選擇一位學生 --</option>';

        // 按編號排序學生
        const sortedStudents = [...students].sort((a, b) => {
            return parseInt(a.number) - parseInt(b.number);
        });

        // 添加學生選項
        sortedStudents.forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = `${student.number} ${student.name}`;
            studentSelect.appendChild(option);
        });

        // 監聽選擇變化
        studentSelect.addEventListener('change', function() {
            const studentId = parseInt(this.value);
            selectedStudent = students.find(s => s.id === studentId);
            spinBtn.disabled = !selectedStudent;

            if (selectedStudent) {
                spinStatus.textContent = `已選擇 ${selectedStudent.name}，點擊開始轉盤！`;
            } else {
                spinStatus.textContent = '請先選擇一位學生';
            }
        });
    }
    
    // 旋轉轉盤
    function spinWheel() {
        if (isSpinning || !selectedStudent) return;

        // 根據機率隨機選擇一個獎項
        const random = Math.random() * 100;
        let cumulativeProbability = 0;
        let selectedPrize = null;

        for (const prize of prizes) {
            cumulativeProbability += prize.probability;
            if (random <= cumulativeProbability) {
                selectedPrize = prize;
                break;
            }
        }

        // 計算目標角度
        // 指針指向12點方向，所以需要計算獎項區域的中心角度
        const targetAngle = (selectedPrize.startAngle + selectedPrize.endAngle) / 2;

        // 計算旋轉角度 (至少旋轉5圈 + 隨機額外角度 + 目標角度)
        const extraSpins = 5 + Math.random() * 3; // 5-8圈
        const randomOffset = (Math.random() - 0.5) * 20; // ±10度隨機偏移
        const finalAngle = extraSpins * 360 + (360 - targetAngle) + randomOffset;

        currentRotation += finalAngle;

        // 開始旋轉動畫
        isSpinning = true;
        spinBtn.disabled = true;
        studentSelect.disabled = true;
        spinBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 轉盤中...';
        spinStatus.textContent = '轉盤旋轉中，請稍候...';

        // 應用旋轉動畫
        wheel.style.transform = `rotate(${currentRotation}deg)`;
        wheel.style.transition = 'transform 4s cubic-bezier(0.17, 0.67, 0.12, 0.99)';

        // 動畫結束後顯示結果
        setTimeout(() => {
            showResult(selectedPrize);
            isSpinning = false;
            studentSelect.disabled = false;
            spinBtn.disabled = false;
            spinBtn.innerHTML = '<i class="fas fa-play"></i> 開始轉盤';
            spinStatus.textContent = `已選擇 ${selectedStudent.name}，點擊開始轉盤！`;
        }, 4500);

        return selectedPrize;
    }
    
    // 顯示結果
    function showResult(prize) {
        // 更新學生分數
        const students = JSON.parse(localStorage.getItem('students')) || [];
        const studentIndex = students.findIndex(s => s.id === selectedStudent.id);
        
        if (studentIndex !== -1) {
            students[studentIndex].score = (students[studentIndex].score || 0) + prize.value;
            localStorage.setItem('students', JSON.stringify(students));
            
            // 更新結果顯示
            resultText.textContent = `${selectedStudent.name} ${prize.label}`;
            resultContainer.style.display = 'block';
            
            // 顯示彈出視窗
            showResultModal(selectedStudent, prize);
        }
    }
    
    // 顯示結果彈出視窗
    function showResultModal(student, prize) {
        modalResultContent.innerHTML = `
            <div class="result-icon">
                <i class="fas ${prize.icon}" style="font-size: 4rem; color: ${prize.color};"></i>
            </div>
            <h2 style="color: ${prize.color}; margin: 15px 0;">${prize.label}</h2>
            <p>${student.name} 目前分數: <strong>${student.score + prize.value} 分</strong></p>
            <div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 8px;">
                <p style="margin: 5px 0;">${getRandomEncouragement(prize.value)}</p>
            </div>
        `;
        
        resultModal.style.display = 'flex';
    }
    
    // 隨機鼓勵語
    function getRandomEncouragement(score) {
        if (score > 0) {
            const encouragements = [
                '太厲害了！繼續保持！',
                '表現得真棒！',
                '你是最棒的！',
                '繼續加油！',
                '太優秀了！'
            ];
            return encouragements[Math.floor(Math.random() * encouragements.length)];
        } else {
            const encouragements = [
                '沒關係，下次會更好！',
                '再接再厲！',
                '不要氣餒，繼續努力！',
                '失敗是成功之母！',
                '下次一定會更好！'
            ];
            return encouragements[Math.floor(Math.random() * encouragements.length)];
        }
    }
    
    // 關閉彈出視窗
    function closeModal() {
        resultModal.style.display = 'none';
    }
    
    // 事件監聽器
    spinBtn.addEventListener('click', spinWheel);
    confirmResultBtn.addEventListener('click', closeModal);
    
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', closeModal);
    });
    
    window.addEventListener('click', (e) => {
        if (e.target === resultModal) {
            closeModal();
        }
    });
    
    // 匯出資料
    exportBtn.addEventListener('click', () => {
        const students = JSON.parse(localStorage.getItem('students')) || [];
        const dataStr = JSON.stringify(students, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `學生名單_${new Date().toISOString().split('T')[0]}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    });

    // 匯入資料
    importBtn.addEventListener('click', () => {
        importFile.click();
    });

    importFile.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedStudents = JSON.parse(e.target.result);
                if (Array.isArray(importedStudents)) {
                    if (confirm(`確定要匯入 ${importedStudents.length} 筆學生資料嗎？這將覆蓋現有資料。`)) {
                        localStorage.setItem('students', JSON.stringify(importedStudents));
                        loadStudents(); // 重新載入學生選單
                        alert('資料匯入成功！');
                    }
                } else {
                    alert('匯入的檔案格式不正確！');
                }
            } catch (error) {
                console.error('匯入錯誤:', error);
                alert('匯入檔案時發生錯誤！');
            }
            // 重置檔案輸入，允許重複選擇同一個檔案
            importFile.value = '';
        };
        reader.readAsText(file);
    });

    // 初始化
    initWheel();
    loadStudents();

    // 禁用右鍵選單
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
});
