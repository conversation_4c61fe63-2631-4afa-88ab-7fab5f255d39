// 違規歷史記錄頁面功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const currentDateElement = document.getElementById('currentDate');
    const totalRecordsElement = document.getElementById('totalRecords');
    const todayRecordsElement = document.getElementById('todayRecords');
    const weekRecordsElement = document.getElementById('weekRecords');
    const totalPenaltyElement = document.getElementById('totalPenalty');
    const filteredTotalElement = document.getElementById('filteredTotal');
    const filteredPenaltyElement = document.getElementById('filteredPenalty');

    // 時段選擇器元素
    const totalPeriodBtn = document.getElementById('totalPeriodBtn');
    const weekPeriodBtn = document.getElementById('weekPeriodBtn');
    const monthPeriodBtn = document.getElementById('monthPeriodBtn');

    // 時段資訊元素
    const periodRange = document.getElementById('periodRange');
    const periodViolations = document.getElementById('periodViolations');
    const periodPenalty = document.getElementById('periodPenalty');
    const periodStudents = document.getElementById('periodStudents');

    // 排行榜整合元素
    const enableLeaderboardIntegration = document.getElementById('enableLeaderboardIntegration');
    const dateRangeSelector = document.getElementById('dateRangeSelector');
    const integrationStartDate = document.getElementById('integrationStartDate');
    const integrationEndDate = document.getElementById('integrationEndDate');
    const applyIntegrationBtn = document.getElementById('applyIntegrationBtn');
    const clearIntegrationBtn = document.getElementById('clearIntegrationBtn');
    const integrationStatus = document.getElementById('integrationStatus');
    const integrationStatusText = document.getElementById('integrationStatusText');
    const affectedRecords = document.getElementById('affectedRecords');
    const affectedCount = document.getElementById('affectedCount');
    const affectedPenalty = document.getElementById('affectedPenalty');
    
    const dateFilterStart = document.getElementById('dateFilterStart');
    const dateFilterEnd = document.getElementById('dateFilterEnd');
    const studentFilter = document.getElementById('studentFilter');
    const violationFilter = document.getElementById('violationFilter');
    const applyFilterBtn = document.getElementById('applyFilterBtn');
    const clearFilterBtn = document.getElementById('clearFilterBtn');
    const downloadHistoryBtn = document.getElementById('downloadHistoryBtn');
    const backBtn = document.getElementById('backBtn');
    
    const historyTableBody = document.getElementById('historyTableBody');
    const historyPagination = document.getElementById('historyPagination');
    
    const editViolationModal = document.getElementById('editViolationModal');
    const editStudentNameSpan = document.getElementById('editStudentNameSpan');
    const confirmEditViolationBtn = document.getElementById('confirmEditViolationBtn');
    const cancelEditViolationBtn = document.getElementById('cancelEditViolationBtn');
    const editCustomNoteSection = document.getElementById('editCustomNoteSection');
    const editCustomNote = document.getElementById('editCustomNote');
    const closeModalBtns = document.querySelectorAll('.close');

    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const exportHistoryBtn = document.getElementById('exportHistoryBtn');
    const importFile = document.getElementById('importFile');
    
    // 常數設定
    const HISTORY_ITEMS_PER_PAGE = 20;
    const VIOLATION_PENALTY = 5;
    
    // 違規類型對照表
    const VIOLATION_TYPES = {
        'attitude': '對老師態度不佳',
        'talking_back': '對老師幹部頂嘴',
        'talking_after_bell': '鐘響講話',
        'talking_without_permission': '上課、午餐或午休未經允許講話、未舉手講話',
        'fighting_swearing': '吵架、罵髒話',
        'hitting_kicking': '亂踢人打人',
        'incorrect_corrections': '訂正不確實三次以上',
        'playing_with_violators': '和欠作業或反省的同學玩、講話',
        'missing_homework': '欠作業超過三天',
        'playing_with_toys': '上課玩玩具、文具',
        'chatting_while_cleaning': '掃地時間聊天或玩',
        'talking_in_line': '整隊時講話或玩',
        'littering': '亂丟東西',
        'other': '其它'
    };
    
    // 狀態變數
    let students = [];
    let disciplineHistory = [];
    let filteredHistory = [];
    let currentHistoryPage = 1;
    let editingRecord = null;
    let editSelectedViolation = null;
    let today = new Date().toISOString().split('T')[0];
    let currentPeriod = 'total'; // 'total', 'week', 'month'

    // 排行榜整合設定
    let leaderboardIntegrationConfig = {
        enabled: false,
        startDate: null,
        endDate: null
    };
    
    // 初始化頁面
    function initPage() {
        // 設置當前日期
        const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
        const todayFormatted = new Date().toLocaleDateString('zh-TW', options);
        if (currentDateElement) {
            currentDateElement.textContent = todayFormatted;
        }
        
        // 設置日期篩選器預設值
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        if (dateFilterStart) {
            dateFilterStart.value = firstDayOfMonth.toISOString().split('T')[0];
        }
        if (dateFilterEnd) {
            dateFilterEnd.value = today;
        }
        
        // 載入資料
        loadStudents();
        loadDisciplineHistory();
        
        // 事件監聽器
        setupEventListeners();
        
        // 初始化顯示
        populateStudentFilter();
        loadLeaderboardIntegrationConfig();
        updatePeriodInfo();
        applyHistoryFilter();
        updateSummary();
    }
    
    // 設置事件監聽器
    function setupEventListeners() {
        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', applyHistoryFilter);
        }
        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', clearHistoryFilter);
        }
        if (downloadHistoryBtn) {
            downloadHistoryBtn.addEventListener('click', downloadFilteredHistory);
        }
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                window.location.href = 'discipline.html';
            });
        }

        // 時段切換
        if (totalPeriodBtn) {
            totalPeriodBtn.addEventListener('click', () => switchPeriod('total'));
        }
        if (weekPeriodBtn) {
            weekPeriodBtn.addEventListener('click', () => switchPeriod('week'));
        }
        if (monthPeriodBtn) {
            monthPeriodBtn.addEventListener('click', () => switchPeriod('month'));
        }

        // 排行榜整合功能
        if (enableLeaderboardIntegration) {
            enableLeaderboardIntegration.addEventListener('change', toggleLeaderboardIntegration);
        }
        if (applyIntegrationBtn) {
            applyIntegrationBtn.addEventListener('click', applyLeaderboardIntegration);
        }
        if (clearIntegrationBtn) {
            clearIntegrationBtn.addEventListener('click', clearLeaderboardIntegration);
        }
        
        if (confirmEditViolationBtn) {
            confirmEditViolationBtn.addEventListener('click', confirmEditViolation);
        }
        if (cancelEditViolationBtn) {
            cancelEditViolationBtn.addEventListener('click', closeEditViolationModal);
        }
        
        // 匯入匯出功能
        if (exportBtn) {
            exportBtn.addEventListener('click', exportData);
        }
        if (importBtn) {
            importBtn.addEventListener('click', () => importFile.click());
        }
        if (exportHistoryBtn) {
            exportHistoryBtn.addEventListener('click', exportHistory);
        }
        if (importFile) {
            importFile.addEventListener('change', importData);
        }
        
        // 彈窗關閉
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', closeEditViolationModal);
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === editViolationModal) {
                closeEditViolationModal();
            }
        });
        
        // 違規選項點擊事件
        document.addEventListener('click', function(e) {
            if (e.target.closest('.edit-violation-option')) {
                const option = e.target.closest('.edit-violation-option');
                
                // 重置選擇
                document.querySelectorAll('.edit-violation-option').forEach(opt => opt.classList.remove('selected'));
                
                option.classList.add('selected');
                editSelectedViolation = option.dataset.violation;
                
                // 如果選擇「其它」，顯示自訂說明欄位
                if (editSelectedViolation === 'other') {
                    if (editCustomNoteSection) {
                        editCustomNoteSection.style.display = 'block';
                    }
                    if (editCustomNote) {
                        editCustomNote.required = true;
                    }
                } else {
                    if (editCustomNoteSection) {
                        editCustomNoteSection.style.display = 'none';
                    }
                    if (editCustomNote) {
                        editCustomNote.required = false;
                        editCustomNote.value = '';
                    }
                }
                
                if (confirmEditViolationBtn) {
                    confirmEditViolationBtn.disabled = false;
                }
            }
        });
    }
    
    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
        }
    }
    
    // 載入違規歷史記錄
    function loadDisciplineHistory() {
        const savedHistory = localStorage.getItem('disciplineHistory');
        if (savedHistory) {
            disciplineHistory = JSON.parse(savedHistory);
        }
    }
    
    // 保存違規歷史記錄
    function saveDisciplineHistory() {
        localStorage.setItem('disciplineHistory', JSON.stringify(disciplineHistory));
    }
    
    // 保存學生資料
    function saveStudents() {
        localStorage.setItem('students', JSON.stringify(students));
    }
    
    // 填充學生篩選選項
    function populateStudentFilter() {
        if (!studentFilter) return;
        
        studentFilter.innerHTML = '<option value="">所有學生</option>';
        students.forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = `${student.number} - ${student.name}`;
            studentFilter.appendChild(option);
        });
    }
    
    // 應用歷史記錄篩選
    function applyHistoryFilter() {
        const startDate = dateFilterStart ? dateFilterStart.value : '';
        const endDate = dateFilterEnd ? dateFilterEnd.value : '';
        const studentId = studentFilter ? studentFilter.value : '';
        const violationType = violationFilter ? violationFilter.value : '';
        
        filteredHistory = disciplineHistory.filter(record => {
            // 日期篩選
            if (startDate && record.date < startDate) return false;
            if (endDate && record.date > endDate) return false;
            
            // 學生篩選
            if (studentId && record.studentId !== parseInt(studentId)) return false;
            
            // 違規類型篩選
            if (violationType && record.type !== violationType) return false;
            
            return true;
        });
        
        // 按時間倒序排列
        filteredHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        currentHistoryPage = 1;
        renderHistoryTable();
        renderHistoryPagination();
        updateFilteredStats();
    }
    
    // 清除歷史記錄篩選
    function clearHistoryFilter() {
        if (dateFilterStart) {
            dateFilterStart.value = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
        }
        if (dateFilterEnd) {
            dateFilterEnd.value = today;
        }
        if (studentFilter) {
            studentFilter.value = '';
        }
        if (violationFilter) {
            violationFilter.value = '';
        }
        applyHistoryFilter();
    }
    
    // 渲染歷史記錄表格
    function renderHistoryTable() {
        if (!historyTableBody) return;
        
        if (!filteredHistory.length) {
            historyTableBody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px 0; color: #7f8c8d;">沒有找到符合條件的記錄</td></tr>';
            return;
        }
        
        // 計算當前頁的記錄
        const startIndex = (currentHistoryPage - 1) * HISTORY_ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + HISTORY_ITEMS_PER_PAGE, filteredHistory.length);
        const currentPageRecords = filteredHistory.slice(startIndex, endIndex);
        
        historyTableBody.innerHTML = '';
        
        currentPageRecords.forEach(record => {
            // 找到對應的學生資料
            const student = students.find(s => s.id === record.studentId);
            const studentAnimal = student ? (() => {
                let animalDisplay = student.animal || '🐱';
                if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                    const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                    const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                    animalDisplay = `<div class="student-animal-avatar">
                        <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                        <span class="animal-emoji">${animalInfo.emoji}</span>
                    </div>`;
                }
                return animalDisplay;
            })() : '🐾';
            
            const date = new Date(record.timestamp);
            const dateStr = date.toLocaleDateString('zh-TW');
            const timeStr = date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' });
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${dateStr}</td>
                <td>${timeStr}</td>
                <td>
                    <div class="student-avatar">${studentAnimal}</div>
                </td>
                <td>${record.studentNumber}</td>
                <td>${record.studentName}</td>
                <td>${VIOLATION_TYPES[record.type] || record.type}</td>
                <td style="max-width: 200px; word-wrap: break-word;">${record.description}</td>
                <td class="penalty-count">-${record.penalty}</td>
                <td>
                    <button class="btn btn-sm edit-record-btn" data-id="${record.id}" style="margin-right: 5px;">
                        <i class="fas fa-edit"></i> 編輯
                    </button>
                    <button class="btn btn-sm delete-record-btn" data-id="${record.id}">
                        <i class="fas fa-trash"></i> 刪除
                    </button>
                </td>
            `;
            
            historyTableBody.appendChild(row);
        });
        
        // 添加編輯和刪除按鈕事件監聽器
        document.querySelectorAll('.edit-record-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const recordId = parseInt(this.getAttribute('data-id'));
                showEditViolationModal(recordId);
            });
        });
        
        document.querySelectorAll('.delete-record-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const recordId = parseInt(this.getAttribute('data-id'));
                deleteViolationRecord(recordId);
            });
        });
    }
    
    // 渲染歷史記錄分頁
    function renderHistoryPagination() {
        if (!historyPagination) return;
        
        const totalPages = Math.ceil(filteredHistory.length / HISTORY_ITEMS_PER_PAGE);
        
        if (totalPages <= 1) {
            historyPagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一頁按鈕
        paginationHTML += `
            <button class="history-page-btn" id="historyPrevPage" ${currentHistoryPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 頁碼按鈕
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentHistoryPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="history-page-btn ${i === currentHistoryPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        // 下一頁按鈕
        paginationHTML += `
            <button class="history-page-btn" id="historyNextPage" ${currentHistoryPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        historyPagination.innerHTML = paginationHTML;
        
        // 添加頁面按鈕事件監聽器
        document.querySelectorAll('.history-page-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.id === 'historyPrevPage') {
                    if (currentHistoryPage > 1) changeHistoryPage(currentHistoryPage - 1);
                } else if (this.id === 'historyNextPage') {
                    if (currentHistoryPage < totalPages) changeHistoryPage(currentHistoryPage + 1);
                } else {
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page && !isNaN(page)) changeHistoryPage(page);
                }
            });
        });
    }
    
    // 切換歷史記錄頁面
    function changeHistoryPage(page) {
        if (page < 1 || page > Math.ceil(filteredHistory.length / HISTORY_ITEMS_PER_PAGE)) return;
        
        currentHistoryPage = page;
        renderHistoryTable();
        renderHistoryPagination();
        
        // 滾動到表格頂部
        if (historyTableBody) {
            historyTableBody.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    
    // 更新摘要統計
    function updateSummary() {
        const total = disciplineHistory.length;
        const todayTotal = disciplineHistory.filter(record => record.date === today).length;
        
        // 計算本週記錄
        const now = new Date();
        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
        const weekStart = startOfWeek.toISOString().split('T')[0];
        const weekTotal = disciplineHistory.filter(record => record.date >= weekStart).length;
        
        const totalPenalty = disciplineHistory.reduce((sum, record) => sum + (record.penalty || VIOLATION_PENALTY), 0);
        
        if (totalRecordsElement) totalRecordsElement.textContent = total;
        if (todayRecordsElement) todayRecordsElement.textContent = todayTotal;
        if (weekRecordsElement) weekRecordsElement.textContent = weekTotal;
        if (totalPenaltyElement) totalPenaltyElement.textContent = totalPenalty;
    }
    
    // 更新篩選統計
    function updateFilteredStats() {
        const filteredTotal = filteredHistory.length;
        const filteredPenalty = filteredHistory.reduce((sum, record) => sum + (record.penalty || VIOLATION_PENALTY), 0);
        
        if (filteredTotalElement) filteredTotalElement.textContent = filteredTotal;
        if (filteredPenaltyElement) filteredPenaltyElement.textContent = filteredPenalty;
    }
    
    // 顯示編輯違規記錄彈窗
    function showEditViolationModal(recordId) {
        const record = disciplineHistory.find(r => r.id === recordId);
        if (!record) return;
        
        editingRecord = record;
        editSelectedViolation = null;
        
        // 設置學生名稱
        if (editStudentNameSpan) {
            editStudentNameSpan.textContent = record.studentName;
        }
        
        // 重置選項
        document.querySelectorAll('.edit-violation-option').forEach(opt => opt.classList.remove('selected'));
        
        // 選中當前記錄的違規類型
        const currentOption = document.querySelector(`.edit-violation-option[data-violation="${record.type}"]`);
        if (currentOption) {
            currentOption.classList.add('selected');
            editSelectedViolation = record.type;
        }
        
        // 處理自訂說明
        if (record.type === 'other') {
            if (editCustomNoteSection) {
                editCustomNoteSection.style.display = 'block';
            }
            if (editCustomNote) {
                editCustomNote.value = record.description;
                editCustomNote.required = true;
            }
        } else {
            if (editCustomNoteSection) {
                editCustomNoteSection.style.display = 'none';
            }
            if (editCustomNote) {
                editCustomNote.value = '';
                editCustomNote.required = false;
            }
        }
        
        if (confirmEditViolationBtn) {
            confirmEditViolationBtn.disabled = false;
        }
        if (editViolationModal) {
            editViolationModal.style.display = 'flex';
        }
    }
    
    // 確認編輯違規記錄
    function confirmEditViolation() {
        if (!editingRecord || !editSelectedViolation) {
            closeEditViolationModal();
            return;
        }
        
        // 如果是「其它」類型且沒有填寫說明，則不允許提交
        if (editSelectedViolation === 'other' && (!editCustomNote || !editCustomNote.value.trim())) {
            alert('請填寫違規行為說明');
            return;
        }
        
        // 更新記錄
        editingRecord.type = editSelectedViolation;
        editingRecord.description = editSelectedViolation === 'other' ? 
            editCustomNote.value.trim() : VIOLATION_TYPES[editSelectedViolation];
        
        // 同時更新學生記錄中的對應記錄
        const student = students.find(s => s.id === editingRecord.studentId);
        if (student && student.violations) {
            const studentRecord = student.violations.find(v => v.id === editingRecord.id);
            if (studentRecord) {
                studentRecord.type = editingRecord.type;
                studentRecord.description = editingRecord.description;
            }
        }
        
        // 保存更新
        saveStudents();
        saveDisciplineHistory();
        
        // 更新UI
        applyHistoryFilter();
        updateSummary();
        
        // 顯示成功消息
        const violationText = editSelectedViolation === 'other' ? 
            editCustomNote.value.trim() : VIOLATION_TYPES[editSelectedViolation];
        showNotification(`已修改 ${editingRecord.studentName} 的違規記錄為：${violationText}`, 'success');
        
        closeEditViolationModal();
    }
    
    // 關閉編輯違規彈窗
    function closeEditViolationModal() {
        if (editViolationModal) {
            editViolationModal.style.display = 'none';
        }
        editingRecord = null;
        editSelectedViolation = null;
    }
    
    // 刪除違規記錄
    function deleteViolationRecord(recordId) {
        if (!confirm('確定要刪除這筆違規記錄嗎？這將恢復學生的扣分。')) return;
        
        // 從歷史記錄中找到並刪除
        const recordIndex = disciplineHistory.findIndex(record => record.id === recordId);
        if (recordIndex === -1) return;
        
        const record = disciplineHistory[recordIndex];
        
        // 從學生記錄中刪除
        const student = students.find(s => s.id === record.studentId);
        if (student && student.violations) {
            const studentRecordIndex = student.violations.findIndex(v => v.id === recordId);
            if (studentRecordIndex !== -1) {
                student.violations.splice(studentRecordIndex, 1);
                // 恢復扣分
                student.score = (student.score || 0) + record.penalty;
            }
        }
        
        // 從歷史記錄中刪除
        disciplineHistory.splice(recordIndex, 1);
        
        // 保存更新
        saveStudents();
        saveDisciplineHistory();
        
        // 更新UI
        applyHistoryFilter();
        updateSummary();
        
        showNotification(`已刪除違規記錄，恢復 ${record.penalty} 分給 ${record.studentName}`, 'success');
    }
    
    // 下載篩選後的歷史記錄
    function downloadFilteredHistory() {
        if (!filteredHistory.length) {
            alert('沒有記錄可以下載');
            return;
        }
        
        // 準備CSV數據
        const headers = ['日期', '時間', '座號', '姓名', '違規行為', '說明', '扣分'];
        const csvData = [headers];
        
        filteredHistory.forEach(record => {
            const date = new Date(record.timestamp);
            const dateStr = date.toLocaleDateString('zh-TW');
            const timeStr = date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' });
            
            csvData.push([
                dateStr,
                timeStr,
                record.studentNumber,
                record.studentName,
                VIOLATION_TYPES[record.type] || record.type,
                record.description,
                `-${record.penalty}`
            ]);
        });
        
        // 轉換為CSV格式
        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const BOM = '\uFEFF'; // 添加BOM以支援中文
        const csvBlob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // 下載檔案
        const link = document.createElement('a');
        const url = URL.createObjectURL(csvBlob);
        const startDate = dateFilterStart ? dateFilterStart.value : 'all';
        const endDate = dateFilterEnd ? dateFilterEnd.value : 'all';
        
        link.setAttribute('href', url);
        link.setAttribute('download', `違規記錄_${startDate}_${endDate}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    // 匯出資料
    function exportData() {
        const dataStr = JSON.stringify(students, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `學生名單_${new Date().toISOString().split('T')[0]}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    }
    
    // 匯出歷史記錄 (CSV格式)
    function exportHistory() {
        if (!disciplineHistory.length) {
            alert('沒有違規記錄可以匯出');
            return;
        }

        // 準備CSV數據
        const headers = ['日期', '時間', '座號', '姓名', '違規行為', '說明', '扣分'];
        const csvData = [headers];

        disciplineHistory.forEach(record => {
            const date = new Date(record.timestamp);
            const dateStr = date.toLocaleDateString('zh-TW');
            const timeStr = date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' });

            csvData.push([
                dateStr,
                timeStr,
                record.studentNumber,
                record.studentName,
                VIOLATION_TYPES[record.type] || record.type,
                record.description,
                `-${record.penalty}`
            ]);
        });

        // 轉換為CSV格式
        const csvContent = csvData.map(row =>
            row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
        ).join('\n');
        const BOM = '\uFEFF'; // 添加BOM以支援中文
        const csvBlob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

        // 下載檔案
        const link = document.createElement('a');
        const url = URL.createObjectURL(csvBlob);
        const today = new Date().toISOString().split('T')[0];

        link.setAttribute('href', url);
        link.setAttribute('download', `違規記錄_${today}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理URL
        setTimeout(() => URL.revokeObjectURL(url), 1000);

        // 顯示成功訊息
        alert(`✅ 違規記錄已匯出為 CSV 格式！\n\n檔案名稱：違規記錄_${today}.csv\n記錄數量：${disciplineHistory.length} 筆\n\n檔案已下載到您的下載資料夾中。`);
    }

    // 匯入資料
    function importData(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedStudents = JSON.parse(e.target.result);
                if (Array.isArray(importedStudents)) {
                    if (confirm(`確定要匯入 ${importedStudents.length} 筆學生資料嗎？這將覆蓋現有資料。`)) {
                        students = importedStudents;
                        saveStudents();
                        populateStudentFilter();
                        applyHistoryFilter();
                        updateSummary();
                        alert('資料匯入成功！');
                    }
                } else {
                    alert('匯入的檔案格式不正確！');
                }
            } catch (error) {
                console.error('匯入錯誤:', error);
                alert('匯入檔案時發生錯誤！');
            }
            importFile.value = '';
        };
        reader.readAsText(file);
    }
    
    // 顯示通知消息
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="close-notification">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }
    
    // 獲取通知圖標
    function getNotificationIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    }
    
    // 切換時段
    function switchPeriod(period) {
        currentPeriod = period;

        // 更新按鈕狀態
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeBtn = document.querySelector(`[data-period="${period}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // 更新時段資訊和篩選
        updatePeriodInfo();
        applyHistoryFilter();
    }

    // 獲取時段日期範圍
    function getPeriodDateRange() {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        switch (currentPeriod) {
            case 'week':
                // 本週（週一到週日）
                const dayOfWeek = today.getDay();
                const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                const monday = new Date(today);
                monday.setDate(today.getDate() + mondayOffset);
                const sunday = new Date(monday);
                sunday.setDate(monday.getDate() + 6);
                return { start: monday, end: sunday };

            case 'month':
                // 本月
                const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
                const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                return { start: monthStart, end: monthEnd };

            default:
                // 總違規記錄 - 不限制日期
                return null;
        }
    }

    // 更新時段資訊
    function updatePeriodInfo() {
        const dateRange = getPeriodDateRange();
        let rangeText = '';
        let periodRecords = [];

        switch (currentPeriod) {
            case 'total':
                rangeText = '全部時間';
                periodRecords = disciplineHistory;
                break;
            case 'week':
                if (dateRange) {
                    rangeText = `${formatDate(dateRange.start)} ~ ${formatDate(dateRange.end)}`;
                    periodRecords = disciplineHistory.filter(record => {
                        const recordDate = new Date(record.date);
                        return recordDate >= dateRange.start && recordDate <= dateRange.end;
                    });
                }
                break;
            case 'month':
                if (dateRange) {
                    rangeText = `${dateRange.start.getFullYear()}年${dateRange.start.getMonth() + 1}月`;
                    periodRecords = disciplineHistory.filter(record => {
                        const recordDate = new Date(record.date);
                        return recordDate >= dateRange.start && recordDate <= dateRange.end;
                    });
                }
                break;
        }

        if (periodRange) {
            periodRange.textContent = rangeText;
        }

        // 計算統計資料
        const uniqueStudents = new Set(periodRecords.map(record => record.studentId));
        const totalPenaltyPoints = periodRecords.reduce((sum, record) => sum + (record.score || -5), 0);

        if (periodViolations) {
            periodViolations.textContent = periodRecords.length;
        }
        if (periodPenalty) {
            periodPenalty.textContent = Math.abs(totalPenaltyPoints);
        }
        if (periodStudents) {
            periodStudents.textContent = uniqueStudents.size;
        }
    }

    // 格式化日期
    function formatDate(date) {
        return `${date.getMonth() + 1}/${date.getDate()}`;
    }

    // 載入排行榜整合設定
    function loadLeaderboardIntegrationConfig() {
        const savedConfig = localStorage.getItem('leaderboardIntegrationConfig');
        if (savedConfig) {
            try {
                leaderboardIntegrationConfig = JSON.parse(savedConfig);
                updateLeaderboardIntegrationUI();
            } catch (e) {
                console.warn('Failed to load leaderboard integration config:', e);
            }
        }
    }

    // 儲存排行榜整合設定
    function saveLeaderboardIntegrationConfig() {
        localStorage.setItem('leaderboardIntegrationConfig', JSON.stringify(leaderboardIntegrationConfig));
    }

    // 切換排行榜整合功能
    function toggleLeaderboardIntegration() {
        const isEnabled = enableLeaderboardIntegration.checked;

        if (dateRangeSelector) {
            dateRangeSelector.style.display = isEnabled ? 'block' : 'none';
        }

        if (!isEnabled) {
            clearLeaderboardIntegration();
        } else {
            // 設定預設日期範圍（本月）
            const now = new Date();
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

            if (integrationStartDate) {
                integrationStartDate.value = monthStart.toISOString().split('T')[0];
            }
            if (integrationEndDate) {
                integrationEndDate.value = monthEnd.toISOString().split('T')[0];
            }
        }
    }

    // 套用排行榜整合設定
    function applyLeaderboardIntegration() {
        const startDate = integrationStartDate?.value;
        const endDate = integrationEndDate?.value;

        if (!startDate || !endDate) {
            alert('請選擇開始和結束日期');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            alert('開始日期不能晚於結束日期');
            return;
        }

        leaderboardIntegrationConfig = {
            enabled: true,
            startDate: startDate,
            endDate: endDate
        };

        saveLeaderboardIntegrationConfig();
        updateLeaderboardIntegrationUI();

        // 顯示成功訊息
        alert('排行榜扣分設定已套用！\n\n' +
              `扣分期間：${startDate} ~ ${endDate}\n` +
              '這些違規記錄將會影響學生在排行榜中的分數。');
    }

    // 清除排行榜整合設定
    function clearLeaderboardIntegration() {
        leaderboardIntegrationConfig = {
            enabled: false,
            startDate: null,
            endDate: null
        };

        saveLeaderboardIntegrationConfig();
        updateLeaderboardIntegrationUI();

        if (enableLeaderboardIntegration) {
            enableLeaderboardIntegration.checked = false;
        }
        if (dateRangeSelector) {
            dateRangeSelector.style.display = 'none';
        }
    }

    // 更新排行榜整合UI
    function updateLeaderboardIntegrationUI() {
        if (!integrationStatus || !integrationStatusText) return;

        if (leaderboardIntegrationConfig.enabled) {
            integrationStatus.classList.add('active');
            integrationStatusText.textContent =
                `已啟用違規扣分併入排行榜 (${leaderboardIntegrationConfig.startDate} ~ ${leaderboardIntegrationConfig.endDate})`;

            // 計算影響的記錄
            const affectedViolations = disciplineHistory.filter(record => {
                const recordDate = record.date;
                return recordDate >= leaderboardIntegrationConfig.startDate &&
                       recordDate <= leaderboardIntegrationConfig.endDate;
            });

            const totalAffectedPenalty = affectedViolations.reduce((sum, record) =>
                sum + Math.abs(record.score || 5), 0);

            if (affectedRecords && affectedCount && affectedPenalty) {
                affectedRecords.style.display = 'flex';
                affectedCount.textContent = affectedViolations.length;
                affectedPenalty.textContent = totalAffectedPenalty;
            }

            if (enableLeaderboardIntegration) {
                enableLeaderboardIntegration.checked = true;
            }
            if (dateRangeSelector) {
                dateRangeSelector.style.display = 'block';
            }
            if (integrationStartDate) {
                integrationStartDate.value = leaderboardIntegrationConfig.startDate;
            }
            if (integrationEndDate) {
                integrationEndDate.value = leaderboardIntegrationConfig.endDate;
            }
        } else {
            integrationStatus.classList.remove('active');
            integrationStatusText.textContent = '目前未啟用違規扣分併入排行榜';

            if (affectedRecords) {
                affectedRecords.style.display = 'none';
            }
        }
    }

    // 初始化頁面
    initPage();
});