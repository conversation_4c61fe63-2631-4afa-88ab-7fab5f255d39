<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 榮譽榜</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/leaderboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li><a href="homework-submission.html"><i class="fas fa-book"></i> 📚 作業繳交</a></li>
                    <li><a href="homework-history.html"><i class="fas fa-clipboard-list"></i> 📋 繳交歷史</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li class="active"><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>
            <div class="data-management">
                <h3>📊 資料管理</h3>
                <button id="exportBtn" class="btn"><i class="fas fa-file-export"></i> 匯出資料</button>
                <button id="importBtn" class="btn"><i class="fas fa-file-import"></i> 匯入資料</button>
                <input type="file" id="importFile" accept=".json,.csv" style="display: none;">
            </div>
        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <div class="header-container">
                <h2><i class="fas fa-trophy"></i> 榮譽榜</h2>
                <div class="controls-container">
                    <!-- 時段選擇 -->
                    <div class="period-selector">
                        <button id="totalPeriodBtn" class="period-btn active" data-period="total">
                            <i class="fas fa-trophy"></i> 總排行榜
                        </button>
                        <button id="weekPeriodBtn" class="period-btn" data-period="week">
                            <i class="fas fa-calendar-week"></i> 週排行榜
                        </button>
                        <button id="monthPeriodBtn" class="period-btn" data-period="month">
                            <i class="fas fa-calendar-alt"></i> 月排行榜
                        </button>
                        <button id="semesterPeriodBtn" class="period-btn" data-period="semester">
                            <i class="fas fa-graduation-cap"></i> 學期排行榜
                        </button>
                    </div>

                    <!-- 檢視選項 -->
                    <div class="view-options">
                        <button id="viewAllBtn" class="btn active">全部</button>
                        <button id="viewTop5Btn" class="btn">前五名</button>
                        <button id="viewByClassBtn" class="btn">按班級</button>
                    </div>
                </div>
            </div>

            <!-- 時段資訊顯示 -->
            <div class="period-info" id="periodInfo">
                <div class="period-stats">
                    <div class="stat-card">
                        <i class="fas fa-calendar"></i>
                        <div class="stat-content">
                            <span class="stat-label">統計期間</span>
                            <span class="stat-value" id="periodRange">全部時間</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-users"></i>
                        <div class="stat-content">
                            <span class="stat-label">參與人數</span>
                            <span class="stat-value" id="participantCount">0</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-star"></i>
                        <div class="stat-content">
                            <span class="stat-label">平均分數</span>
                            <span class="stat-value" id="averageScore">0</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-chart-line"></i>
                        <div class="stat-content">
                            <span class="stat-label">最高分數</span>
                            <span class="stat-value" id="highestScore">0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 前五名獎台 -->
            <div class="podium-container" id="top5Podium">
                <div class="podium-place fifth">
                    <div class="podium-stand">
                        <div class="podium-rank">5</div>
                        <div class="podium-avatar" id="fifthPlace">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="podium-info">
                            <div class="podium-name" id="fifthPlaceName">--</div>
                            <div class="podium-score" id="fifthPlaceScore">0 分</div>
                        </div>
                    </div>
                </div>
                <div class="podium-place fourth">
                    <div class="podium-stand">
                        <div class="podium-rank">4</div>
                        <div class="podium-avatar" id="fourthPlace">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="podium-info">
                            <div class="podium-name" id="fourthPlaceName">--</div>
                            <div class="podium-score" id="fourthPlaceScore">0 分</div>
                        </div>
                    </div>
                </div>
                <div class="podium-place second">
                    <div class="podium-stand">
                        <div class="podium-rank">2</div>
                        <div class="podium-avatar" id="secondPlace">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="podium-info">
                            <div class="podium-name" id="secondPlaceName">--</div>
                            <div class="podium-score" id="secondPlaceScore">0 分</div>
                        </div>
                    </div>
                </div>
                <div class="podium-place first">
                    <div class="podium-stand">
                        <div class="podium-rank">1</div>
                        <div class="podium-avatar" id="firstPlace">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="podium-info">
                            <div class="podium-name" id="firstPlaceName">--</div>
                            <div class="podium-score" id="firstPlaceScore">0 分</div>
                        </div>
                    </div>
                </div>
                <div class="podium-place third">
                    <div class="podium-stand">
                        <div class="podium-rank">3</div>
                        <div class="podium-avatar" id="thirdPlace">
                            <i class="fas fa-medal"></i>
                        </div>
                        <div class="podium-info">
                            <div class="podium-name" id="thirdPlaceName">--</div>
                            <div class="podium-score" id="thirdPlaceScore">0 分</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 排行榜表格 -->
            <div class="leaderboard-container">
                <div class="leaderboard-header">
                    <h3 id="leaderboardTitle">完整排行榜</h3>
                    <div class="leaderboard-filters">
                        <select id="classFilter" class="form-control">
                            <option value="">所有班級</option>
                        </select>
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="searchInput" placeholder="搜尋學生...">
                        </div>
                    </div>
                </div>
                
                <div class="table-container">
                    <table id="leaderboardTable">
                        <thead>
                            <tr>
                                <th>排名</th>
                                <th>學號</th>
                                <th>姓名</th>
                                <th>班級</th>
                                <th>分數</th>
                                <th>獎章</th>
                            </tr>
                        </thead>
                        <tbody id="leaderboardTableBody">
                            <!-- 排行榜資料將由 JavaScript 動態生成 -->
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination" id="pagination">
                    <!-- 分頁按鈕將由 JavaScript 動態生成 -->
                </div>
            </div>
            
            <!-- 獎章說明 -->
            <div class="badges-info">
                <h3>獎章說明</h3>
                <div class="badges-grid">
                    <div class="badge-item">
                        <div class="badge gold"><i class="fas fa-trophy"></i></div>
                        <span>第1名</span>
                    </div>
                    <div class="badge-item">
                        <div class="badge silver"><i class="fas fa-medal"></i></div>
                        <span>第2名</span>
                    </div>
                    <div class="badge-item">
                        <div class="badge bronze"><i class="fas fa-award"></i></div>
                        <span>第3名</span>
                    </div>
                    <div class="badge-item">
                        <div class="badge fourth"><i class="fas fa-ribbon"></i></div>
                        <span>第4名</span>
                    </div>
                    <div class="badge-item">
                        <div class="badge fifth"><i class="fas fa-certificate"></i></div>
                        <span>第5名</span>
                    </div>
                    <div class="badge-item">
                        <div class="badge star"><i class="fas fa-star"></i></div>
                        <span>前10%</span>
                    </div>
                    <div class="badge-item">
                        <div class="badge fire"><i class="fas fa-fire"></i></div>
                        <span>進步獎</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 學生詳情彈窗 -->
    <div id="studentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>學生詳情</h3>
            <div class="student-details" id="studentDetails">
                <!-- 學生詳情將由 JavaScript 動態生成 -->
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script src="js/leaderboard.js"></script>
</body>
</html>
