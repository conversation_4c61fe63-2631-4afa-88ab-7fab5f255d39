<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 每日簽到</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/daily-checkin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li class="active"><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li><a href="homework-submission.html"><i class="fas fa-book"></i> 📚 作業繳交</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>
            <div class="data-management">
                <h3>📊 資料管理</h3>
                <button id="exportBtn" class="btn btn-secondary"><i class="fas fa-file-export"></i> 匯出資料</button>
                <button id="importBtn" class="btn btn-secondary"><i class="fas fa-file-import"></i> 匯入資料</button>
                <input type="file" id="importFile" accept=".json" style="display: none;">
            </div>
        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <!-- 浮動裝飾元素 -->
            <div class="floating-decorations">
                <div class="floating-decoration">📅</div>
                <div class="floating-decoration">🐤</div>
                <div class="floating-decoration">☀️</div>
            </div>
            
            <div class="header-container">
                <h2><i class="fas fa-calendar-check"></i> 每日簽到 - 作業繳交確認
                    <span class="title-decoration">
                        <img src="images/animals/chick.png" alt="小雞" class="title-animal-icon">
                    </span>
                </h2>
                <div class="date-display">
                    <span id="currentDate"></span>
                </div>
            </div>
            
            <div class="checkin-summary">
                <div class="summary-card">
                    <i class="fas fa-users"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="totalStudents">0</span>
                        <span class="summary-label">總學生數</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-calendar-check success"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="checkedInCount">0</span>
                        <span class="summary-label">今日已簽到</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-calendar-times warning"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="notCheckedInCount">0</span>
                        <span class="summary-label">未簽到</span>
                    </div>
                </div>
            </div>
            
            <!-- 簽到統計區域 -->
            <div class="statistics-section">
                <h3><i class="fas fa-chart-bar"></i> 簽到統計分析</h3>
                <div class="statistics-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="weeklyRate">0%</span>
                            <span class="stat-label">本週簽到率</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="monthlyRate">0%</span>
                            <span class="stat-label">本月簽到率</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="averageStreak">0</span>
                            <span class="stat-label">平均連續天數</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="topPerformer">--</span>
                            <span class="stat-label">簽到王</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button id="checkAllBtn" class="btn btn-primary">
                    <i class="fas fa-check-double"></i> 全部簽到
                </button>
                <button id="uncheckAllBtn" class="btn btn-secondary">
                    <i class="fas fa-undo"></i> 全部取消
                </button>
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="搜尋學生...">
                </div>
            </div>
            
            <div class="table-container">
                <table id="checkinTable">
                    <thead>
                        <tr>
                            <th>學號</th>
                            <th>姓名</th>
                            <th>目前分數</th>
                            <th>簽到狀態</th>
                            <th>簽到時間</th>
                            <th>連續天數</th>
                            <th>本月率</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="checkinTableBody">
                        <!-- 學生簽到資料將由 JavaScript 動態生成 -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination" id="pagination">
                <!-- 分頁按鈕將由 JavaScript 動態生成 -->
            </div>
        </main>
    </div>

    <!-- 簽到確認彈窗 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>確認作業繳交簽到</h3>
            <p id="confirmMessage">確定要為 <span id="studentNameSpan"></span> 登記作業繳交簽到嗎？</p>
            <div class="reward-info">
                <i class="fas fa-gift"></i>
                <span>作業繳交簽到可獲得 <strong>5 分</strong> 獎勵！</span>
            </div>
            <div class="modal-buttons">
                <button id="confirmCheckinBtn" class="btn btn-primary">確認簽到</button>
                <button id="cancelCheckinBtn" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script src="js/animal-config.js"></script>
    <script src="js/script.js"></script>
    <script src="js/daily-checkin.js"></script>
</body>
</html>
