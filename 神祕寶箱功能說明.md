# 🎁 神祕寶箱功能說明

## 功能概述

神祕寶箱是記分小達人系統的一個隨機事件功能，當學生獲得加分時會自動觸發，為學習過程增添趣味性和不確定性。

## 觸發條件

- 當學生分數**增加**時（無論是快速加分還是自訂加分）
- 系統會自動顯示「神祕寶箱」彈窗
- 學生可以選擇「開啟寶箱」或「跳過」

## 隨機事件類型

### 🍀 強運 (Lucky)
- **效果**：當前獲得的分數翻倍
- **範例**：原本+3分變成+6分
- **機率**：30%
- **顏色**：綠色 (#00b894)

### 💰 掠奪 (Steal)
- **效果**：從其他隨機選定的學生身上偷取5分加到自己身上
- **條件**：需要有其他學生分數≥5分
- **機率**：22%
- **顏色**：橙色 (#fdcb6e)

### 😇 天使 (Angel)
- **效果**：獲得一次「答錯免扣分」的保護狀態
- **顯示**：學生卡片會顯示天使圖示和保護狀態
- **機率**：21%
- **顏色**：藍色 (#74b9ff)

### 😈 惡魔 (Devil)
- **效果**：當前學生的總分數減半（無條件捨去）
- **範例**：15分變成7分
- **機率**：5%
- **顏色**：紅色 (#ff7675)

### 🔄 交換 (Swap)
- **效果**：與隨機選定的另一位學生完全交換總分數
- **條件**：需要有其他學生存在
- **機率**：15%
- **顏色**：紫色 (#a29bfe)

### 🎪 馬戲團 (Circus)
- **效果**：所有學生的分數都增加1分（大家一起受益）
- **機率**：8%
- **顏色**：彩虹色 (#ff6b9d)
- **特色**：團體受益，增進班級和諧

### 🌟 流星雨 (Meteor Shower)
- **效果**：幫自己以外的一位同學加1分
- **條件**：需要有其他學生存在
- **機率**：10%
- **顏色**：星空藍 (#2c3e50)
- **特色**：互助精神，選擇幫助對象

## 技術實現

### 核心函數

1. **showMysteryBoxTrigger(student, originalChange)**
   - 顯示神祕寶箱觸發彈窗
   - 包含開啟和跳過選項

2. **triggerMysteryBoxEvent(student, originalChange, modal)**
   - 隨機選擇事件類型
   - 播放開箱動畫

3. **executeMysteryBoxEvent(student, originalChange, event, modal)**
   - 執行具體的事件效果
   - 更新學生分數和狀態

4. **showMysteryBoxResult(event, message, affectedStudents, modal)**
   - 顯示事件結果
   - 包含動畫效果

### 特殊狀態管理

- 天使保護狀態存儲在 `student.specialStates.angelProtection`
- 在學生卡片上顯示特殊狀態圖示
- 提供檢查和使用保護狀態的函數

### 歷史記錄

所有神祕寶箱事件都會記錄到分數歷史中：
- 原因標記為 'mystery_box'
- 詳細說明包含事件類型和效果
- 支援歷史查詢和過濾

## UI/UX 設計

### 視覺效果

1. **寶箱動畫**
   - 3D寶箱造型
   - 開啟動畫效果
   - 閃爍特效

2. **結果展示**
   - 大型事件圖示
   - 彩色主題配色
   - 動畫進入效果

3. **特殊狀態**
   - 天使保護圖示
   - 發光動畫效果
   - 狀態標籤

### 響應式設計

- 支援手機和平板設備
- 彈窗自適應螢幕大小
- 觸控友好的按鈕設計

## 使用方式

### 在手動加減分頁面

1. 選擇學生進行加分操作
2. 確認分數調整後，系統自動檢測是否為加分
3. 如果是加分，顯示神祕寶箱彈窗
4. 點擊「開啟寶箱」觸發隨機事件
5. 查看事件結果並確認

### 測試功能

使用 `mystery-box-test.html` 頁面可以：
- 測試所有事件類型
- 查看視覺效果
- 驗證功能邏輯

## 配置選項

### 事件機率

目前所有事件機率相等（各20%），可以通過修改 `MYSTERY_BOX_EVENTS` 對象來調整。

### 音效支援

預留了音效播放接口，可以添加：
- 開箱音效
- 結果音效
- 背景音樂

### 自訂事件

可以通過擴展 `MYSTERY_BOX_EVENTS` 對象來添加新的事件類型。

## 注意事項

1. **數據一致性**：所有分數變更都會正確保存到 localStorage
2. **錯誤處理**：包含完整的錯誤處理機制
3. **性能優化**：動畫使用 CSS3 硬體加速
4. **相容性**：支援現代瀏覽器的所有功能

## 未來擴展

- 添加更多事件類型
- 實現事件機率配置
- 添加音效和背景音樂
- 支援多語言界面
- 添加事件統計功能
