<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 作業繳交</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/homework-submission.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li class="active"><a href="homework-submission.html"><i class="fas fa-book"></i> 📚 作業繳交</a></li>
                    <li><a href="homework-history.html"><i class="fas fa-clipboard-list"></i> 📋 繳交歷史</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>
            <div class="data-management">
                <h3>📊 資料管理</h3>
                <button id="exportBtn" class="btn btn-secondary"><i class="fas fa-file-export"></i> 匯出資料</button>
                <button id="importBtn" class="btn btn-secondary"><i class="fas fa-file-import"></i> 匯入資料</button>
                <input type="file" id="importFile" accept=".json" style="display: none;">
            </div>
        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <div class="header-container">
                <h2><i class="fas fa-book"></i> 作業繳交</h2>
                <div class="date-display">
                    <span id="currentDate"></span>
                </div>
            </div>
            
            <!-- 常用作業庫管理 -->
            <div class="frequent-homework-management">
                <h3><i class="fas fa-bookmark"></i> 📚 常用作業庫</h3>
                <div class="frequent-homework-input-section">
                    <div class="input-group">
                        <input type="text" id="frequentHomeworkInput" placeholder="輸入常用作業項目，例如：數學習作、英語作業、自然觀察日記..." maxlength="50">
                        <button id="addFrequentHomeworkBtn" class="btn btn-info">
                            <i class="fas fa-bookmark-plus"></i> 添加到常用庫
                        </button>
                    </div>
                </div>
                <div class="frequent-homework-items" id="frequentHomeworkItems">
                    <!-- 常用作業項目將由 JavaScript 動態生成 -->
                    <p class="empty-frequent-hint">
                        <i class="fas fa-lightbulb"></i> 
                        添加常用作業項目，下次可快速選用！
                    </p>
                </div>
            </div>

            <!-- 今日作業項目管理 -->
            <div class="homework-management">
                <h3><i class="fas fa-clipboard-check"></i> 📋 今日作業項目</h3>
                <div class="homework-input-section">
                    <div class="input-group">
                        <input type="text" id="homeworkInput" placeholder="輸入作業項目，例如：聯絡簿、國語甲本、數習二頁..." maxlength="50">
                        <button id="addHomeworkBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增作業
                        </button>
                    </div>
                </div>
                <div class="homework-items" id="homeworkItems">
                    <!-- 作業項目將由 JavaScript 動態生成 -->
                    <p class="empty-today-hint">
                        <i class="fas fa-arrow-up"></i> 
                        點擊上方常用作業庫的項目可快速添加，或手動輸入新的作業項目！
                    </p>
                </div>
            </div>

            <div class="homework-summary">
                <div class="summary-card">
                    <i class="fas fa-clipboard-check"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="totalHomeworkItems">0</span>
                        <span class="summary-label">今日作業項目</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-users"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="totalStudents">0</span>
                        <span class="summary-label">總學生數</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-chart-line success"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="completionRate">0%</span>
                        <span class="summary-label">總體完成率</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-medal primary"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="todayPoints">0</span>
                        <span class="summary-label">今日總獲得分數</span>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="搜尋學生...">
                </div>
                <button id="markAllSubmittedBtn" class="btn btn-success">
                    <i class="fas fa-check-double"></i> 全部繳交
                </button>
                <button id="markAllNotSubmittedBtn" class="btn btn-warning">
                    <i class="fas fa-undo"></i> 全部取消
                </button>
                <button id="viewHistoryBtn" class="btn btn-info" onclick="window.location.href='homework-history.html'">
                    <i class="fas fa-history"></i> 查看繳交歷史
                </button>
                <button id="downloadHistoryBtn" class="btn btn-secondary">
                    <i class="fas fa-download"></i> 下載記錄
                </button>
            </div>
            
            <div class="table-container">
                <table id="homeworkTable">
                    <thead>
                        <tr>
                            <th>頭像</th>
                            <th>座號</th>
                            <th>姓名</th>
                            <th>目前分數</th>
                            <th id="homeworkColumns">作業項目繳交狀態</th>
                            <th>完成度</th>
                            <th>今日得分</th>
                        </tr>
                    </thead>
                    <tbody id="homeworkTableBody">
                        <!-- 學生作業資料將由 JavaScript 動態生成 -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination" id="pagination">
                <!-- 分頁按鈕將由 JavaScript 動態生成 -->
            </div>
        </main>
    </div>

    <!-- 作業繳交確認彈窗 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>確認作業繳交</h3>
            <p id="confirmMessage">確定要為 <span id="studentNameSpan"></span> 登記「<span id="homeworkItemSpan"></span>」的繳交嗎？</p>
            <div class="reward-info">
                <i class="fas fa-gift"></i>
                <span>每項作業繳交可獲得 <strong>3 分</strong> 獎勵！</span>
            </div>
            <div class="modal-buttons">
                <button id="confirmSubmissionBtn" class="btn btn-primary">確認繳交</button>
                <button id="cancelSubmissionBtn" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script src="js/animal-config.js"></script>
    <script src="js/script.js"></script>
    <script src="js/homework-submission.js"></script>
</body>
</html>