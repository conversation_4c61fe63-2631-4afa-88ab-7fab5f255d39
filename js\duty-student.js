// 值日生工作系統
class DutyStudentSystem {
    constructor() {
        this.students = [];
        this.dutyStudentData = {};
        
        this.init();
    }

    init() {
        this.loadData();
        this.renderStudents();
        this.updateStats();
        this.renderTodayRecords();
        this.renderMonthlyStats();
        this.setupEventListeners();
    }

    // 載入資料
    loadData() {
        // 從localStorage載入學生資料
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            this.students = JSON.parse(savedStudents);
        }

        // 從localStorage載入值日生工作資料
        const savedDutyStudent = localStorage.getItem('dutyStudentData');
        if (savedDutyStudent) {
            this.dutyStudentData = JSON.parse(savedDutyStudent);
        }
    }

    // 儲存資料
    saveData() {
        localStorage.setItem('students', JSON.stringify(this.students));
        localStorage.setItem('dutyStudentData', JSON.stringify(this.dutyStudentData));
    }

    // 獲取今日日期字串
    getTodayString() {
        return new Date().toLocaleDateString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        }).replace(/\//g, '-');
    }

    // 獲取本月日期字串
    getThisMonthString() {
        const now = new Date();
        return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    }

    // 記錄值日生工作
    recordDutyStudent(studentId, performance) {
        const today = this.getTodayString();
        const timestamp = new Date().toLocaleTimeString('zh-TW', { 
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });

        // 初始化學生的值日生工作資料
        if (!this.dutyStudentData[studentId]) {
            this.dutyStudentData[studentId] = {};
        }

        // 記錄今日值日生工作
        this.dutyStudentData[studentId][today] = {
            performance: performance,
            timestamp: timestamp,
            fullTimestamp: new Date().toISOString()
        };

        // 更新學生分數
        const student = this.students.find(s => s.id === studentId);
        if (student) {
            const points = performance === 'excellent' ? 5 : 3;
            student.score = (student.score || 0) + points;
        }

        this.saveData();
        this.updateStats();
        this.renderTodayRecords();
        this.renderMonthlyStats();
        this.renderStudents();

        // 顯示成功訊息
        this.showSuccessMessage(student, performance, points);
    }

    // 修改值日生工作記錄
    updateDutyStudentRecord(studentId, date, newPerformance) {
        const student = this.students.find(s => s.id === studentId);
        if (!student || !this.dutyStudentData[studentId] || !this.dutyStudentData[studentId][date]) {
            return false;
        }

        const oldRecord = this.dutyStudentData[studentId][date];
        const oldPoints = oldRecord.performance === 'excellent' ? 5 : 3;
        const newPoints = newPerformance === 'excellent' ? 5 : 3;

        // 更新記錄
        this.dutyStudentData[studentId][date].performance = newPerformance;

        // 調整分數
        student.score = (student.score || 0) - oldPoints + newPoints;

        this.saveData();
        this.updateStats();
        this.renderTodayRecords();
        this.renderMonthlyStats();
        this.renderStudents();

        return true;
    }

    // 刪除值日生工作記錄
    deleteDutyStudentRecord(studentId, date) {
        const student = this.students.find(s => s.id === studentId);
        if (!student || !this.dutyStudentData[studentId] || !this.dutyStudentData[studentId][date]) {
            return false;
        }

        const record = this.dutyStudentData[studentId][date];
        const points = record.performance === 'excellent' ? 5 : 3;

        // 恢復分數
        student.score = (student.score || 0) - points;

        // 刪除記錄
        delete this.dutyStudentData[studentId][date];

        // 如果該學生沒有任何記錄，刪除整個學生資料
        if (Object.keys(this.dutyStudentData[studentId]).length === 0) {
            delete this.dutyStudentData[studentId];
        }

        this.saveData();
        this.updateStats();
        this.renderTodayRecords();
        this.renderMonthlyStats();
        this.renderStudents();

        return true;
    }

    // 檢查學生今日是否已記錄
    hasRecordToday(studentId) {
        const today = this.getTodayString();
        return this.dutyStudentData[studentId] && this.dutyStudentData[studentId][today];
    }

    // 獲取學生今日記錄
    getTodayRecord(studentId) {
        const today = this.getTodayString();
        return this.dutyStudentData[studentId] && this.dutyStudentData[studentId][today];
    }

    // 渲染學生卡片
    renderStudents() {
        const studentsGrid = document.getElementById('studentsGrid');
        if (!studentsGrid) return;

        studentsGrid.innerHTML = '';

        this.students.forEach(student => {
            const hasRecord = this.hasRecordToday(student.id);
            const todayRecord = this.getTodayRecord(student.id);

            const studentCard = document.createElement('div');
            studentCard.className = 'student-card';
            // 創建動物頭像顯示
            let animalDisplay = student.animal || '🐾';
            if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                animalDisplay = `
                    <div class="student-animal-avatar" title="${animalInfo.name} - ${animalInfo.personality}">
                        <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                        <span class="animal-emoji">${animalInfo.emoji}</span>
                    </div>
                `;
            } else if (typeof student.animal === 'string') {
                // 如果是emoji，直接顯示
                animalDisplay = `<div class="student-animal-emoji">${student.animal}</div>`;
            }

            studentCard.innerHTML = `
                <div class="student-header">
                    <div class="student-avatar">${animalDisplay}</div>
                    <div class="student-info">
                        <h4>${student.name}</h4>
                        <span class="student-number">學號：${student.number}</span>
                    </div>
                </div>
                ${hasRecord ? `
                    <div class="record-status">
                        <div class="performance-badge ${todayRecord.performance}">
                            ${todayRecord.performance === 'excellent' ? '🌟 自動自發完成 (+5分)' : '🔔 要人提醒才完成 (+3分)'}
                        </div>
                        <small>記錄時間：${todayRecord.timestamp}</small>
                    </div>
                ` : `
                    <div class="student-actions">
                        <button class="btn btn-excellent" onclick="dutyStudentSystem.recordDutyStudent(${student.id}, 'excellent')">
                            🌟 自動自發 (+5分)
                        </button>
                        <button class="btn btn-good" onclick="dutyStudentSystem.recordDutyStudent(${student.id}, 'good')">
                            🔔 要人提醒 (+3分)
                        </button>
                    </div>
                `}
            `;

            studentsGrid.appendChild(studentCard);
        });
    }

    // 更新統計數據
    updateStats() {
        const today = this.getTodayString();
        let todayExcellent = 0;
        let todayGood = 0;

        // 統計今日資料
        Object.values(this.dutyStudentData).forEach(studentData => {
            if (studentData[today]) {
                if (studentData[today].performance === 'excellent') {
                    todayExcellent++;
                } else if (studentData[today].performance === 'good') {
                    todayGood++;
                }
            }
        });

        // 更新統計顯示
        const todayExcellentCount = document.getElementById('todayExcellentCount');
        const todayGoodCount = document.getElementById('todayGoodCount');
        const todayTotalCount = document.getElementById('todayTotalCount');

        if (todayExcellentCount) todayExcellentCount.textContent = todayExcellent;
        if (todayGoodCount) todayGoodCount.textContent = todayGood;
        if (todayTotalCount) todayTotalCount.textContent = todayExcellent + todayGood;
    }

    // 渲染今日記錄
    renderTodayRecords() {
        const recordsTableBody = document.getElementById('recordsTableBody');
        if (!recordsTableBody) return;

        recordsTableBody.innerHTML = '';
        const today = this.getTodayString();

        // 獲取今日所有記錄
        const todayRecords = [];
        Object.keys(this.dutyStudentData).forEach(studentId => {
            const studentData = this.dutyStudentData[studentId];
            if (studentData[today]) {
                const student = this.students.find(s => s.id == studentId);
                if (student) {
                    todayRecords.push({
                        student: student,
                        record: studentData[today]
                    });
                }
            }
        });

        // 按時間排序（最新的在前）
        todayRecords.sort((a, b) => {
            return new Date(b.record.fullTimestamp) - new Date(a.record.fullTimestamp);
        });

        todayRecords.forEach(({student, record}) => {
            const row = document.createElement('tr');
            const points = record.performance === 'excellent' ? 5 : 3;
            const performanceText = record.performance === 'excellent' ? '自動自發完成' : '要人提醒才完成';
            
            // 創建動物頭像顯示
            let animalDisplay = student.animal || '🐾';
            if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                animalDisplay = `
                    <div class="student-animal-avatar" title="${animalInfo.name} - ${animalInfo.personality}">
                        <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                        <span class="animal-emoji">${animalInfo.emoji}</span>
                    </div>
                `;
            } else if (typeof student.animal === 'string') {
                // 如果是emoji，直接顯示
                animalDisplay = `<div class="student-animal-emoji">${student.animal}</div>`;
            }
            
            row.innerHTML = `
                <td class="animal-cell">${animalDisplay}</td>
                <td>${student.number}</td>
                <td>${student.name}</td>
                <td>
                    <span class="performance-badge ${record.performance}">
                        ${performanceText}
                    </span>
                </td>
                <td><span class="score-badge">+${points}分</span></td>
                <td>${record.timestamp}</td>
                <td>
                    <button class="btn btn-sm btn-edit" onclick="dutyStudentSystem.openEditModal(${student.id}, '${today}')">
                        <i class="fas fa-edit"></i> 修改
                    </button>
                </td>
            `;
            recordsTableBody.appendChild(row);
        });

        if (todayRecords.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="7" style="text-align: center; color: #666; padding: 20px;">
                    📝 今日尚無值日生工作記錄
                </td>
            `;
            recordsTableBody.appendChild(row);
        }
    }

    // 渲染本月統計
    renderMonthlyStats() {
        const monthlySummary = document.getElementById('monthlySummary');
        if (!monthlySummary) return;

        const thisMonth = this.getThisMonthString();
        let monthlyExcellent = 0;
        let monthlyGood = 0;
        let activeStudents = new Set();

        // 統計本月資料
        Object.keys(this.dutyStudentData).forEach(studentId => {
            const studentData = this.dutyStudentData[studentId];
            Object.keys(studentData).forEach(date => {
                if (date.startsWith(thisMonth)) {
                    activeStudents.add(studentId);
                    if (studentData[date].performance === 'excellent') {
                        monthlyExcellent++;
                    } else if (studentData[date].performance === 'good') {
                        monthlyGood++;
                    }
                }
            });
        });

        const totalRecords = monthlyExcellent + monthlyGood;
        const excellentPercentage = totalRecords > 0 ? ((monthlyExcellent / totalRecords) * 100).toFixed(1) : 0;
        const averagePerStudent = activeStudents.size > 0 ? (totalRecords / activeStudents.size).toFixed(1) : 0;

        monthlySummary.innerHTML = `
            <div class="summary-item">
                <div>🌟 <strong>自動自發次數</strong></div>
                <div>${monthlyExcellent} 次</div>
            </div>
            <div class="summary-item">
                <div>🔔 <strong>要人提醒次數</strong></div>
                <div>${monthlyGood} 次</div>
            </div>
            <div class="summary-item">
                <div>📊 <strong>自發比例</strong></div>
                <div>${excellentPercentage}%</div>
            </div>
            <div class="summary-item">
                <div>👥 <strong>參與人數</strong></div>
                <div>${activeStudents.size} 人</div>
            </div>
            <div class="summary-item">
                <div>📈 <strong>平均工作次數</strong></div>
                <div>${averagePerStudent} 次/人</div>
            </div>
        `;
    }

    // 顯示成功訊息
    showSuccessMessage(student, performance, points) {
        const performanceText = performance === 'excellent' ? '自動自發完成' : '要人提醒才完成';
        alert(`✅ 記錄成功！\n\n${student.animal} ${student.name}\n${performanceText} +${points}分\n\n勤勞的值日生！`);
    }

    // 開啟編輯彈窗
    openEditModal(studentId, date) {
        const student = this.students.find(s => s.id === studentId);
        const record = this.dutyStudentData[studentId] && this.dutyStudentData[studentId][date];
        
        if (!student || !record) return;

        // 創建動物頭像顯示
        let animalDisplay = student.animal || '🐾';
        if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
            const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
            const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
            animalDisplay = `
                <div class="student-animal-avatar" title="${animalInfo.name} - ${animalInfo.personality}">
                    <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                    <span class="animal-emoji">${animalInfo.emoji}</span>
                </div>
            `;
        } else if (typeof student.animal === 'string') {
            // 如果是emoji，直接顯示
            animalDisplay = `<div class="student-animal-emoji">${student.animal}</div>`;
        }

        // 填入資料
        document.getElementById('editStudentId').value = studentId;
        document.getElementById('editDate').value = date;
        document.getElementById('editStudentInfo').innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                <span style="font-size: 1.5em;">${animalDisplay}</span>
                <div>
                    <strong>${student.name}</strong><br>
                    <small>學號：${student.number} | 日期：${date}</small>
                </div>
            </div>
        `;

        // 設定選中狀態
        const performanceRadios = document.querySelectorAll('input[name="performance"]');
        performanceRadios.forEach(radio => {
            radio.checked = radio.value === record.performance;
        });

        // 顯示彈窗
        document.getElementById('editModal').style.display = 'block';
    }

    // 設置事件監聽器
    setupEventListeners() {
        // 關閉彈窗
        const closeBtn = document.querySelector('.close');
        const cancelBtn = document.getElementById('cancelEditBtn');
        
        if (closeBtn) {
            closeBtn.onclick = () => {
                document.getElementById('editModal').style.display = 'none';
            };
        }
        
        if (cancelBtn) {
            cancelBtn.onclick = () => {
                document.getElementById('editModal').style.display = 'none';
            };
        }

        // 點擊彈窗外部關閉
        window.onclick = (event) => {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        };

        // 編輯表單提交
        const editForm = document.getElementById('editForm');
        if (editForm) {
            editForm.onsubmit = (e) => {
                e.preventDefault();
                
                const studentId = parseInt(document.getElementById('editStudentId').value);
                const date = document.getElementById('editDate').value;
                const newPerformance = document.querySelector('input[name="performance"]:checked').value;
                
                if (this.updateDutyStudentRecord(studentId, date, newPerformance)) {
                    alert('✅ 記錄修改成功！');
                    document.getElementById('editModal').style.display = 'none';
                } else {
                    alert('❌ 修改失敗，請稍後再試。');
                }
            };
        }

        // 刪除記錄按鈕
        const deleteBtn = document.getElementById('deleteRecordBtn');
        if (deleteBtn) {
            deleteBtn.onclick = () => {
                if (confirm('確定要刪除這筆值日生工作記錄嗎？\n\n刪除後將恢復對應的分數，此操作無法復原。')) {
                    const studentId = parseInt(document.getElementById('editStudentId').value);
                    const date = document.getElementById('editDate').value;
                    
                    if (this.deleteDutyStudentRecord(studentId, date)) {
                        alert('✅ 記錄已刪除，分數已恢復！');
                        document.getElementById('editModal').style.display = 'none';
                    } else {
                        alert('❌ 刪除失敗，請稍後再試。');
                    }
                }
            };
        }
    }
}

// 全域變數
let dutyStudentSystem;

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    dutyStudentSystem = new DutyStudentSystem();
});