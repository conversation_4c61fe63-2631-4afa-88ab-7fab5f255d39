// 秩序禮儀功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const disciplineTableBody = document.getElementById('disciplineTableBody');
    const searchInput = document.getElementById('searchInput');
    const resetBtn = document.getElementById('resetBtn');
    const violationModal = document.getElementById('violationModal');
    const historyModal = document.getElementById('historyModal');
    const editViolationModal = document.getElementById('editViolationModal');
    const studentNameSpan = document.getElementById('studentNameSpan');
    const confirmViolationBtn = document.getElementById('confirmViolationBtn');
    const cancelViolationBtn = document.getElementById('cancelViolationBtn');
    const customNoteSection = document.getElementById('customNoteSection');
    const customNote = document.getElementById('customNote');
    const editStudentNameSpan = document.getElementById('editStudentNameSpan');
    const confirmEditViolationBtn = document.getElementById('confirmEditViolationBtn');
    const cancelEditViolationBtn = document.getElementById('cancelEditViolationBtn');
    const editCustomNoteSection = document.getElementById('editCustomNoteSection');
    const editCustomNote = document.getElementById('editCustomNote');
    const closeModalBtns = document.querySelectorAll('.close');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const exportHistoryBtn = document.getElementById('exportHistoryBtn');
    const importFile = document.getElementById('importFile');
    const currentDateElement = document.getElementById('currentDate');
    const totalStudentsElement = document.getElementById('totalStudents');
    const todayViolationsElement = document.getElementById('todayViolations');
    const totalViolationsElement = document.getElementById('totalViolations');
    const totalPenaltyElement = document.getElementById('totalPenalty');
    const paginationElement = document.getElementById('pagination');
    
    // 歷史記錄相關元素
    const dateFilterStart = document.getElementById('dateFilterStart');
    const dateFilterEnd = document.getElementById('dateFilterEnd');
    const studentFilter = document.getElementById('studentFilter');
    const violationFilter = document.getElementById('violationFilter');
    const applyFilterBtn = document.getElementById('applyFilterBtn');
    const clearFilterBtn = document.getElementById('clearFilterBtn');
    const downloadHistoryBtn = document.getElementById('downloadHistoryBtn');
    const historyTotal = document.getElementById('historyTotal');
    const historyPenalty = document.getElementById('historyPenalty');
    const historyTableBody = document.getElementById('historyTableBody');
    const historyPagination = document.getElementById('historyPagination');

    // 匯出格式選擇彈窗相關元素
    const exportModal = document.getElementById('exportModal');
    const exportHistoryModal = document.getElementById('exportHistoryModal');
    const exportModalClose = document.getElementById('exportModalClose');
    const exportHistoryModalClose = document.getElementById('exportHistoryModalClose');
    
    // 常數設定
    const ITEMS_PER_PAGE = 10; // 每頁顯示的學生數量
    const HISTORY_ITEMS_PER_PAGE = 20; // 每頁顯示的歷史記錄數量
    const VIOLATION_PENALTY = 5; // 每次違規扣分
    
    // 違規類型對照表
    const VIOLATION_TYPES = {
        'attitude': '對老師態度不佳',
        'talking_back': '對老師幹部頂嘴',
        'talking_after_bell': '鐘響講話',
        'talking_without_permission': '上課、午餐或午休未經允許講話、未舉手講話',
        'fighting_swearing': '吵架、罵髒話',
        'hitting_kicking': '亂踢人打人',
        'incorrect_corrections': '訂正不確實三次以上',
        'playing_with_violators': '和欠作業或反省的同學玩、講話',
        'missing_homework': '欠作業超過三天',
        'playing_with_toys': '上課玩玩具、文具',
        'chatting_while_cleaning': '掃地時間聊天或玩',
        'talking_in_line': '整隊時講話或玩',
        'littering': '亂丟東西',
        'other': '其它'
    };
    
    // 狀態變數
    let students = [];
    let filteredStudents = [];
    let disciplineHistory = []; // 違規歷史記錄
    let filteredHistory = [];
    let currentPage = 1;
    let currentHistoryPage = 1;
    let selectedStudent = null;
    let selectedViolation = null;
    let editingRecord = null;  // 正在編輯的記錄
    let editSelectedViolation = null;  // 編輯時選擇的違規類型
    let today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    
    // 顯示重置確認對話框
    function showResetConfirmation() {
        const password = prompt('請輸入重置密碼：');
        if (password === '0718') {
            if (confirm('確定要重置所有違規記錄嗎？這個操作無法復原！\n\n這將會：\n- 清除所有學生的違規記錄\n- 恢復所有學生因違規被扣的分數\n- 清除所有歷史違規記錄')) {
                resetAllRecords();
            }
        } else if (password !== null) { // 使用者有輸入密碼但錯誤
            alert('密碼錯誤！');
        }
    }
    
    // 重置所有記錄
    function resetAllRecords() {
        try {
            // 記錄重置前的統計
            const totalViolationsBefore = disciplineHistory.length;
            const totalPenaltyBefore = disciplineHistory.reduce((total, record) => total + record.penalty, 0);
            
            // 清除所有學生的違規記錄並恢復分數
            students.forEach(student => {
                if (student.violations && student.violations.length > 0) {
                    // 計算該學生的總違規扣分
                    const studentPenalty = student.violations.reduce((total, violation) => total + (violation.penalty || VIOLATION_PENALTY), 0);
                    // 恢復分數
                    student.score = (student.score || 0) + studentPenalty;
                    // 清除違規記錄
                    student.violations = [];
                }
            });
            
            // 清除歷史違規記錄
            disciplineHistory = [];
            filteredHistory = [];
            
            // 保存資料
            saveStudents();
            saveDisciplineHistory();
            
            // 更新頁面顯示
            filterStudents();
            updateSummary();
            populateStudentFilter();
            
            // 顯示重置成功訊息
            alert(`重置完成！\n\n統計資訊：\n- 清除了 ${totalViolationsBefore} 筆違規記錄\n- 恢復了 ${totalPenaltyBefore} 分扣分\n- 所有學生記錄已重置`);
            
        } catch (error) {
            console.error('重置過程中發生錯誤:', error);
            alert('重置過程中發生錯誤，請重新操作或聯繫管理員。');
        }
    }
    
    // 初始化頁面
    function initPage() {
        // 設置當前日期
        const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
        const todayFormatted = new Date().toLocaleDateString('zh-TW', options);
        if (currentDateElement) {
            currentDateElement.textContent = todayFormatted;
        }
        
        // 設置日期篩選器預設值
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        if (dateFilterStart) {
            dateFilterStart.value = firstDayOfMonth.toISOString().split('T')[0];
        }
        if (dateFilterEnd) {
            dateFilterEnd.value = today;
        }
        
        // 載入學生資料和違規記錄
        loadStudents();
        loadDisciplineHistory();

        // 初始化歷史記錄篩選和顯示
        applyHistoryFilter();
        
        // 事件監聽器
        if (searchInput) {
            searchInput.addEventListener('input', filterStudents);
        }
        if (resetBtn) {
            resetBtn.addEventListener('click', showResetConfirmation);
        }
        if (confirmViolationBtn) {
            confirmViolationBtn.addEventListener('click', confirmViolation);
        }
        if (cancelViolationBtn) {
            cancelViolationBtn.addEventListener('click', closeViolationModal);
        }
        if (confirmEditViolationBtn) {
            confirmEditViolationBtn.addEventListener('click', confirmEditViolation);
        }
        if (cancelEditViolationBtn) {
            cancelEditViolationBtn.addEventListener('click', closeEditViolationModal);
        }
        
        // 歷史記錄按鈕
        const historyBtn = document.getElementById('historyBtn');
        if (historyBtn) {
            historyBtn.addEventListener('click', function() {
                window.location.href = 'discipline-history.html';
            });
        }
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                closeViolationModal();
                closeHistoryModal();
                closeEditViolationModal();
            });
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === violationModal) {
                closeViolationModal();
            }
            const historyModalElement = document.getElementById('historyModal');
            if (e.target === historyModalElement) {
                closeHistoryModal();
            }
            if (e.target === editViolationModal) {
                closeEditViolationModal();
            }
            if (e.target === exportModal) {
                closeExportModal();
            }
            if (e.target === exportHistoryModal) {
                closeExportHistoryModal();
            }
        });

        // 匯入匯出功能
        if (exportBtn) {
            exportBtn.addEventListener('click', showExportModal);
        }
        if (importBtn) {
            importBtn.addEventListener('click', () => importFile.click());
        }
        if (exportHistoryBtn) {
            exportHistoryBtn.addEventListener('click', showExportHistoryModal);
        }
        if (importFile) {
            importFile.addEventListener('change', importData);
        }

        // 匯出格式選擇彈窗事件監聽器
        if (exportModalClose) {
            exportModalClose.addEventListener('click', closeExportModal);
        }
        if (exportHistoryModalClose) {
            exportHistoryModalClose.addEventListener('click', closeExportHistoryModal);
        }

        // 匯出格式選擇事件
        document.addEventListener('click', function(e) {
            if (e.target.closest('#exportModal .export-option button')) {
                const format = e.target.closest('.export-option').dataset.format;
                exportData(format);
                closeExportModal();
            }
            if (e.target.closest('#exportHistoryModal .export-option button')) {
                const format = e.target.closest('.export-option').dataset.format;
                exportHistory(format);
                closeExportHistoryModal();
            }
        });
        
        // 歷史記錄篩選
        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', applyHistoryFilter);
        }
        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', clearHistoryFilter);
        }
        if (downloadHistoryBtn) {
            downloadHistoryBtn.addEventListener('click', downloadFilteredHistory);
        }
        
        // 違規選項點擊事件
        document.addEventListener('click', function(e) {
            // 處理新增違規的選項點擊
            if (e.target.closest('.violation-option')) {
                const option = e.target.closest('.violation-option');
                
                // 重置選擇
                document.querySelectorAll('.violation-option').forEach(opt => opt.classList.remove('selected'));
                
                option.classList.add('selected');
                selectedViolation = option.dataset.violation;
                
                // 如果選擇「其它」，顯示自訂說明欄位
                if (selectedViolation === 'other') {
                    if (customNoteSection) {
                        customNoteSection.style.display = 'block';
                    }
                    if (customNote) {
                        customNote.required = true;
                    }
                } else {
                    if (customNoteSection) {
                        customNoteSection.style.display = 'none';
                    }
                    if (customNote) {
                        customNote.required = false;
                        customNote.value = '';
                    }
                }
                
                if (confirmViolationBtn) {
                    confirmViolationBtn.disabled = false;
                }
            }
            
            // 處理編輯違規的選項點擊
            if (e.target.closest('.edit-violation-option')) {
                const option = e.target.closest('.edit-violation-option');
                
                // 重置選擇
                document.querySelectorAll('.edit-violation-option').forEach(opt => opt.classList.remove('selected'));
                
                option.classList.add('selected');
                editSelectedViolation = option.dataset.violation;
                
                // 如果選擇「其它」，顯示自訂說明欄位
                if (editSelectedViolation === 'other') {
                    if (editCustomNoteSection) {
                        editCustomNoteSection.style.display = 'block';
                    }
                    if (editCustomNote) {
                        editCustomNote.required = true;
                    }
                } else {
                    if (editCustomNoteSection) {
                        editCustomNoteSection.style.display = 'none';
                    }
                    if (editCustomNote) {
                        editCustomNote.required = false;
                        editCustomNote.value = '';
                    }
                }
                
                if (confirmEditViolationBtn) {
                    confirmEditViolationBtn.disabled = false;
                }
            }
        });
    }
    
    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
            
            // 初始化每個學生的違規記錄
            students.forEach(student => {
                if (!student.violations) {
                    student.violations = [];
                }
            });
            
            // 保存更新後的學生資料
            saveStudents();
            
            // 填充學生篩選選項
            populateStudentFilter();
            
            // 過濾和顯示學生
            filterStudents();
            updateSummary();
        } else {
            alert('找不到學生資料，請先新增學生。');
            window.location.href = 'index.html';
        }
    }
    
    // 載入違規歷史記錄
    function loadDisciplineHistory() {
        const savedHistory = localStorage.getItem('disciplineHistory');
        if (savedHistory) {
            disciplineHistory = JSON.parse(savedHistory);
        }
    }
    
    // 保存違規歷史記錄
    function saveDisciplineHistory() {
        localStorage.setItem('disciplineHistory', JSON.stringify(disciplineHistory));
    }
    
    // 填充學生篩選選項
    function populateStudentFilter() {
        studentFilter.innerHTML = '<option value="">所有學生</option>';
        students.forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = `${student.number} - ${student.name}`;
            studentFilter.appendChild(option);
        });
    }
    
    // 過濾學生
    function filterStudents() {
        const searchTerm = searchInput.value.toLowerCase();
        
        filteredStudents = students.filter(student => {
            return (
                student.name.toLowerCase().includes(searchTerm) ||
                student.number.toString().includes(searchTerm) ||
                student.class.toString().includes(searchTerm)
            );
        });
        
        // 重置到第一頁
        currentPage = 1;
        renderStudentTable();
        renderPagination();
        updateSummary();
    }
    
    // 渲染學生表格
    function renderStudentTable() {
        if (!filteredStudents.length) {
            disciplineTableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 30px 0;">沒有找到符合條件的學生</td></tr>';
            return;
        }
        
        // 計算當前頁的學生
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, filteredStudents.length);
        const currentPageStudents = filteredStudents.slice(startIndex, endIndex);
        
        disciplineTableBody.innerHTML = '';
        
        currentPageStudents.forEach(student => {
            const todayViolations = getTodayViolations(student);
            const totalViolations = student.violations ? student.violations.length : 0;
            const totalPenalty = totalViolations * VIOLATION_PENALTY;
            
            const row = document.createElement('tr');
            
            // 動物頭像
            let animalDisplay = student.animal || '🐱';
            if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                animalDisplay = `<div class="student-animal-avatar">
                    <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                    <span class="animal-emoji">${animalInfo.emoji}</span>
                </div>`;
            }
            const animalAvatar = animalDisplay;
            
            row.innerHTML = `
                <td>
                    <div class="student-avatar">${animalAvatar}</div>
                </td>
                <td>${student.number}</td>
                <td>${student.name}</td>
                <td class="score">${student.score || 0} 分</td>
                <td class="violation-count ${todayViolations > 0 ? 'has-violations' : ''}">${todayViolations}</td>
                <td class="violation-count ${totalViolations > 0 ? 'has-violations' : ''}">${totalViolations}</td>
                <td class="penalty-count">${totalPenalty}</td>
                <td>
                    <button class="btn btn-sm violation-btn" data-id="${student.number}">
                        <i class="fas fa-exclamation-triangle"></i> 記錄違規
                    </button>
                </td>
            `;
            
            disciplineTableBody.appendChild(row);
        });
        
        // 添加違規按鈕事件監聽器
        document.querySelectorAll('.violation-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 添加點擊視覺反饋
                this.classList.add('clicked');
                setTimeout(() => this.classList.remove('clicked'), 300);

                const studentId = parseInt(this.getAttribute('data-id'));
                showViolationModal(studentId);
            });
        });
    }
    
    // 獲取學生今日違規次數
    function getTodayViolations(student) {
        if (!student.violations) return 0;
        
        return student.violations.filter(violation => {
            const violationDate = new Date(violation.timestamp).toISOString().split('T')[0];
            return violationDate === today;
        }).length;
    }
    
    // 渲染分頁
    function renderPagination() {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        
        if (totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一頁按鈕
        paginationHTML += `
            <button class="page-btn" id="prevPage" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 頁碼按鈕
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        if (startPage > 1) {
            paginationHTML += `
                <button class="page-btn" data-page="1">1</button>
                ${startPage > 2 ? '<span class="ellipsis">...</span>' : ''}
            `;
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        if (endPage < totalPages) {
            paginationHTML += `
                ${endPage < totalPages - 1 ? '<span class="ellipsis">...</span>' : ''}
                <button class="page-btn" data-page="${totalPages}">${totalPages}</button>
            `;
        }
        
        // 下一頁按鈕
        paginationHTML += `
            <button class="page-btn" id="nextPage" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        paginationElement.innerHTML = paginationHTML;
        
        // 添加頁面按鈕事件監聽器
        document.querySelectorAll('.page-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.id === 'prevPage') {
                    if (currentPage > 1) changePage(currentPage - 1);
                } else if (this.id === 'nextPage') {
                    if (currentPage < totalPages) changePage(currentPage + 1);
                } else {
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page && !isNaN(page)) changePage(page);
                }
            });
        });
    }
    
    // 切換頁面
    function changePage(page) {
        if (page < 1 || page > Math.ceil(filteredStudents.length / ITEMS_PER_PAGE)) return;
        
        currentPage = page;
        renderStudentTable();
        renderPagination();
        
        // 滾動到表格頂部
        disciplineTableBody.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
    
    // 顯示違規記錄彈窗
    function showViolationModal(studentNumber) {
        const student = students.find(s => s.number === studentNumber || s.id === studentNumber);
        if (!student) {
            console.error('找不到學生:', studentNumber);
            showMessage('找不到學生資料', 'error');
            return;
        }

        selectedStudent = student;
        selectedViolation = null;
        studentNameSpan.textContent = student.name;
        confirmViolationBtn.disabled = true;

        // 重置選項
        document.querySelectorAll('.violation-option').forEach(opt => opt.classList.remove('selected'));
        customNoteSection.style.display = 'none';
        customNote.value = '';
        customNote.required = false;

        violationModal.style.display = 'flex';

        // 確認彈窗已顯示
        console.log('違規記錄彈窗已顯示，學生:', student.name);

        // 聚焦到彈窗以確保可見性
        violationModal.focus();
    }
    
    // 確認違規記錄
    function confirmViolation() {
        if (!selectedStudent || !selectedViolation) {
            closeViolationModal();
            return;
        }
        
        // 如果是「其它」類型且沒有填寫說明，則不允許提交
        if (selectedViolation === 'other' && !customNote.value.trim()) {
            alert('請填寫違規行為說明');
            return;
        }
        
        const student = students.find(s => s.number === selectedStudent.number);
        if (student) {
            const violationRecord = {
                id: Date.now(), // 使用時間戳作為唯一ID
                studentId: student.id,
                studentName: student.name,
                studentNumber: student.number,
                type: selectedViolation,
                description: selectedViolation === 'other' ? customNote.value.trim() : VIOLATION_TYPES[selectedViolation],
                timestamp: new Date().toISOString(),
                penalty: VIOLATION_PENALTY,
                date: today
            };
            
            // 添加到學生違規記錄
            if (!student.violations) {
                student.violations = [];
            }
            student.violations.push(violationRecord);
            
            // 添加到歷史記錄
            disciplineHistory.push(violationRecord);
            
            // 扣除分數
            student.score = Math.max(0, (student.score || 0) - VIOLATION_PENALTY);
            
            // 保存更新
            saveStudents();
            saveDisciplineHistory();
            
            // 更新UI
            renderStudentTable();
            updateSummary();
            
            // 顯示成功消息
            const violationText = selectedViolation === 'other' ? customNote.value.trim() : VIOLATION_TYPES[selectedViolation];
            showNotification(`已記錄 ${student.name} 的違規行為：${violationText}，扣除 ${VIOLATION_PENALTY} 分`, 'warning');
        }
        
        closeViolationModal();
    }
    
    // 顯示歷史記錄彈窗
    function showHistoryModal() {
        console.log('顯示歷史記錄彈窗'); // 調試用
        
        // 確保元素存在
        const historyModalElement = document.getElementById('historyModal');
        if (!historyModalElement) {
            console.error('找不到 historyModal 元素');
            return;
        }
        
        applyHistoryFilter(); // 應用當前篩選條件
        historyModalElement.style.display = 'flex';
    }
    
    // 應用歷史記錄篩選
    function applyHistoryFilter() {
        console.log('正在應用歷史記錄篩選...'); // 調試用
        console.log('總違規記錄數:', disciplineHistory.length); // 調試用
        
        const startDate = dateFilterStart ? dateFilterStart.value : '';
        const endDate = dateFilterEnd ? dateFilterEnd.value : '';
        const studentId = studentFilter ? studentFilter.value : '';
        const violationType = violationFilter ? violationFilter.value : '';
        
        console.log('篩選條件:', { startDate, endDate, studentId, violationType }); // 調試用
        
        filteredHistory = disciplineHistory.filter(record => {
            // 日期篩選
            if (startDate && record.date < startDate) return false;
            if (endDate && record.date > endDate) return false;
            
            // 學生篩選
            if (studentId && record.studentId !== parseInt(studentId)) return false;
            
            // 違規類型篩選
            if (violationType && record.type !== violationType) return false;
            
            return true;
        });
        
        console.log('篩選後記錄數:', filteredHistory.length); // 調試用
        
        // 按時間倒序排列
        filteredHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        currentHistoryPage = 1;
        renderHistoryTable();
        renderHistoryPagination();
        updateHistoryStats();
    }
    
    // 清除歷史記錄篩選
    function clearHistoryFilter() {
        dateFilterStart.value = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
        dateFilterEnd.value = today;
        studentFilter.value = '';
        violationFilter.value = '';
        applyHistoryFilter();
    }
    
    // 渲染歷史記錄表格
    function renderHistoryTable() {
        console.log('正在渲染歷史記錄表格...'); // 調試用
        console.log('historyTableBody 元素:', historyTableBody); // 調試用
        
        if (!historyTableBody) {
            console.error('找不到 historyTableBody 元素');
            return;
        }
        
        if (!filteredHistory.length) {
            console.log('沒有歷史記錄顯示'); // 調試用
            historyTableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 30px 0;">沒有找到符合條件的記錄</td></tr>';
            return;
        }
        
        // 計算當前頁的記錄
        const startIndex = (currentHistoryPage - 1) * HISTORY_ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + HISTORY_ITEMS_PER_PAGE, filteredHistory.length);
        const currentPageRecords = filteredHistory.slice(startIndex, endIndex);
        
        console.log('當前頁記錄:', currentPageRecords); // 調試用
        
        historyTableBody.innerHTML = '';
        
        currentPageRecords.forEach(record => {
            const date = new Date(record.timestamp);
            const dateStr = date.toLocaleDateString('zh-TW');
            const timeStr = date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' });
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${dateStr}</td>
                <td>${timeStr}</td>
                <td>${record.studentNumber} - ${record.studentName}</td>
                <td>${VIOLATION_TYPES[record.type] || record.type}</td>
                <td>${record.description}</td>
                <td class="penalty-count">-${record.penalty}</td>
                <td>
                    <button class="btn btn-sm edit-record-btn" data-id="${record.id}" style="margin-right: 5px;">
                        <i class="fas fa-edit"></i> 編輯
                    </button>
                    <button class="btn btn-sm delete-record-btn" data-id="${record.id}">
                        <i class="fas fa-trash"></i> 刪除
                    </button>
                </td>
            `;
            
            historyTableBody.appendChild(row);
        });
        
        // 添加編輯和刪除按鈕事件監聽器
        document.querySelectorAll('.edit-record-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const recordId = parseInt(this.getAttribute('data-id'));
                showEditViolationModal(recordId);
            });
        });
        document.querySelectorAll('.delete-record-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const recordId = parseInt(this.getAttribute('data-id'));
                deleteViolationRecord(recordId);
            });
        });
    }
    
    // 渲染歷史記錄分頁
    function renderHistoryPagination() {
        const totalPages = Math.ceil(filteredHistory.length / HISTORY_ITEMS_PER_PAGE);
        
        if (totalPages <= 1) {
            historyPagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一頁按鈕
        paginationHTML += `
            <button class="history-page-btn" id="historyPrevPage" ${currentHistoryPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 頁碼按鈕
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentHistoryPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="history-page-btn ${i === currentHistoryPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        // 下一頁按鈕
        paginationHTML += `
            <button class="history-page-btn" id="historyNextPage" ${currentHistoryPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        historyPagination.innerHTML = paginationHTML;
        
        // 添加頁面按鈕事件監聽器
        document.querySelectorAll('.history-page-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.id === 'historyPrevPage') {
                    if (currentHistoryPage > 1) changeHistoryPage(currentHistoryPage - 1);
                } else if (this.id === 'historyNextPage') {
                    if (currentHistoryPage < totalPages) changeHistoryPage(currentHistoryPage + 1);
                } else {
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page && !isNaN(page)) changeHistoryPage(page);
                }
            });
        });
    }
    
    // 切換歷史記錄頁面
    function changeHistoryPage(page) {
        if (page < 1 || page > Math.ceil(filteredHistory.length / HISTORY_ITEMS_PER_PAGE)) return;
        
        currentHistoryPage = page;
        renderHistoryTable();
        renderHistoryPagination();
    }
    
    // 更新歷史記錄統計
    function updateHistoryStats() {
        historyTotal.textContent = filteredHistory.length;
        historyPenalty.textContent = filteredHistory.reduce((total, record) => total + record.penalty, 0);
    }
    
    // 刪除違規記錄
    function deleteViolationRecord(recordId) {
        if (!confirm('確定要刪除這筆違規記錄嗎？這將恢復學生的扣分。')) return;
        
        // 從歷史記錄中找到並刪除
        const recordIndex = disciplineHistory.findIndex(record => record.id === recordId);
        if (recordIndex === -1) return;
        
        const record = disciplineHistory[recordIndex];
        
        // 從學生記錄中刪除
        const student = students.find(s => s.id === record.studentId);
        if (student && student.violations) {
            const studentRecordIndex = student.violations.findIndex(v => v.id === recordId);
            if (studentRecordIndex !== -1) {
                student.violations.splice(studentRecordIndex, 1);
                // 恢復扣分
                student.score = (student.score || 0) + record.penalty;
            }
        }
        
        // 從歷史記錄中刪除
        disciplineHistory.splice(recordIndex, 1);
        
        // 保存更新
        saveStudents();
        saveDisciplineHistory();
        
        // 更新UI
        renderStudentTable();
        updateSummary();
        applyHistoryFilter(); // 重新渲染歷史記錄
        
        showNotification(`已刪除違規記錄，恢復 ${record.penalty} 分給 ${record.studentName}`, 'success');
    }
    
    // 下載篩選後的歷史記錄
    function downloadFilteredHistory() {
        console.log('開始下載歷史記錄...'); // 調試用
        console.log('filteredHistory 長度:', filteredHistory.length); // 調試用

        if (!filteredHistory.length) {
            alert('沒有記錄可以下載');
            return;
        }
        
        // 準備CSV數據
        const headers = ['日期', '時間', '學號', '姓名', '違規行為', '說明', '扣分'];
        const csvData = [headers];
        
        filteredHistory.forEach(record => {
            const date = new Date(record.timestamp);
            const dateStr = date.toLocaleDateString('zh-TW');
            const timeStr = date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' });
            
            csvData.push([
                dateStr,
                timeStr,
                record.studentNumber,
                record.studentName,
                VIOLATION_TYPES[record.type] || record.type,
                record.description,
                `-${record.penalty}`
            ]);
        });
        
        // 轉換為CSV格式
        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const BOM = '\uFEFF'; // 添加BOM以支援中文
        const csvBlob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // 下載檔案
        const link = document.createElement('a');
        const url = URL.createObjectURL(csvBlob);
        link.setAttribute('href', url);

        // 生成檔案名稱
        const startDate = dateFilterStart ? dateFilterStart.value : '';
        const endDate = dateFilterEnd ? dateFilterEnd.value : '';
        const today = new Date().toISOString().split('T')[0];
        const fileName = startDate && endDate ?
            `違規記錄_${startDate}_${endDate}.csv` :
            `違規記錄_${today}.csv`;

        link.setAttribute('download', fileName);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
    
    // 更新摘要信息
    function updateSummary() {
        const total = students.length;
        const todayTotal = students.reduce((sum, student) => sum + getTodayViolations(student), 0);
        const totalViolations = disciplineHistory.length;
        const totalPenalty = totalViolations * VIOLATION_PENALTY;
        
        totalStudentsElement.textContent = total;
        todayViolationsElement.textContent = todayTotal;
        totalViolationsElement.textContent = totalViolations;
        totalPenaltyElement.textContent = totalPenalty;
    }
    
    // 保存學生資料到本地存儲
    function saveStudents() {
        localStorage.setItem('students', JSON.stringify(students));
    }
    
    // 關閉違規彈窗
    function closeViolationModal() {
        violationModal.style.display = 'none';
        selectedStudent = null;
        selectedViolation = null;
    }
    
    // 顯示編輯違規記錄彈窗
    function showEditViolationModal(recordId) {
        const record = disciplineHistory.find(r => r.id === recordId);
        if (!record) return;
        
        editingRecord = record;
        editSelectedViolation = null;
        
        // 設置學生名稱
        editStudentNameSpan.textContent = record.studentName;
        
        // 重置選項
        document.querySelectorAll('.edit-violation-option').forEach(opt => opt.classList.remove('selected'));
        
        // 選中當前記錄的違規類型
        const currentOption = document.querySelector(`.edit-violation-option[data-violation="${record.type}"]`);
        if (currentOption) {
            currentOption.classList.add('selected');
            editSelectedViolation = record.type;
        }
        
        // 處理自訂說明
        if (record.type === 'other') {
            editCustomNoteSection.style.display = 'block';
            editCustomNote.value = record.description;
            editCustomNote.required = true;
        } else {
            editCustomNoteSection.style.display = 'none';
            editCustomNote.value = '';
            editCustomNote.required = false;
        }
        
        confirmEditViolationBtn.disabled = false;
        editViolationModal.style.display = 'flex';
    }
    
    // 確認編輯違規記錄
    function confirmEditViolation() {
        if (!editingRecord || !editSelectedViolation) {
            closeEditViolationModal();
            return;
        }
        
        // 如果是「其它」類型且沒有填寫說明，則不允許提交
        if (editSelectedViolation === 'other' && !editCustomNote.value.trim()) {
            alert('請填寫違規行為說明');
            return;
        }
        
        // 保存原始記錄的時間戳和其他不變的資訊
        const originalTimestamp = editingRecord.timestamp;
        const originalDate = editingRecord.date;
        const originalId = editingRecord.id;
        const originalStudentId = editingRecord.studentId;
        const originalStudentName = editingRecord.studentName;
        const originalStudentNumber = editingRecord.studentNumber;
        
        // 更新記錄
        editingRecord.type = editSelectedViolation;
        editingRecord.description = editSelectedViolation === 'other' ? 
            editCustomNote.value.trim() : VIOLATION_TYPES[editSelectedViolation];
        
        // 同時更新學生記錄中的對應記錄
        const student = students.find(s => s.id === editingRecord.studentId);
        if (student && student.violations) {
            const studentRecord = student.violations.find(v => v.id === editingRecord.id);
            if (studentRecord) {
                studentRecord.type = editingRecord.type;
                studentRecord.description = editingRecord.description;
            }
        }
        
        // 保存更新
        saveStudents();
        saveDisciplineHistory();
        
        // 更新UI
        renderStudentTable();
        updateSummary();
        applyHistoryFilter(); // 重新渲染歷史記錄
        
        // 顯示成功消息
        const violationText = editSelectedViolation === 'other' ? 
            editCustomNote.value.trim() : VIOLATION_TYPES[editSelectedViolation];
        showNotification(`已修改 ${editingRecord.studentName} 的違規記錄為：${violationText}`, 'success');
        
        closeEditViolationModal();
    }
    
    // 關閉編輯違規彈窗
    function closeEditViolationModal() {
        editViolationModal.style.display = 'none';
        editingRecord = null;
        editSelectedViolation = null;
    }
    
    // 關閉歷史記錄彈窗
    function closeHistoryModal() {
        const historyModalElement = document.getElementById('historyModal');
        if (historyModalElement) {
            historyModalElement.style.display = 'none';
        }
    }

    // 顯示匯出格式選擇彈窗
    function showExportModal() {
        if (exportModal) {
            exportModal.style.display = 'flex';
        }
    }

    // 關閉匯出格式選擇彈窗
    function closeExportModal() {
        if (exportModal) {
            exportModal.style.display = 'none';
        }
    }

    // 顯示違規記錄匯出格式選擇彈窗
    function showExportHistoryModal() {
        if (exportHistoryModal) {
            exportHistoryModal.style.display = 'flex';
        }
    }

    // 關閉違規記錄匯出格式選擇彈窗
    function closeExportHistoryModal() {
        if (exportHistoryModal) {
            exportHistoryModal.style.display = 'none';
        }
    }
    
    // 顯示通知消息
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="close-notification">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }
    
    // 獲取通知圖標
    function getNotificationIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    }
    
    // 匯出資料
    function exportData(format = 'json') {
        const timestamp = new Date().toISOString().split('T')[0];

        switch (format) {
            case 'json':
                exportDataAsJSON(timestamp);
                break;
            case 'csv':
                exportDataAsCSV(timestamp);
                break;
            case 'excel':
                exportDataAsExcel(timestamp);
                break;
            default:
                exportDataAsJSON(timestamp);
        }
    }

    // 匯出為 JSON 格式
    function exportDataAsJSON(timestamp) {
        const dataStr = JSON.stringify(students, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
        const exportFileDefaultName = `學生名單_${timestamp}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    }

    // 匯出為 CSV 格式
    function exportDataAsCSV(timestamp) {
        const headers = ['學號', '姓名', '班級', '動物', '目前分數', '今日違規', '總違規次數', '總扣分'];
        const csvData = [headers];

        students.forEach(student => {
            const todayViolations = getTodayViolations(student);
            const totalViolations = student.violations ? student.violations.length : 0;
            const totalPenalty = totalViolations * VIOLATION_PENALTY;

            csvData.push([
                student.number,
                student.name,
                student.class,
                student.animal || '🐾',
                student.score || 0,
                todayViolations,
                totalViolations,
                totalPenalty
            ]);
        });

        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const BOM = '\uFEFF';
        const csvBlob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        const url = URL.createObjectURL(csvBlob);
        link.setAttribute('href', url);
        link.setAttribute('download', `學生名單_${timestamp}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    // 匯出為 Excel 格式
    function exportDataAsExcel(timestamp) {
        const workbook = XLSX.utils.book_new();

        // 準備學生資料
        const studentData = students.map(student => {
            const todayViolations = getTodayViolations(student);
            const totalViolations = student.violations ? student.violations.length : 0;
            const totalPenalty = totalViolations * VIOLATION_PENALTY;

            return {
                '學號': student.number,
                '姓名': student.name,
                '班級': student.class,
                '動物': student.animal || '🐾',
                '目前分數': student.score || 0,
                '今日違規': todayViolations,
                '總違規次數': totalViolations,
                '總扣分': totalPenalty
            };
        });

        const worksheet = XLSX.utils.json_to_sheet(studentData);

        // 設定欄寬
        const colWidths = [
            { wch: 8 },  // 學號
            { wch: 12 }, // 姓名
            { wch: 8 },  // 班級
            { wch: 8 },  // 動物
            { wch: 10 }, // 目前分數
            { wch: 10 }, // 今日違規
            { wch: 12 }, // 總違規次數
            { wch: 10 }  // 總扣分
        ];
        worksheet['!cols'] = colWidths;

        XLSX.utils.book_append_sheet(workbook, worksheet, '學生名單');
        XLSX.writeFile(workbook, `學生名單_${timestamp}.xlsx`);
    }
    
    // 匯出歷史記錄
    function exportHistory(format = 'json') {
        const timestamp = new Date().toISOString().split('T')[0];

        switch (format) {
            case 'json':
                exportHistoryAsJSON(timestamp);
                break;
            case 'csv':
                exportHistoryAsCSV(timestamp);
                break;
            case 'excel':
                exportHistoryAsExcel(timestamp);
                break;
            default:
                exportHistoryAsJSON(timestamp);
        }
    }

    // 匯出歷史記錄為 JSON 格式
    function exportHistoryAsJSON(timestamp) {
        console.log('匯出 JSON 格式歷史記錄...'); // 調試用
        console.log('disciplineHistory 長度:', disciplineHistory.length); // 調試用

        if (disciplineHistory.length === 0) {
            alert('沒有違規記錄可以匯出');
            return;
        }

        const dataStr = JSON.stringify(disciplineHistory, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
        const exportFileDefaultName = `違規記錄_${timestamp}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();

        console.log('JSON 匯出完成'); // 調試用
    }

    // 匯出歷史記錄為 CSV 格式
    function exportHistoryAsCSV(timestamp) {
        console.log('匯出 CSV 格式歷史記錄...'); // 調試用
        console.log('disciplineHistory 長度:', disciplineHistory.length); // 調試用

        if (disciplineHistory.length === 0) {
            alert('沒有違規記錄可以匯出');
            return;
        }

        const headers = ['日期', '時間', '學號', '姓名', '違規行為', '說明', '扣分'];
        const csvData = [headers];

        disciplineHistory.forEach(record => {
            const date = new Date(record.timestamp);
            const dateStr = date.toLocaleDateString('zh-TW');
            const timeStr = date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' });

            csvData.push([
                dateStr,
                timeStr,
                record.studentNumber,
                record.studentName,
                VIOLATION_TYPES[record.type] || record.type,
                record.description,
                `-${record.penalty}`
            ]);
        });

        const csvContent = csvData.map(row => row.join(',')).join('\n');
        const BOM = '\uFEFF';
        const csvBlob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

        const link = document.createElement('a');
        const url = URL.createObjectURL(csvBlob);
        link.setAttribute('href', url);
        link.setAttribute('download', `違規記錄_${timestamp}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    // 匯出歷史記錄為 Excel 格式
    function exportHistoryAsExcel(timestamp) {
        console.log('匯出 Excel 格式歷史記錄...'); // 調試用
        console.log('disciplineHistory 長度:', disciplineHistory.length); // 調試用

        if (disciplineHistory.length === 0) {
            alert('沒有違規記錄可以匯出');
            return;
        }

        const workbook = XLSX.utils.book_new();

        // 準備歷史記錄資料
        const historyData = disciplineHistory.map(record => {
            const date = new Date(record.timestamp);
            const dateStr = date.toLocaleDateString('zh-TW');
            const timeStr = date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' });

            return {
                '日期': dateStr,
                '時間': timeStr,
                '學號': record.studentNumber,
                '姓名': record.studentName,
                '違規行為': VIOLATION_TYPES[record.type] || record.type,
                '說明': record.description,
                '扣分': `-${record.penalty}`
            };
        });

        const worksheet = XLSX.utils.json_to_sheet(historyData);

        // 設定欄寬
        const colWidths = [
            { wch: 12 }, // 日期
            { wch: 8 },  // 時間
            { wch: 8 },  // 學號
            { wch: 12 }, // 姓名
            { wch: 20 }, // 違規行為
            { wch: 30 }, // 說明
            { wch: 8 }   // 扣分
        ];
        worksheet['!cols'] = colWidths;

        XLSX.utils.book_append_sheet(workbook, worksheet, '違規記錄');
        XLSX.writeFile(workbook, `違規記錄_${timestamp}.xlsx`);
    }

    // 匯入資料
    function importData(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedStudents = JSON.parse(e.target.result);
                if (Array.isArray(importedStudents)) {
                    if (confirm(`確定要匯入 ${importedStudents.length} 筆學生資料嗎？這將覆蓋現有資料。`)) {
                        students = importedStudents;

                        // 初始化每個學生的違規記錄
                        students.forEach(student => {
                            if (!student.violations) {
                                student.violations = [];
                            }
                        });

                        saveStudents();
                        populateStudentFilter();
                        filterStudents();
                        updateSummary();
                        alert('資料匯入成功！');
                    }
                } else {
                    alert('匯入的檔案格式不正確！');
                }
            } catch (error) {
                console.error('匯入錯誤:', error);
                alert('匯入檔案時發生錯誤！');
            }
            importFile.value = '';
        };
        reader.readAsText(file);
    }
    
    // 初始化頁面
    initPage();
});