/* 不妄語頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.date-display {
    background: var(--card-background);
    padding: 10px 20px;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-soft);
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
    border: 2px solid var(--candy-lemon-deep);
}

/* 誠實統計摘要卡片 */
.honesty-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    padding: 20px;
    box-shadow: var(--shadow-soft);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    border: 3px solid transparent;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.summary-card i {
    font-size: 2.5rem;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.summary-card i.fa-users {
    background: var(--gradient-lavender);
}

.summary-card.honest i {
    background: linear-gradient(135deg, #00b894, #55a3ff);
}

.summary-card.dishonest i {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.summary-card.total-score i {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.summary-info {
    display: flex;
    flex-direction: column;
}

.summary-count {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
    color: var(--dark-color);
}

.summary-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 理念說明區域 */
.philosophy-guide {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-medium);
    border: 3px solid #fd79a8;
}

.philosophy-card {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(253, 121, 168, 0.1), rgba(255, 107, 107, 0.1));
    border-radius: var(--border-radius);
}

.philosophy-icon {
    font-size: 3rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #fd79a8, #ff6b6b);
    border-radius: 50%;
    color: white;
    box-shadow: var(--shadow-medium);
}

.philosophy-content h3 {
    margin: 0 0 10px 0;
    font-size: 1.5rem;
    color: #e17055;
    font-weight: 700;
}

.philosophy-content p {
    margin: 0;
    color: #666;
    font-size: 1rem;
    line-height: 1.4;
}

.philosophy-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.action-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
}

.action-card.honest {
    border: 3px solid #00b894;
}

.action-card.honest:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    background: linear-gradient(135deg, rgba(0, 184, 148, 0.1), rgba(85, 163, 255, 0.1));
}

.action-card.dishonest {
    border: 3px solid #ff6b6b;
}

.action-card.dishonest:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 82, 0.1));
}

.action-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
}

.action-card.honest .action-icon {
    background: linear-gradient(135deg, #00b894, #55a3ff);
}

.action-card.dishonest .action-icon {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.action-text {
    flex: 1;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
}

.action-score {
    font-weight: 700;
    font-size: 1rem;
    padding: 5px 12px;
    border-radius: var(--border-radius);
    color: white;
}

.action-card.honest .action-score {
    background: linear-gradient(135deg, #00b894, #55a3ff);
}

.action-card.dishonest .action-score {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

/* 操作按鈕區域 */
.action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 25px 0;
    flex-wrap: wrap;
    gap: 15px;
}

.batch-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.batch-btn {
    padding: 12px 20px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--border-radius-xl);
    transition: var(--transition);
    border: 3px solid;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: var(--shadow-soft);
}

.batch-btn.honest {
    background: linear-gradient(135deg, #00b894, #55a3ff);
    color: white;
    border-color: #00b894;
}

.batch-btn.honest:hover {
    background: linear-gradient(135deg, #00a085, #4285f4);
    border-color: #00a085;
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.batch-btn.reset {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border-color: #ff6b6b;
}

.batch-btn.reset:hover {
    background: linear-gradient(135deg, #ee5a52, #d63031);
    border-color: #ee5a52;
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.search-box {
    position: relative;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: 2px solid var(--candy-cream);
    border-radius: var(--border-radius-xl);
    font-size: 0.95rem;
    transition: var(--transition);
    background: var(--card-background);
}

.search-box input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-glow);
}

/* 表格樣式 */
.table-container {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    margin-bottom: 25px;
    border: 3px solid var(--candy-peach);
}

#honestyTable {
    width: 100%;
    border-collapse: collapse;
}

#honestyTable th,
#honestyTable td {
    padding: 15px;
    text-align: left;
    border-bottom: 2px solid var(--candy-cream);
    transition: var(--transition-fast);
}

#honestyTable th {
    background: var(--gradient-peach);
    font-weight: 700;
    color: var(--dark-color);
    white-space: nowrap;
    font-size: 0.95rem;
}

#honestyTable tbody tr:hover {
    background: var(--gradient-lemon);
    transform: scale(1.01);
}

/* 誠實記錄按鈕樣式 */
.honesty-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 2px solid;
    color: white;
    margin-right: 5px;
}

.honesty-btn.honest {
    background: linear-gradient(135deg, #00b894, #55a3ff);
    border-color: #00b894;
}

.honesty-btn.honest:hover {
    background: linear-gradient(135deg, #00a085, #4285f4);
    border-color: #00a085;
    transform: scale(1.05);
}

.honesty-btn.dishonest {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-color: #ff6b6b;
}

.honesty-btn.dishonest:hover {
    background: linear-gradient(135deg, #ee5a52, #d63031);
    border-color: #ee5a52;
    transform: scale(1.05);
}

/* 記錄彈窗樣式 */
.modal-content {
    max-width: 700px;
    width: 90%;
}

.student-name {
    font-weight: 700;
    color: var(--primary-color);
    background: rgba(135, 206, 235, 0.1);
    padding: 4px 8px;
    border-radius: var(--border-radius);
}

.record-date {
    font-weight: 600;
    color: #e17055;
    background: rgba(225, 112, 85, 0.1);
    padding: 4px 8px;
    border-radius: var(--border-radius);
}

.honesty-selection h4 {
    margin: 20px 0 15px 0;
    color: var(--dark-color);
    text-align: center;
}

.honesty-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.honesty-option {
    background: var(--card-background);
    border: 3px solid;
    border-radius: var(--border-radius);
    padding: 20px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.honesty-option.honest {
    border-color: #00b894;
}

.honesty-option.dishonest {
    border-color: #ff6b6b;
}

.honesty-option:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.honesty-option.selected.honest {
    background: linear-gradient(135deg, rgba(0, 184, 148, 0.1), rgba(85, 163, 255, 0.1));
    border-color: #00a085;
    transform: scale(1.08);
}

.honesty-option.selected.dishonest {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 82, 0.1));
    border-color: #ee5a52;
    transform: scale(1.08);
}

.option-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.option-text {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.option-score {
    font-weight: 700;
    font-size: 1rem;
    padding: 5px 12px;
    border-radius: var(--border-radius);
    color: white;
    display: inline-block;
    margin-bottom: 8px;
}

.honesty-option.honest .option-score {
    background: linear-gradient(135deg, #00b894, #55a3ff);
}

.honesty-option.dishonest .option-score {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.option-description {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.3;
}

.note-section {
    margin-top: 20px;
}

.note-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.note-section textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid var(--candy-cream);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
}

.note-section textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-glow);
}

/* 編輯選項樣式 */
.edit-honesty-option {
    background: var(--card-background);
    border: 3px solid;
    border-radius: var(--border-radius);
    padding: 20px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.edit-honesty-option.honest {
    border-color: #00b894;
}

.edit-honesty-option.dishonest {
    border-color: #ff6b6b;
}

.edit-honesty-option:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.edit-honesty-option.selected.honest {
    background: linear-gradient(135deg, rgba(0, 184, 148, 0.1), rgba(85, 163, 255, 0.1));
    border-color: #00a085;
    transform: scale(1.08);
}

.edit-honesty-option.selected.dishonest {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(238, 90, 82, 0.1));
    border-color: #ee5a52;
    transform: scale(1.08);
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
}

.pagination button {
    width: 40px;
    height: 40px;
    border: 2px solid var(--candy-cream);
    background: var(--card-background);
    border-radius: var(--border-radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.pagination button:hover:not(:disabled) {
    background: var(--gradient-lavender);
    border-color: var(--candy-lavender-deep);
    transform: scale(1.1);
}

.pagination button.active {
    background: var(--gradient-pink);
    color: white;
    border-color: var(--candy-pink-deep);
    transform: scale(1.1);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 992px) {
    .honesty-summary {
        grid-template-columns: 1fr 1fr;
    }
    
    .philosophy-actions {
        grid-template-columns: 1fr;
    }
    
    .honesty-options {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-display {
        width: 100%;
        justify-content: center;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .batch-buttons {
        justify-content: center;
        margin-bottom: 15px;
    }
    
    .search-box {
        width: 100%;
    }
    
    .honesty-summary {
        grid-template-columns: 1fr;
    }
    
    .philosophy-card {
        flex-direction: column;
        text-align: center;
    }
    
    .philosophy-actions {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px auto;
    }
    
    #honestyTable th,
    #honestyTable td {
        padding: 10px;
        font-size: 0.9rem;
    }
}