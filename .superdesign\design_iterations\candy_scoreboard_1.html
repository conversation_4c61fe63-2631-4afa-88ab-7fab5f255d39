<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 記分小達人 - 糖果樂園</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- 引入主題 CSS -->
    <link rel="stylesheet" href="candy_theme.css">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    
    <style>
        /* 額外的組件樣式 */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--background);
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: float-in 1s ease-out;
        }
        
        .header h1 {
            font-size: 3rem;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .candy-emoji {
            font-size: 3rem;
            animation: wiggle-cute 2s ease-in-out infinite alternate;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .function-card {
            background: var(--card-background);
            border-radius: var(--radius-large);
            padding: 30px;
            text-align: center;
            box-shadow: var(--shadow-soft);
            transition: all var(--transition-medium);
            border: 3px solid transparent;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            animation: bubble-pop 0.8s ease-out;
        }
        
        .function-card:nth-child(1) { animation-delay: 0.1s; }
        .function-card:nth-child(2) { animation-delay: 0.2s; }
        .function-card:nth-child(3) { animation-delay: 0.3s; }
        .function-card:nth-child(4) { animation-delay: 0.4s; }
        .function-card:nth-child(5) { animation-delay: 0.5s; }
        .function-card:nth-child(6) { animation-delay: 0.6s; }
        
        .function-card.daily-checkin {
            border-color: var(--candy-pink);
            background: linear-gradient(135deg, var(--card-background) 0%, rgba(255, 182, 193, 0.1) 100%);
        }
        
        .function-card.tooth-brushing {
            border-color: var(--candy-mint);
            background: linear-gradient(135deg, var(--card-background) 0%, rgba(152, 251, 152, 0.1) 100%);
        }
        
        .function-card.lucky-draw {
            border-color: var(--candy-sky);
            background: linear-gradient(135deg, var(--card-background) 0%, rgba(135, 206, 235, 0.1) 100%);
        }
        
        .function-card.leaderboard {
            border-color: var(--candy-lemon-deep);
            background: linear-gradient(135deg, var(--card-background) 0%, rgba(255, 215, 0, 0.1) 100%);
        }
        
        .function-card.mystery-box {
            border-color: var(--candy-lavender-deep);
            background: linear-gradient(135deg, var(--card-background) 0%, rgba(147, 112, 219, 0.1) 100%);
        }
        
        .function-card.manual-score {
            border-color: var(--candy-peach-deep);
            background: linear-gradient(135deg, var(--card-background) 0%, rgba(255, 127, 80, 0.1) 100%);
        }
        
        .function-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-glow);
        }
        
        .function-card:active {
            transform: translateY(-3px) scale(0.98);
        }
        
        .function-icon {
            font-size: 4rem;
            margin-bottom: 15px;
            display: block;
            animation: bounce-gentle 3s ease-in-out infinite;
        }
        
        .function-title {
            font-size: 1.5rem;
            font-weight: var(--font-weight-bold);
            margin-bottom: 10px;
            color: var(--text-primary);
        }
        
        .function-desc {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .event-section {
            margin-bottom: 40px;
            animation: float-in 1.2s ease-out 0.5s both;
        }
        
        .event-card {
            background: var(--gradient-lemon);
            border-radius: var(--radius-large);
            padding: 30px;
            border: 4px dashed var(--candy-lemon-deep);
            text-align: center;
            box-shadow: var(--shadow-medium);
            animation: gentle-glow 3s ease-in-out infinite alternate;
            position: relative;
            overflow: hidden;
        }
        
        .event-title {
            font-size: 1.8rem;
            font-weight: var(--font-weight-bold);
            color: var(--candy-lemon-deep);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .event-content {
            font-size: 1.1rem;
            color: var(--text-primary);
            margin-bottom: 20px;
        }
        
        .event-multiplier {
            background: var(--candy-lemon-deep);
            color: white;
            padding: 8px 16px;
            border-radius: var(--radius-medium);
            font-weight: var(--font-weight-bold);
            display: inline-block;
            animation: scale-breathe 2s ease-in-out infinite;
        }
        
        .students-section {
            margin-bottom: 40px;
            animation: float-in 1.4s ease-out 0.7s both;
        }
        
        .section-title {
            text-align: center;
            font-size: 2rem;
            font-weight: var(--font-weight-bold);
            color: var(--candy-mint-deep);
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .students-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .student-card {
            background: var(--card-background);
            border-radius: var(--radius-large);
            padding: 20px;
            text-align: center;
            box-shadow: var(--shadow-soft);
            transition: all var(--transition-medium);
            border: 2px solid transparent;
            cursor: pointer;
        }
        
        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
            border-color: var(--candy-pink);
        }
        
        .student-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--gradient-lavender);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin: 0 auto 15px;
            box-shadow: var(--shadow-soft);
            transition: all var(--transition-medium);
            border: 4px solid white;
        }
        
        .student-card:hover .student-avatar {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-glow);
        }
        
        .student-name {
            font-size: 1.2rem;
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: 8px;
        }
        
        .student-score {
            font-size: 1.5rem;
            font-weight: var(--font-weight-bold);
            background: var(--gradient-pink);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-section {
            animation: float-in 1.6s ease-out 0.9s both;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }
        
        .stat-card {
            background: var(--card-background);
            border-radius: var(--radius-large);
            padding: 25px;
            text-align: center;
            box-shadow: var(--shadow-soft);
            transition: all var(--transition-medium);
            border: 3px solid;
        }
        
        .stat-card.checkin-stat {
            border-color: var(--candy-pink);
        }
        
        .stat-card.brush-stat {
            border-color: var(--candy-mint);
        }
        
        .stat-card.performance-stat {
            border-color: var(--candy-sky);
        }
        
        .stat-card.average-stat {
            border-color: var(--candy-lavender);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: var(--font-weight-bold);
            margin-bottom: 10px;
            background: var(--gradient-pink);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 1rem;
            margin-bottom: 15px;
        }
        
        .progress-bar-container {
            background: var(--soft-gray);
            border-radius: var(--radius-xl);
            height: 12px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            border-radius: var(--radius-xl);
            transition: width var(--transition-slow);
            position: relative;
        }
        
        .progress-bar.pink {
            background: var(--gradient-pink);
        }
        
        .progress-bar.mint {
            background: var(--gradient-mint);
        }
        
        .progress-bar.sky {
            background: linear-gradient(135deg, var(--candy-sky) 0%, var(--candy-lavender) 100%);
        }
        
        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }
        
        .floating-bubbles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .bubble {
            position: absolute;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.3) 0%, rgba(255, 182, 193, 0.1) 70%, transparent 100%);
            border-radius: 50%;
            animation: float-up linear infinite;
        }
        
        /* 動畫定義 */
        @keyframes float-in {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        @keyframes bubble-pop {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            70% {
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes wiggle-cute {
            0%, 100% { transform: rotate(-3deg); }
            50% { transform: rotate(3deg); }
        }
        
        @keyframes bounce-gentle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-8px); }
        }
        
        @keyframes gentle-glow {
            0% { box-shadow: var(--shadow-medium); }
            100% { box-shadow: var(--shadow-glow); }
        }
        
        @keyframes scale-breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        @keyframes float-up {
            from {
                transform: translateY(100vh) translateX(0);
                opacity: 0.3;
            }
            10% {
                opacity: 0.6;
            }
            90% {
                opacity: 0.6;
            }
            to {
                transform: translateY(-20vh) translateX(50px);
                opacity: 0;
            }
        }
        
        /* 響應式設計 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .function-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .students-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .student-avatar {
                width: 60px;
                height: 60px;
                font-size: 2.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 浮動氣泡背景 -->
    <div class="floating-bubbles" id="bubbles"></div>
    
    <div class="main-container">
        <!-- 頁面標題 -->
        <header class="header">
            <h1>
                <span class="candy-emoji">🍭</span>
                記分小達人
                <span class="candy-emoji">🌟</span>
            </h1>
        </header>
        
        <!-- 功能卡片區域 -->
        <section class="function-grid">
            <div class="function-card daily-checkin" onclick="navigateToPage('daily-checkin')">
                <span class="function-icon">📅</span>
                <h3 class="function-title">每日簽到</h3>
                <p class="function-desc">作業繳交確認，記錄學習足跡</p>
            </div>
            
            <div class="function-card tooth-brushing" onclick="navigateToPage('tooth-brushing')">
                <span class="function-icon">🦷</span>
                <h3 class="function-title">刷牙登記</h3>
                <p class="function-desc">健康習慣養成，潔白笑容加分</p>
            </div>
            
            <div class="function-card lucky-draw" onclick="navigateToPage('lucky-draw')">
                <span class="function-icon">🎲</span>
                <h3 class="function-title">幸運轉盤</h3>
                <p class="function-desc">驚喜大獎等你來，運氣爆棚</p>
            </div>
            
            <div class="function-card leaderboard" onclick="navigateToPage('leaderboard')">
                <span class="function-icon">🏆</span>
                <h3 class="function-title">榮譽榜</h3>
                <p class="function-desc">學習之星閃閃發光</p>
            </div>
            
            <div class="function-card mystery-box" onclick="navigateToPage('mystery-box')">
                <span class="function-icon">✨</span>
                <h3 class="function-title">神祕寶箱</h3>
                <p class="function-desc">解鎖特殊獎勵與成就</p>
            </div>
            
            <div class="function-card manual-score" onclick="navigateToPage('manual-score')">
                <span class="function-icon">✏️</span>
                <h3 class="function-title">手動加減分</h3>
                <p class="function-desc">彈性調整，公平公正</p>
            </div>
        </section>
        
        <!-- 今日隨機事件 -->
        <section class="event-section">
            <div class="event-card">
                <h3 class="event-title">
                    <span>✨</span>
                    今日隨機事件
                    <span>✨</span>
                </h3>
                <p class="event-content">
                    🎉 恭喜！今天是雙倍積分日！<br>
                    所有活動獲得的分數都會
                </p>
                <span class="event-multiplier">× 2</span>
            </div>
        </section>
        
        <!-- 學生小動物園 -->
        <section class="students-section">
            <h2 class="section-title">
                <span>🐾</span>
                學生小動物園
                <span>🐾</span>
            </h2>
            <div class="students-grid">
                <div class="student-card">
                    <div class="student-avatar">🐱</div>
                    <div class="student-name">小明</div>
                    <div class="student-score">85分</div>
                </div>
                <div class="student-card">
                    <div class="student-avatar">🐶</div>
                    <div class="student-name">小華</div>
                    <div class="student-score">92分</div>
                </div>
                <div class="student-card">
                    <div class="student-avatar">🐰</div>
                    <div class="student-name">小美</div>
                    <div class="student-score">78分</div>
                </div>
                <div class="student-card">
                    <div class="student-avatar">🐻</div>
                    <div class="student-name">小強</div>
                    <div class="student-score">88分</div>
                </div>
                <div class="student-card">
                    <div class="student-avatar">🦊</div>
                    <div class="student-name">小芳</div>
                    <div class="student-score">91分</div>
                </div>
                <div class="student-card">
                    <div class="student-avatar">🐸</div>
                    <div class="student-name">小剛</div>
                    <div class="student-score">76分</div>
                </div>
                <div class="student-card">
                    <div class="student-avatar">🐼</div>
                    <div class="student-name">小莉</div>
                    <div class="student-score">89分</div>
                </div>
                <div class="student-card">
                    <div class="student-avatar">🐨</div>
                    <div class="student-name">小傑</div>
                    <div class="student-score">82分</div>
                </div>
            </div>
        </section>
        
        <!-- 統計儀表板 -->
        <section class="stats-section">
            <h2 class="section-title">
                <span>📊</span>
                統計儀表板
                <span>📊</span>
            </h2>
            <div class="stats-grid">
                <div class="stat-card checkin-stat">
                    <span class="stat-icon">📅</span>
                    <div class="stat-value">60%</div>
                    <div class="stat-label">今日簽到率</div>
                    <div class="progress-bar-container">
                        <div class="progress-bar pink" style="width: 60%"></div>
                    </div>
                </div>
                
                <div class="stat-card brush-stat">
                    <span class="stat-icon">🦷</span>
                    <div class="stat-value">80%</div>
                    <div class="stat-label">本週刷牙率</div>
                    <div class="progress-bar-container">
                        <div class="progress-bar mint" style="width: 80%"></div>
                    </div>
                </div>
                
                <div class="stat-card performance-stat">
                    <span class="stat-icon">📈</span>
                    <div class="stat-value">70%</div>
                    <div class="stat-label">本月表現</div>
                    <div class="progress-bar-container">
                        <div class="progress-bar sky" style="width: 70%"></div>
                    </div>
                </div>
                
                <div class="stat-card average-stat">
                    <span class="stat-icon">⭐</span>
                    <div class="stat-value">85分</div>
                    <div class="stat-label">平均分數</div>
                    <div style="color: var(--candy-lemon-deep); font-size: 1.5rem; margin-top: 10px;">
                        ⭐⭐⭐⭐⭐
                    </div>
                </div>
            </div>
        </section>
    </div>
    
    <script>
        // 初始化浮動氣泡
        function createBubbles() {
            const bubblesContainer = document.getElementById('bubbles');
            const colors = ['#FFB6C1', '#98FB98', '#87CEEB', '#FFFFE0', '#E6E6FA', '#FFDAB9'];
            
            for (let i = 0; i < 8; i++) {
                const bubble = document.createElement('div');
                bubble.className = 'bubble';
                bubble.style.left = Math.random() * 100 + '%';
                bubble.style.width = bubble.style.height = Math.random() * 60 + 20 + 'px';
                bubble.style.background = `radial-gradient(circle, ${colors[Math.floor(Math.random() * colors.length)]}40 0%, ${colors[Math.floor(Math.random() * colors.length)]}20 70%, transparent 100%)`;
                bubble.style.animationDuration = (Math.random() * 15 + 10) + 's';
                bubble.style.animationDelay = Math.random() * 5 + 's';
                bubblesContainer.appendChild(bubble);
            }
        }
        
        // 頁面導航函數
        function navigateToPage(page) {
            // 添加點擊動畫效果
            event.target.closest('.function-card').style.transform = 'scale(0.95)';
            
            setTimeout(() => {
                // 這裡可以添加實際的頁面跳轉邏輯
                alert(`正在前往 ${page} 頁面...`);
                event.target.closest('.function-card').style.transform = '';
            }, 150);
        }
        
        // 添加粒子效果
        function addSparkleEffect(element) {
            const sparkle = document.createElement('div');
            sparkle.style.position = 'absolute';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.fontSize = '20px';
            sparkle.innerHTML = '✨';
            sparkle.style.left = Math.random() * element.offsetWidth + 'px';
            sparkle.style.top = Math.random() * element.offsetHeight + 'px';
            sparkle.style.animation = 'sparkle-fade 1s ease-out forwards';
            element.appendChild(sparkle);
            
            setTimeout(() => {
                sparkle.remove();
            }, 1000);
        }
        
        // 為學生卡片添加互動效果
        document.querySelectorAll('.student-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                addSparkleEffect(card);
            });
        });
        
        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', () => {
            createBubbles();
            
            // 添加進度條動畫
            setTimeout(() => {
                document.querySelectorAll('.progress-bar').forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 1000);
        });
        
        // 添加額外的 CSS 動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sparkle-fade {
                0% {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
                100% {
                    opacity: 0;
                    transform: translateY(-30px) scale(0.5);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>