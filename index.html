<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 學生名單</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li class="active"><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li><a href="homework-submission.html"><i class="fas fa-book"></i> 📚 作業繳交</a></li>
                    <li><a href="homework-history.html"><i class="fas fa-clipboard-list"></i> 📋 繳交歷史</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>
            <div class="data-management">
                <h3>📊 資料管理</h3>
                <button id="exportBtn" class="btn btn-secondary"><i class="fas fa-file-export"></i> 匯出 CSV</button>
                <button id="importBtn" class="btn btn-secondary"><i class="fas fa-file-import"></i> 匯入資料</button>
                <input type="file" id="importFile" accept=".json,.csv" style="display: none;">
            </div>
        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <!-- 浮動裝飾元素 -->
            <div class="floating-decorations">
                <div class="floating-decoration">🐾</div>
                <div class="floating-decoration">🦊</div>
                <div class="floating-decoration">🐻</div>
            </div>
            
            <h2><i class="fas fa-users"></i> 🐾 學生動物園名單 
                <span class="title-decoration">
                    <img src="images/animals/bear.png" alt="小熊" class="title-animal-icon">
                    <img src="images/animals/rabbit.png" alt="小兔" class="title-animal-icon">
                </span>
            </h2>
            
            <div class="action-buttons">
                <button id="addStudentBtn" class="btn btn-primary"><i class="fas fa-plus"></i> 新增學生</button>
                <button id="updateHistoricalNamesBtn" class="btn btn-success"><i class="fas fa-history"></i> 更新歷史人物名字</button>
                <div class="reset-score-section">
                    <input type="text" id="resetCodeInput" placeholder="輸入重置代碼" maxlength="4">
                    <button id="resetScoresBtn" class="btn btn-danger"><i class="fas fa-undo"></i> 重置分數</button>
                </div>
            </div>
            
            <div class="table-container">
                <table id="studentTable">
                    <thead>
                        <tr>
                            <th>🐾 動物</th>
                            <th class="sortable" data-sort="number">
                                座號
                                <i class="fas fa-sort sort-icon" id="numberSortIcon"></i>
                            </th>
                            <th class="sortable" data-sort="class">
                                班級
                                <i class="fas fa-sort sort-icon" id="classSortIcon"></i>
                            </th>
                            <th class="sortable" data-sort="name">
                                姓名
                                <i class="fas fa-sort sort-icon" id="nameSortIcon"></i>
                            </th>
                            <th class="sortable" data-sort="score">
                                🌟 分數
                                <i class="fas fa-sort sort-icon" id="scoreSortIcon"></i>
                            </th>
                            <th>🎮 操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 學生資料將由 JavaScript 動態生成 -->
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <!-- 新增/編輯學生彈窗 -->
    <div id="studentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3 id="modalTitle">🐾 新增小動物學生</h3>
            <form id="studentForm">
                <input type="hidden" id="studentId">
                <div class="form-group">
                    <label for="studentAnimal">🐾 選擇動物頭像：</label>
                    <div class="animal-selection">
                        <p style="font-size: 0.9em; color: var(--dark-color); margin-bottom: 8px; text-align: center;">
                            💫 點擊選擇你最喜歡的小動物夥伴！
                        </p>
                        <div class="animal-grid">
                            <!-- 哺乳動物 -->
                            <div class="animal-option" data-animal="🐱">🐱</div>
                            <div class="animal-option" data-animal="🐶">🐶</div>
                            <div class="animal-option" data-animal="🐰">🐰</div>
                            <div class="animal-option" data-animal="🐻">🐻</div>
                            <div class="animal-option" data-animal="🦊">🦊</div>
                            <div class="animal-option" data-animal="🐼">🐼</div>
                            <div class="animal-option" data-animal="🐨">🐨</div>
                            <div class="animal-option" data-animal="🦁">🦁</div>
                            <div class="animal-option" data-animal="🐯">🐯</div>
                            <div class="animal-option" data-animal="🐵">🐵</div>
                            <div class="animal-option" data-animal="🐺">🐺</div>
                            <div class="animal-option" data-animal="🐹">🐹</div>
                            <!-- 小動物 -->
                            <div class="animal-option" data-animal="🐭">🐭</div>
                            <div class="animal-option" data-animal="🐷">🐷</div>
                            <div class="animal-option" data-animal="🐮">🐮</div>
                            <div class="animal-option" data-animal="🦝">🦝</div>
                            <div class="animal-option" data-animal="🦡">🦡</div>
                            <div class="animal-option" data-animal="🐿️">🐿️</div>
                            <div class="animal-option" data-animal="🦔">🦔</div>
                            <div class="animal-option" data-animal="🦘">🦘</div>
                            <div class="animal-option" data-animal="🐴">🐴</div>
                            <!-- 鳥類 -->
                            <div class="animal-option" data-animal="🐧">🐧</div>
                            <div class="animal-option" data-animal="🦆">🦆</div>
                            <div class="animal-option" data-animal="🦉">🦉</div>
                            <div class="animal-option" data-animal="🐣">🐣</div>
                            <div class="animal-option" data-animal="🐤">🐤</div>
                            <div class="animal-option" data-animal="🐦">🐦</div>
                            <div class="animal-option" data-animal="🦅">🦅</div>
                            <!-- 海洋動物 -->
                            <div class="animal-option" data-animal="🐸">🐸</div>
                            <div class="animal-option" data-animal="🐙">🐙</div>
                            <div class="animal-option" data-animal="🐢">🐢</div>
                            <div class="animal-option" data-animal="🐳">🐳</div>
                            <div class="animal-option" data-animal="🐬">🐬</div>
                            <div class="animal-option" data-animal="🦈">🦈</div>
                            <!-- 昆蟲與特殊動物 -->
                            <div class="animal-option" data-animal="🐝">🐝</div>
                            <div class="animal-option" data-animal="🦋">🦋</div>
                            <div class="animal-option" data-animal="🐞">🐞</div>
                            <div class="animal-option" data-animal="🕷️">🕷️</div>
                            <div class="animal-option" data-animal="🦄">🦄</div>
                            <div class="animal-option" data-animal="🐉">🐉</div>
                        </div>
                        <input type="hidden" id="selectedAnimal" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="studentNumber">📝 編號：</label>
                    <input type="number" id="studentNumber" required min="1" max="99">
                </div>
                <div class="form-group">
                    <label for="studentClass">🏫 班級：</label>
                    <input type="text" id="studentClass" value="603" required>
                </div>
                <div class="form-group">
                    <label for="studentName">✨ 姓名：</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="studentName" required style="flex: 1;">
                        <button type="button" id="randomNameBtn" class="btn btn-secondary" style="
                            padding: 8px 12px;
                            font-size: 0.9rem;
                            white-space: nowrap;
                        " title="隨機歷史人物名字">
                            <i class="fas fa-random"></i> 隨機
                        </button>
                    </div>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn btn-primary">💾 儲存</button>
                    <button type="button" class="btn btn-secondary" id="cancelBtn">❌ 取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 匯出格式選擇彈窗 -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <span class="close" id="exportModalClose">&times;</span>
            <h3>📊 選擇匯出格式</h3>
            <div class="export-options">
                <div class="export-option" data-format="json">
                    <div class="export-icon">📄</div>
                    <h4>JSON 格式</h4>
                    <p>完整資料結構，適合程式處理</p>
                    <button class="btn btn-primary">選擇 JSON</button>
                </div>
                <div class="export-option" data-format="csv">
                    <div class="export-icon">📊</div>
                    <h4>CSV 格式</h4>
                    <p>表格格式，適合 Excel 開啟</p>
                    <button class="btn btn-primary">選擇 CSV</button>
                </div>
                <div class="export-option" data-format="excel">
                    <div class="export-icon">📈</div>
                    <h4>Excel 格式</h4>
                    <p>完整 Excel 檔案，包含格式設定</p>
                    <button class="btn btn-primary">選擇 Excel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="js/animal-config.js"></script>
    <script src="js/script.js"></script>
</body>
</html>
