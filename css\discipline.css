/* 秩序禮儀頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.date-display {
    background: var(--card-background);
    padding: 10px 20px;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-soft);
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
    border: 2px solid var(--candy-lemon-deep);
}

/* 違規摘要卡片 */
.discipline-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    padding: 20px;
    box-shadow: var(--shadow-soft);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    border: 3px solid transparent;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.summary-card i {
    font-size: 2.5rem;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.summary-card i.fa-users {
    background: var(--gradient-lavender);
}

.summary-card i.fa-exclamation-triangle.violation {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.summary-card i.fa-calendar-times.total {
    background: linear-gradient(135deg, #ff9ff3, #f368e0);
}

.summary-card i.fa-minus-circle.penalty {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
}

.summary-info {
    display: flex;
    flex-direction: column;
}

.summary-count {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
    color: var(--dark-color);
}

.summary-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 生活常規規則說明 */
.rules-guide {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-medium);
    border: 3px solid #ff6b6b;
}

.rules-guide h3 {
    margin: 0 0 20px 0;
    color: #e17055;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
    text-align: center;
    justify-content: center;
}

.rules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.rule-card {
    background: var(--card-background);
    border: 2px solid #ff7675;
    border-radius: var(--border-radius);
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
    cursor: pointer;
}

.rule-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: #ff6b6b;
}

.rule-card.selected {
    border-color: #e17055;
    background: linear-gradient(135deg, rgba(225, 112, 85, 0.1), rgba(255, 107, 107, 0.1));
    transform: scale(1.02);
}

.rule-number {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    font-weight: 700;
    font-size: 1rem;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.rule-text {
    flex: 1;
    font-size: 0.95rem;
    line-height: 1.4;
    color: var(--dark-color);
}

.rule-penalty {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
    color: white;
    font-weight: 600;
    font-size: 0.85rem;
    padding: 4px 10px;
    border-radius: var(--border-radius);
    flex-shrink: 0;
}

/* 操作按鈕區域 */
.action-buttons {
    display: flex;
    justify-content: space-between;
    margin: 25px 0;
    flex-wrap: wrap;
    gap: 15px;
}

.btn-info {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border: 2px solid #74b9ff;
}

.btn-info:hover {
    background: linear-gradient(135deg, #0984e3, #0056b3);
    border-color: #0984e3;
}

.btn-warning {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
    color: white;
    border: 2px solid #fdcb6e;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e17055, #d63031);
    border-color: #e17055;
}

.search-box {
    position: relative;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: 2px solid var(--candy-cream);
    border-radius: var(--border-radius-xl);
    font-size: 0.95rem;
    transition: var(--transition);
    background: var(--card-background);
}

.search-box input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-glow);
}

/* 表格樣式 */
.table-container {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    margin-bottom: 25px;
    border: 3px solid var(--candy-peach);
}

#disciplineTable {
    width: 100%;
    border-collapse: collapse;
}

#disciplineTable th,
#disciplineTable td {
    padding: 15px;
    text-align: left;
    border-bottom: 2px solid var(--candy-cream);
    transition: var(--transition-fast);
}

#disciplineTable th {
    background: var(--gradient-peach);
    font-weight: 700;
    color: var(--dark-color);
    white-space: nowrap;
    font-size: 0.95rem;
}

#disciplineTable tbody tr:hover {
    background: var(--gradient-lemon);
    transform: scale(1.01);
}

/* 違規按鈕樣式 */
.violation-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: 2px solid #ff6b6b;
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.violation-btn:hover {
    background: linear-gradient(135deg, #ee5a52, #d63031);
    border-color: #ee5a52;
    transform: scale(1.05);
}

.violation-btn:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #d63031, #b71c1c);
}

/* 點擊時的視覺反饋 */
.violation-btn.clicked {
    animation: button-click 0.3s ease;
}

@keyframes button-click {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); background: linear-gradient(135deg, #d63031, #b71c1c); }
    100% { transform: scale(1); }
}

/* 違規登記彈窗 */
.modal-content.large {
    max-width: 900px;
    width: 90%;
}

.modal-content.extra-large {
    max-width: 1200px;
    width: 95%;
    height: 90vh;
    display: flex;
    flex-direction: column;
}

.student-name {
    font-weight: 700;
    color: var(--primary-color);
    background: rgba(135, 206, 235, 0.1);
    padding: 4px 8px;
    border-radius: var(--border-radius);
}

.violation-selection h4 {
    margin: 20px 0 15px 0;
    color: var(--dark-color);
}

.violation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    border: 2px solid var(--candy-cream);
    border-radius: var(--border-radius);
}

.violation-option {
    background: var(--card-background);
    border: 2px solid #ff7675;
    border-radius: var(--border-radius);
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.violation-option:hover {
    border-color: #ff6b6b;
    transform: scale(1.02);
}

.violation-option.selected {
    border-color: #e17055;
    background: linear-gradient(135deg, rgba(225, 112, 85, 0.1), rgba(255, 107, 107, 0.1));
    transform: scale(1.05);
}

.violation-number {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    font-weight: 700;
    font-size: 0.9rem;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.violation-text {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.3;
    color: var(--dark-color);
}

.custom-note {
    margin-top: 15px;
}

.custom-note label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.custom-note textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid var(--candy-cream);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
}

.custom-note textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-glow);
}

.penalty-info {
    background: rgba(255, 107, 107, 0.1);
    border: 2px solid #ff6b6b;
    border-radius: var(--border-radius);
    padding: 15px;
    margin: 20px 0;
    text-align: center;
}

.penalty-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1.1rem;
    color: #e17055;
}

.penalty-display i {
    font-size: 1.3rem;
}

/* 歷史記錄樣式 */
.history-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(135, 206, 235, 0.1);
    border-radius: var(--border-radius);
    border: 2px solid var(--candy-sky);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 8px;
    border: 2px solid var(--candy-cream);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.filter-group input:focus,
.filter-group select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.filter-actions {
    grid-column: 1 / -1;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.history-stats {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 107, 107, 0.1);
    border-radius: var(--border-radius);
    border: 2px solid #ff6b6b;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-label {
    font-weight: 600;
    color: var(--dark-color);
}

.stat-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: #e17055;
}

.history-table-container {
    flex: 1;
    overflow: auto;
    border: 2px solid var(--candy-cream);
    border-radius: var(--border-radius);
}

#historyTable {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

#historyTable th,
#historyTable td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--candy-cream);
    font-size: 0.9rem;
}

#historyTable th {
    background: var(--gradient-peach);
    font-weight: 700;
    color: var(--dark-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

#historyTable tbody tr:hover {
    background: var(--gradient-lemon);
}

.delete-record-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: 2px solid #ff6b6b;
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.delete-record-btn:hover {
    background: linear-gradient(135deg, #ee5a52, #d63031);
    border-color: #ee5a52;
}

.history-pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.history-pagination button {
    width: 35px;
    height: 35px;
    border: 2px solid var(--candy-cream);
    background: var(--card-background);
    border-radius: var(--border-radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    font-size: 0.9rem;
}

.history-pagination button:hover:not(:disabled) {
    background: var(--gradient-lavender);
    border-color: var(--candy-lavender-deep);
    transform: scale(1.1);
}

.history-pagination button.active {
    background: var(--gradient-pink);
    color: white;
    border-color: var(--candy-pink-deep);
    transform: scale(1.1);
}

.history-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
}

.pagination button {
    width: 40px;
    height: 40px;
    border: 2px solid var(--candy-cream);
    background: var(--card-background);
    border-radius: var(--border-radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.pagination button:hover:not(:disabled) {
    background: var(--gradient-lavender);
    border-color: var(--candy-lavender-deep);
    transform: scale(1.1);
}

.pagination button.active {
    background: var(--gradient-pink);
    color: white;
    border-color: var(--candy-pink-deep);
    transform: scale(1.1);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 992px) {
    .discipline-summary {
        grid-template-columns: 1fr 1fr;
    }
    
    .rules-grid {
        grid-template-columns: 1fr;
    }
    
    .violation-grid {
        grid-template-columns: 1fr;
    }
    
    .history-filters {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-display {
        width: 100%;
        justify-content: center;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .search-box {
        width: 100%;
    }
    
    .discipline-summary {
        grid-template-columns: 1fr;
    }
    
    .history-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .modal-content.large,
    .modal-content.extra-large {
        width: 95%;
        margin: 20px auto;
    }
    
    #disciplineTable th,
    #disciplineTable td {
        padding: 10px;
        font-size: 0.9rem;
    }
}

/* 匯出格式選擇彈窗樣式 */
.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.export-option {
    background: var(--card-background);
    border: 3px solid var(--candy-lemon-light);
    border-radius: var(--border-radius-large);
    padding: 25px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.export-option:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: var(--candy-lemon-deep);
}

.export-option:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.export-option:hover:before {
    left: 100%;
}

.export-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.export-option h4 {
    color: var(--primary-color);
    margin: 0 0 10px 0;
    font-size: 1.2rem;
    font-weight: 700;
}

.export-option p {
    color: var(--text-secondary);
    margin: 0 0 20px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.export-option button {
    background: linear-gradient(135deg, var(--candy-lemon-light), var(--candy-lemon-deep));
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius-medium);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
    font-size: 0.95rem;
}

.export-option button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    background: linear-gradient(135deg, var(--candy-lemon-deep), var(--candy-orange-light));
}

.export-option button:active {
    transform: translateY(0);
}

/* 響應式設計 - 匯出選項 */
@media (max-width: 768px) {
    .export-options {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .export-option {
        padding: 20px;
    }

    .export-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .export-option h4 {
        font-size: 1.1rem;
    }

    .export-option p {
        font-size: 0.85rem;
    }

    .export-option button {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}