/* 違規歷史記錄頁面專用樣式 */

/* 頁面標題區 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.controls-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-end;
}

/* 時段選擇器 */
.period-selector {
    display: flex;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 6px;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    flex-wrap: wrap;
}

.period-btn {
    border: none;
    background: transparent;
    padding: 10px 16px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.8);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 6px;
}

.period-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.period-btn.active {
    background-color: rgba(255, 255, 255, 0.3);
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.period-btn i {
    font-size: 0.85em;
}

/* 時段資訊區域 */
.period-info {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.period-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-card i {
    font-size: 2rem;
    color: #667eea;
    width: 40px;
    text-align: center;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.3rem;
    font-weight: bold;
    color: #667eea;
}

/* 排行榜整合功能 */
.leaderboard-integration {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    padding: 25px;
    margin-bottom: 30px;
    border-left: 5px solid #28a745;
}

.leaderboard-integration h3 {
    margin: 0 0 20px 0;
    color: #28a745;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.integration-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-label {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #333;
    cursor: pointer;
    font-size: 1.1rem;
}

.integration-checkbox {
    width: 20px;
    height: 20px;
    margin: 0;
    cursor: pointer;
}

.control-description {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    margin-left: 32px;
}

.date-range-selector {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    border: 2px dashed #28a745;
}

.range-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.range-group label {
    font-weight: 600;
    color: #333;
}

.range-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.integration-status {
    background: #e9ecef;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #6c757d;
}

.integration-status.active {
    background: #d4edda;
    border-left-color: #28a745;
}

.status-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.status-info i {
    color: #6c757d;
}

.integration-status.active .status-info i {
    color: #28a745;
}

.affected-records {
    display: flex;
    gap: 20px;
    font-size: 0.9rem;
}

.affected-count,
.affected-penalty {
    color: #666;
}

.affected-count strong,
.affected-penalty strong {
    color: #28a745;
}

.header-container h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.date-display {
    font-size: 1rem;
    opacity: 0.9;
}

/* 統計摘要卡片 */
.history-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    border-left: 5px solid;
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.summary-card:nth-child(1) {
    border-left-color: #4facfe;
}

.summary-card:nth-child(2) {
    border-left-color: #ff6b6b;
}

.summary-card:nth-child(3) {
    border-left-color: #4ecdc4;
}

.summary-card:nth-child(4) {
    border-left-color: #feca57;
}

.summary-card i {
    font-size: 2.5rem;
    opacity: 0.8;
}

.summary-card:nth-child(1) i {
    color: #4facfe;
}

.summary-card:nth-child(2) i {
    color: #ff6b6b;
}

.summary-card:nth-child(3) i {
    color: #4ecdc4;
}

.summary-card:nth-child(4) i {
    color: #feca57;
}

.summary-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.summary-count {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
}

.summary-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

/* 篩選器區域 */
.history-filters {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.history-filters h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.form-control {
    padding: 12px;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding-top: 15px;
    border-top: 1px solid #e0e6ed;
}

/* 統計資訊 */
.history-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 10px;
    color: white;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-label {
    font-weight: 500;
    opacity: 0.9;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
}

.stat-unit {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 歷史記錄表格 */
.history-table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.history-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.history-table th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    border: none;
}

.history-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f5f9;
}

.history-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
    transform: scale(1.01);
}

.history-table td {
    padding: 15px 12px;
    vertical-align: middle;
    border: none;
}

.history-table .student-avatar {
    font-size: 1.5rem;
    text-align: center;
}

.penalty-count {
    color: #e74c3c;
    font-weight: 600;
}

.violation-count {
    color: #f39c12;
    font-weight: 600;
}

.violation-count.has-violations {
    background: linear-gradient(135deg, #ff9a56 0%, #ffad56 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 0.85rem;
}

/* 分頁 */
.history-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
}

.history-page-btn {
    background: white;
    border: 2px solid #e0e6ed;
    color: #2c3e50;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.history-page-btn:hover:not(:disabled) {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
}

.history-page-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.history-page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
}

.ellipsis {
    padding: 10px 5px;
    color: #7f8c8d;
}

/* 編輯彈窗樣式 */
.violation-selection {
    margin: 20px 0;
}

.violation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.edit-violation-option {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 2px solid #e0e6ed;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.edit-violation-option:hover {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
    transform: translateY(-2px);
}

.edit-violation-option.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.violation-number {
    background: #667eea;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.edit-violation-option.selected .violation-number {
    background: white;
    color: #667eea;
}

.violation-text {
    flex: 1;
    font-weight: 500;
    line-height: 1.4;
}

.custom-note {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.custom-note label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2c3e50;
}

.custom-note textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    resize: vertical;
    font-family: inherit;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.custom-note textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.penalty-info {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
}

.penalty-display {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.penalty-display i {
    font-size: 1.3rem;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .controls-container {
        width: 100%;
        align-items: stretch;
    }

    .period-selector {
        justify-content: center;
        padding: 4px;
    }

    .period-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .period-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-card i {
        font-size: 1.5rem;
        width: 30px;
    }

    .stat-value {
        font-size: 1.1rem;
    }

    .leaderboard-integration {
        padding: 20px;
    }

    .date-range-selector {
        padding: 15px;
    }

    .range-actions {
        flex-direction: column;
    }

    .range-actions .btn {
        width: 100%;
    }

    .affected-records {
        flex-direction: column;
        gap: 10px;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }

    .filter-actions {
        flex-direction: column;
    }

    .filter-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .history-stats {
        flex-direction: column;
        gap: 15px;
    }

    .history-table-container {
        overflow-x: auto;
    }

    .history-table {
        min-width: 800px;
    }

    .summary-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .violation-grid {
        grid-template-columns: 1fr;
    }
}