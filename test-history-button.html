<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <title>測試歷史按鈕</title>
</head>
<body>
    <button id="historyBtn">📊 違規歷史記錄</button>
    <div id="historyModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5);">
        <div style="background: white; margin: 50px auto; padding: 20px; width: 80%; max-width: 800px;">
            <h3>歷史記錄彈窗</h3>
            <button class="close">×</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const historyBtn = document.getElementById('historyBtn');
            const historyModal = document.getElementById('historyModal');
            
            console.log('historyBtn:', historyBtn);
            console.log('historyModal:', historyModal);
            
            if (historyBtn) {
                historyBtn.addEventListener('click', function() {
                    console.log('按鈕被點擊了！');
                    if (historyModal) {
                        historyModal.style.display = 'flex';
                        historyModal.style.alignItems = 'center';
                        historyModal.style.justifyContent = 'center';
                    }
                });
            }
            
            const closeBtn = document.querySelector('.close');
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    historyModal.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html>