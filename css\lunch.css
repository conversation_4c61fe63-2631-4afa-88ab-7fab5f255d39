/* 午餐登記頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.date-display {
    background: var(--card-background);
    padding: 10px 20px;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-soft);
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
    border: 2px solid var(--candy-lemon-deep);
}

/* 午餐摘要卡片 */
.lunch-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    padding: 20px;
    box-shadow: var(--shadow-soft);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    border: 3px solid transparent;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.summary-card i {
    font-size: 2.5rem;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.summary-card i.fa-users {
    background: var(--gradient-lavender);
}

.summary-card i.fa-star.excellent {
    background: linear-gradient(135deg, #FFD700, #FFA500);
}

.summary-card i.fa-check-circle.good {
    background: linear-gradient(135deg, #FFA726, #FF9800);
}

.summary-card i.fa-exclamation-triangle.warning {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
}

.summary-info {
    display: flex;
    flex-direction: column;
}

.summary-count {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
    color: var(--dark-color);
}

.summary-label {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 評分標準說明 */
.scoring-guide {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-medium);
    border: 3px solid var(--candy-sky);
}

.scoring-guide h3 {
    margin: 0 0 20px 0;
    color: var(--candy-sky-deep);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
    text-align: center;
    justify-content: center;
}

.guide-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.guide-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
    border: 3px solid;
    box-shadow: var(--shadow-soft);
}

.guide-card.excellent {
    border-color: #FFD700;
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(255, 215, 0, 0.1) 100%);
}

.guide-card.good {
    border-color: #FFA726;
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(255, 167, 38, 0.1) 100%);
}

.guide-card.none {
    border-color: #ff7675;
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(255, 118, 117, 0.1) 100%);
}

.guide-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.guide-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.guide-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.guide-desc {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.4;
}

.guide-score {
    font-size: 1.3rem;
    font-weight: 700;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    display: inline-block;
}

.guide-card.excellent .guide-score {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
}

.guide-card.good .guide-score {
    background: linear-gradient(135deg, #FFA726, #FF9800);
    color: white;
}

.guide-card.none .guide-score {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
    color: white;
}

/* 操作按鈕區域 */
.action-buttons {
    display: flex;
    justify-content: space-between;
    margin: 25px 0;
    flex-wrap: wrap;
    gap: 15px;
}

.btn-excellent {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    border: 2px solid #FFD700;
}

.btn-excellent:hover {
    background: linear-gradient(135deg, #FFA500, #FF8C00);
    border-color: #FFA500;
}

.btn-good {
    background: linear-gradient(135deg, #FFA726, #FF9800);
    color: white;
    border: 2px solid #FFA726;
}

.btn-good:hover {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    border-color: #FF9800;
}

.search-box {
    position: relative;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: 2px solid var(--candy-cream);
    border-radius: var(--border-radius-xl);
    font-size: 0.95rem;
    transition: var(--transition);
    background: var(--card-background);
}

.search-box input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--shadow-glow);
}

/* 表格樣式 */
.table-container {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    margin-bottom: 25px;
    border: 3px solid var(--candy-peach);
}

#lunchTable {
    width: 100%;
    border-collapse: collapse;
}

#lunchTable th,
#lunchTable td {
    padding: 15px;
    text-align: left;
    border-bottom: 2px solid var(--candy-cream);
    transition: var(--transition-fast);
}

#lunchTable th {
    background: var(--gradient-peach);
    font-weight: 700;
    color: var(--dark-color);
    white-space: nowrap;
    font-size: 0.95rem;
}

#lunchTable tbody tr:hover {
    background: var(--gradient-lemon);
    transform: scale(1.01);
}

/* 午餐表現狀態標籤 */
.status-badge {
    display: inline-block;
    padding: 6px 14px;
    border-radius: var(--border-radius-xl);
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    min-width: 90px;
    border: 2px solid;
}

.status-excellent {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    border-color: #FFD700;
}

.status-good {
    background: linear-gradient(135deg, #FFA726, #FF9800);
    color: white;
    border-color: #FFA726;
}

.status-not-done {
    background: rgba(255, 118, 117, 0.1);
    color: #ff7675;
    border-color: #ff7675;
}

/* 本月表現統計 */
.monthly-performance {
    text-align: center;
    font-size: 0.9rem;
    font-weight: 600;
}

.performance-excellent {
    color: #FFD700;
}

.performance-good {
    color: #FFA726;
}

.performance-average {
    color: #666;
}

/* 修改記錄顯示區域 */
.current-record {
    background: rgba(135, 206, 235, 0.1);
    border: 2px solid var(--candy-sky);
    border-radius: var(--border-radius-large);
    padding: 20px;
    margin: 20px 0;
}

.record-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.record-info:last-child {
    margin-bottom: 0;
}

.record-label {
    font-weight: 600;
    color: var(--dark-color);
}

.record-value {
    font-weight: 700;
    color: var(--candy-sky-deep);
}

/* 修改按鈕樣式 */
.edit-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border: 2px solid #74b9ff;
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.edit-btn:hover {
    background: linear-gradient(135deg, #0984e3, #0056b3);
    border-color: #0984e3;
    transform: scale(1.05);
}

.edit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 確認彈窗中的評分選項 */
.rating-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 25px 0;
}

.rating-option {
    background: var(--card-background);
    border: 3px solid;
    border-radius: var(--border-radius-large);
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.rating-option.excellent {
    border-color: #FFD700;
}

.rating-option.good {
    border-color: #FFA726;
}

.rating-option.none {
    border-color: #ff7675;
}

.rating-option:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

.rating-option.selected {
    transform: scale(1.1);
    box-shadow: var(--shadow-strong);
}

.rating-option.excellent.selected {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.2));
}

.rating-option.good.selected {
    background: linear-gradient(135deg, rgba(255, 167, 38, 0.2), rgba(255, 152, 0, 0.2));
}

.rating-option.none.selected {
    background: linear-gradient(135deg, rgba(255, 118, 117, 0.2), rgba(253, 121, 168, 0.2));
}

.rating-icon {
    font-size: 3rem;
    margin-bottom: 10px;
}

.rating-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--dark-color);
}

.rating-desc {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 12px;
}

.rating-score {
    font-size: 1.4rem;
    font-weight: 700;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    display: inline-block;
}

.rating-option.excellent .rating-score {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
}

.rating-option.good .rating-score {
    background: linear-gradient(135deg, #FFA726, #FF9800);
    color: white;
}

.rating-option.none .rating-score {
    background: linear-gradient(135deg, #ff7675, #fd79a8);
    color: white;
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 25px;
}

.pagination button {
    width: 40px;
    height: 40px;
    border: 2px solid var(--candy-cream);
    background: var(--card-background);
    border-radius: var(--border-radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
}

.pagination button:hover:not(:disabled) {
    background: var(--gradient-lavender);
    border-color: var(--candy-lavender-deep);
    transform: scale(1.1);
}

.pagination button.active {
    background: var(--gradient-pink);
    color: white;
    border-color: var(--candy-pink-deep);
    transform: scale(1.1);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 992px) {
    .lunch-summary {
        grid-template-columns: 1fr 1fr;
    }
    
    .guide-grid {
        grid-template-columns: 1fr;
    }
    
    .rating-options {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .date-display {
        width: 100%;
        justify-content: center;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .search-box {
        width: 100%;
    }
    
    .lunch-summary {
        grid-template-columns: 1fr;
    }
    
    #lunchTable th,
    #lunchTable td {
        padding: 10px;
        font-size: 0.9rem;
    }
}