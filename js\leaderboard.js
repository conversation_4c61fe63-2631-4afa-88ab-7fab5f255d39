// 榮譽榜功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const viewAllBtn = document.getElementById('viewAllBtn');
    const viewTop5Btn = document.getElementById('viewTop5Btn');
    const viewByClassBtn = document.getElementById('viewByClassBtn');
    const top5Podium = document.getElementById('top5Podium');
    const leaderboardTableBody = document.getElementById('leaderboardTableBody');
    const leaderboardTitle = document.getElementById('leaderboardTitle');
    const classFilter = document.getElementById('classFilter');
    const searchInput = document.getElementById('searchInput');
    const pagination = document.getElementById('pagination');
    const studentModal = document.getElementById('studentModal');
    const studentDetails = document.getElementById('studentDetails');
    const closeModalBtns = document.querySelectorAll('.close');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');

    // 時段選擇器元素
    const totalPeriodBtn = document.getElementById('totalPeriodBtn');
    const weekPeriodBtn = document.getElementById('weekPeriodBtn');
    const monthPeriodBtn = document.getElementById('monthPeriodBtn');
    const semesterPeriodBtn = document.getElementById('semesterPeriodBtn');

    // 時段資訊元素
    const periodRange = document.getElementById('periodRange');
    const participantCount = document.getElementById('participantCount');
    const averageScore = document.getElementById('averageScore');
    const highestScore = document.getElementById('highestScore');
    
    // 獎台元素
    const firstPlaceName = document.getElementById('firstPlaceName');
    const firstPlaceScore = document.getElementById('firstPlaceScore');
    const secondPlaceName = document.getElementById('secondPlaceName');
    const secondPlaceScore = document.getElementById('secondPlaceScore');
    const thirdPlaceName = document.getElementById('thirdPlaceName');
    const thirdPlaceScore = document.getElementById('thirdPlaceScore');
    const fourthPlaceName = document.getElementById('fourthPlaceName');
    const fourthPlaceScore = document.getElementById('fourthPlaceScore');
    const fifthPlaceName = document.getElementById('fifthPlaceName');
    const fifthPlaceScore = document.getElementById('fifthPlaceScore');
    
    // 常數設定
    const ITEMS_PER_PAGE = 15;
    
    // 狀態變數
    let students = [];
    let filteredStudents = [];
    let currentPage = 1;
    let currentView = 'all'; // 'all', 'top5', 'byClass'
    let currentPeriod = 'total'; // 'total', 'week', 'month', 'semester'

    // 學期設定（可根據實際情況調整）
    const semesterConfig = {
        startDate: '2024-09-01', // 學期開始日期
        endDate: '2025-01-31'    // 學期結束日期
    };
    
    // 初始化頁面
    function initPage() {
        loadStudents();
        setupEventListeners();
    }
    
    // 設置事件監聽器
    function setupEventListeners() {
        // 檢視模式切換
        viewAllBtn.addEventListener('click', () => switchView('all'));
        viewTop5Btn.addEventListener('click', () => switchView('top5'));
        viewByClassBtn.addEventListener('click', () => switchView('byClass'));

        // 時段切換
        totalPeriodBtn.addEventListener('click', () => switchPeriod('total'));
        weekPeriodBtn.addEventListener('click', () => switchPeriod('week'));
        monthPeriodBtn.addEventListener('click', () => switchPeriod('month'));
        semesterPeriodBtn.addEventListener('click', () => switchPeriod('semester'));

        classFilter.addEventListener('change', filterStudents);
        searchInput.addEventListener('input', filterStudents);
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', closeModal);
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === studentModal) {
                closeModal();
            }
        });

        // 匯入匯出功能
        exportBtn.addEventListener('click', exportData);
        importBtn.addEventListener('click', () => importFile.click());
        importFile.addEventListener('change', importData);
    }
    
    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
            
            // 確保每個學生都有分數
            students.forEach(student => {
                if (typeof student.score !== 'number') {
                    student.score = 0;
                }
            });
            
            updateClassFilter();
            updatePeriodInfo();
            filterStudents();
            updatePodium();
        } else {
            showEmptyState();
        }
    }
    
    // 更新班級過濾器
    function updateClassFilter() {
        const classes = [...new Set(students.map(s => s.class))].sort();
        classFilter.innerHTML = '<option value="">所有班級</option>';
        
        classes.forEach(className => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = className;
            classFilter.appendChild(option);
        });
    }
    
    // 切換時段
    function switchPeriod(period) {
        currentPeriod = period;

        // 更新按鈕狀態
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeBtn = document.querySelector(`[data-period="${period}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // 更新標題和統計資訊
        updatePeriodInfo();

        // 重新計算排行榜
        filterStudents();
        updatePodium();
    }

    // 切換檢視模式
    function switchView(view) {
        currentView = view;

        // 更新按鈕狀態
        document.querySelectorAll('.view-options .btn').forEach(btn => {
            btn.classList.remove('active');
        });

        if (view === 'all') {
            viewAllBtn.classList.add('active');
            top5Podium.style.display = 'flex';
        } else if (view === 'top5') {
            viewTop5Btn.classList.add('active');
            top5Podium.style.display = 'flex';
        } else if (view === 'byClass') {
            viewByClassBtn.classList.add('active');
            top5Podium.style.display = 'none';
        }

        filterStudents();
    }

    // 獲取時段日期範圍
    function getPeriodDateRange() {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        switch (currentPeriod) {
            case 'week':
                // 本週（週一到週日）
                const dayOfWeek = today.getDay();
                const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                const monday = new Date(today);
                monday.setDate(today.getDate() + mondayOffset);
                const sunday = new Date(monday);
                sunday.setDate(monday.getDate() + 6);
                return { start: monday, end: sunday };

            case 'month':
                // 本月
                const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
                const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                return { start: monthStart, end: monthEnd };

            case 'semester':
                // 學期
                const semesterStart = new Date(semesterConfig.startDate);
                const semesterEnd = new Date(semesterConfig.endDate);
                return { start: semesterStart, end: semesterEnd };

            default:
                // 總排行榜 - 不限制日期
                return null;
        }
    }

    // 更新時段資訊
    function updatePeriodInfo() {
        const dateRange = getPeriodDateRange();
        let rangeText = '';

        switch (currentPeriod) {
            case 'total':
                rangeText = '全部時間';
                leaderboardTitle.textContent = '總排行榜';
                break;
            case 'week':
                if (dateRange) {
                    rangeText = `${formatDate(dateRange.start)} ~ ${formatDate(dateRange.end)}`;
                }
                leaderboardTitle.textContent = '本週排行榜';
                break;
            case 'month':
                if (dateRange) {
                    rangeText = `${dateRange.start.getFullYear()}年${dateRange.start.getMonth() + 1}月`;
                }
                leaderboardTitle.textContent = '本月排行榜';
                break;
            case 'semester':
                if (dateRange) {
                    rangeText = `${formatDate(dateRange.start)} ~ ${formatDate(dateRange.end)}`;
                }
                leaderboardTitle.textContent = '學期排行榜';
                break;
        }

        periodRange.textContent = rangeText;

        // 計算統計資料
        const periodStudents = getStudentsForPeriod();
        const validScores = periodStudents.map(s => s.periodScore || 0).filter(score => score > 0);

        participantCount.textContent = `${validScores.length} 人`;
        averageScore.textContent = validScores.length > 0 ?
            Math.round(validScores.reduce((sum, score) => sum + score, 0) / validScores.length) : '0';
        highestScore.textContent = validScores.length > 0 ? Math.max(...validScores) : '0';
    }

    // 格式化日期
    function formatDate(date) {
        return `${date.getMonth() + 1}/${date.getDate()}`;
    }
    
    // 獲取特定時段的學生資料
    function getStudentsForPeriod() {
        const dateRange = getPeriodDateRange();

        return students.map(student => {
            const studentCopy = { ...student };

            if (currentPeriod === 'total') {
                // 總排行榜使用原始分數
                studentCopy.periodScore = student.score || 0;
            } else {
                // 計算特定時段的分數
                studentCopy.periodScore = calculatePeriodScore(student, dateRange);
            }

            return studentCopy;
        });
    }

    // 計算特定時段的分數
    function calculatePeriodScore(student, dateRange) {
        if (!dateRange) return student.score || 0;

        let totalScore = 0;
        const { start, end } = dateRange;

        // 檢查各種活動記錄
        const activities = [
            'checkins',           // 每日簽到
            'homeworkSubmission', // 作業繳交
            'toothBrushing',      // 刷牙登記
            'cleaning',           // 打掃登記
            'lunch',              // 午餐登記
            'napTime'             // 午休閉眼睛
        ];

        activities.forEach(activity => {
            if (student[activity]) {
                Object.entries(student[activity]).forEach(([dateStr, record]) => {
                    const recordDate = new Date(dateStr);
                    if (recordDate >= start && recordDate <= end) {
                        if (record.score) {
                            totalScore += record.score;
                        } else if (record.checkedIn && activity === 'checkins') {
                            totalScore += 5; // 簽到預設分數
                        }
                    }
                });
            }
        });

        // 檢查其他資料結構（如 seatCleaningData, officerDutiesData 等）
        const otherActivities = [
            'seatCleaningData',
            'officerDutiesData',
            'dutyStudentData',
            'honestyData'
        ];

        otherActivities.forEach(activityKey => {
            const activityData = localStorage.getItem(activityKey);
            if (activityData) {
                try {
                    const data = JSON.parse(activityData);
                    if (data[student.id]) {
                        Object.entries(data[student.id]).forEach(([dateStr, record]) => {
                            const recordDate = new Date(dateStr);
                            if (recordDate >= start && recordDate <= end && record.score) {
                                totalScore += record.score;
                            }
                        });
                    }
                } catch (e) {
                    console.warn(`Error parsing ${activityKey}:`, e);
                }
            }
        });

        // 檢查違規記錄（扣分）
        const disciplineHistory = localStorage.getItem('disciplineHistory');
        if (disciplineHistory) {
            try {
                const violations = JSON.parse(disciplineHistory);
                violations.forEach(violation => {
                    if (violation.studentId === student.id) {
                        const violationDate = new Date(violation.date);
                        if (violationDate >= start && violationDate <= end) {
                            totalScore += violation.score || -5; // 違規通常是負分
                        }
                    }
                });
            } catch (e) {
                console.warn('Error parsing discipline history:', e);
            }
        }

        return Math.max(0, totalScore); // 確保分數不為負數
    }

    // 過濾學生
    function filterStudents() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedClass = classFilter.value;

        // 獲取當前時段的學生資料
        const periodStudents = getStudentsForPeriod();

        filteredStudents = periodStudents.filter(student => {
            const matchesSearch = (
                student.name.toLowerCase().includes(searchTerm) ||
                student.number.toString().includes(searchTerm)
            );

            const matchesClass = !selectedClass || student.class === selectedClass;

            return matchesSearch && matchesClass;
        });

        // 按時段分數排序（降序）
        filteredStudents.sort((a, b) => (b.periodScore || 0) - (a.periodScore || 0));

        // 根據檢視模式過濾
        if (currentView === 'top5') {
            filteredStudents = filteredStudents.slice(0, 5);
        }

        currentPage = 1;
        renderLeaderboard();
        renderPagination();
    }
    
    // 更新獎台
    function updatePodium() {
        // 獲取當前時段的前五名學生
        const periodStudents = getStudentsForPeriod();
        const topStudents = periodStudents
            .sort((a, b) => (b.periodScore || 0) - (a.periodScore || 0))
            .slice(0, 5);

        // 第一名
        if (topStudents[0]) {
            firstPlaceName.textContent = topStudents[0].name;
            firstPlaceScore.textContent = `${topStudents[0].periodScore || 0} 分`;
        } else {
            firstPlaceName.textContent = '--';
            firstPlaceScore.textContent = '0 分';
        }

        // 第二名
        if (topStudents[1]) {
            secondPlaceName.textContent = topStudents[1].name;
            secondPlaceScore.textContent = `${topStudents[1].periodScore || 0} 分`;
        } else {
            secondPlaceName.textContent = '--';
            secondPlaceScore.textContent = '0 分';
        }

        // 第三名
        if (topStudents[2]) {
            thirdPlaceName.textContent = topStudents[2].name;
            thirdPlaceScore.textContent = `${topStudents[2].periodScore || 0} 分`;
        } else {
            thirdPlaceName.textContent = '--';
            thirdPlaceScore.textContent = '0 分';
        }

        // 第四名
        if (topStudents[3]) {
            fourthPlaceName.textContent = topStudents[3].name;
            fourthPlaceScore.textContent = `${topStudents[3].periodScore || 0} 分`;
        } else {
            fourthPlaceName.textContent = '--';
            fourthPlaceScore.textContent = '0 分';
        }

        // 第五名
        if (topStudents[4]) {
            fifthPlaceName.textContent = topStudents[4].name;
            fifthPlaceScore.textContent = `${topStudents[4].periodScore || 0} 分`;
        } else {
            fifthPlaceName.textContent = '--';
            fifthPlaceScore.textContent = '0 分';
        }
    }
    
    // 渲染排行榜
    function renderLeaderboard() {
        if (!filteredStudents.length) {
            leaderboardTableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 30px 0;">沒有找到符合條件的學生</td></tr>';
            return;
        }
        
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, filteredStudents.length);
        const currentPageStudents = filteredStudents.slice(startIndex, endIndex);
        
        leaderboardTableBody.innerHTML = '';
        
        currentPageStudents.forEach((student, index) => {
            const globalRank = startIndex + index + 1;
            const row = document.createElement('tr');
            row.style.cursor = 'pointer';
            
            row.innerHTML = `
                <td class="rank-cell ${getRankClass(globalRank)}">${globalRank}</td>
                <td>${student.number}</td>
                <td>${student.name}</td>
                <td>${student.class}</td>
                <td><strong>${student.periodScore || 0} 分</strong></td>
                <td>${getBadges(globalRank, student.periodScore || 0)}</td>
            `;
            
            // 添加點擊事件
            row.addEventListener('click', () => showStudentDetails(student, globalRank));
            
            leaderboardTableBody.appendChild(row);
        });
    }
    
    // 獲取排名樣式類別
    function getRankClass(rank) {
        if (rank === 1) return 'rank-1';
        if (rank === 2) return 'rank-2';
        if (rank === 3) return 'rank-3';
        if (rank === 4) return 'rank-4';
        if (rank === 5) return 'rank-5';
        return '';
    }
    
    // 獲取獎章
    function getBadges(rank, score) {
        let badges = '';

        if (rank === 1) {
            badges += '<div class="badge gold"><i class="fas fa-trophy"></i></div>';
        } else if (rank === 2) {
            badges += '<div class="badge silver"><i class="fas fa-medal"></i></div>';
        } else if (rank === 3) {
            badges += '<div class="badge bronze"><i class="fas fa-award"></i></div>';
        } else if (rank === 4) {
            badges += '<div class="badge fourth"><i class="fas fa-ribbon"></i></div>';
        } else if (rank === 5) {
            badges += '<div class="badge fifth"><i class="fas fa-certificate"></i></div>';
        }

        // 前10%獎章
        const totalStudents = filteredStudents.length;
        if (rank <= Math.ceil(totalStudents * 0.1) && rank > 5) {
            badges += '<div class="badge star"><i class="fas fa-star"></i></div>';
        }

        // 進步獎（根據時段調整標準）
        const progressThreshold = currentPeriod === 'total' ? 50 :
                                 currentPeriod === 'week' ? 15 :
                                 currentPeriod === 'month' ? 30 : 40;
        if (score >= progressThreshold) {
            badges += '<div class="badge fire"><i class="fas fa-fire"></i></div>';
        }

        return badges || '<span class="text-muted">--</span>';
    }
    
    // 渲染分頁
    function renderPagination() {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一頁
        paginationHTML += `
            <button class="page-btn" id="prevPage" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 頁碼
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        // 下一頁
        paginationHTML += `
            <button class="page-btn" id="nextPage" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        pagination.innerHTML = paginationHTML;
        
        // 添加事件監聽器
        document.querySelectorAll('.page-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.id === 'prevPage' && currentPage > 1) {
                    changePage(currentPage - 1);
                } else if (this.id === 'nextPage' && currentPage < totalPages) {
                    changePage(currentPage + 1);
                } else {
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page && !isNaN(page)) changePage(page);
                }
            });
        });
    }
    
    // 切換頁面
    function changePage(page) {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        if (page < 1 || page > totalPages) return;
        
        currentPage = page;
        renderLeaderboard();
        renderPagination();
    }
    
    // 顯示學生詳情
    function showStudentDetails(student, rank) {
        const checkinCount = getCheckinCount(student);
        const recentActivity = getRecentActivity(student);
        
        studentDetails.innerHTML = `
            <div class="student-profile">
                <div class="student-avatar">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="student-info">
                    <h4>${student.name}</h4>
                    <p>學號：${student.number} | 班級：${student.class}</p>
                    <p>目前排名：第 ${rank} 名</p>
                </div>
            </div>
            
            <div class="student-stats">
                <div class="stat-item">
                    <i class="fas fa-star"></i>
                    <div>
                        <span class="stat-value">${student.periodScore || 0}</span>
                        <span class="stat-label">${getPeriodScoreLabel()}</span>
                    </div>
                </div>
                <div class="stat-item">
                    <i class="fas fa-calendar-check"></i>
                    <div>
                        <span class="stat-value">${checkinCount}</span>
                        <span class="stat-label">簽到次數</span>
                    </div>
                </div>
                <div class="stat-item">
                    <i class="fas fa-trophy"></i>
                    <div>
                        <span class="stat-value">${rank}</span>
                        <span class="stat-label">目前排名</span>
                    </div>
                </div>
            </div>
            
            <div class="badges-earned">
                <h5>獲得獎章</h5>
                <div class="badges-list">
                    ${getBadges(rank, student.periodScore || 0)}
                </div>
            </div>
        `;
        
        studentModal.style.display = 'flex';
    }
    
    // 獲取簽到次數
    function getCheckinCount(student) {
        if (!student.checkins) return 0;
        return Object.values(student.checkins).filter(checkin => checkin.checkedIn).length;
    }
    
    // 獲取最近活動（簡化版本）
    function getRecentActivity(student) {
        return [
            { type: 'checkin', date: '今天', description: '每日簽到 +5分' },
            { type: 'lucky', date: '昨天', description: '幸運轉蛋 +10分' }
        ];
    }
    
    // 顯示空狀態
    function showEmptyState() {
        leaderboardTableBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 50px 0;">
                    <i class="fas fa-users" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                    <p>還沒有學生資料</p>
                    <a href="index.html" class="btn btn-primary">前往新增學生</a>
                </td>
            </tr>
        `;
        
        // 清空獎台
        firstPlaceName.textContent = '--';
        firstPlaceScore.textContent = '0 分';
        secondPlaceName.textContent = '--';
        secondPlaceScore.textContent = '0 分';
        thirdPlaceName.textContent = '--';
        thirdPlaceScore.textContent = '0 分';
        fourthPlaceName.textContent = '--';
        fourthPlaceScore.textContent = '0 分';
        fifthPlaceName.textContent = '--';
        fifthPlaceScore.textContent = '0 分';
    }
    
    // 關閉彈窗
    function closeModal() {
        studentModal.style.display = 'none';
    }
    
    // 獲取時段分數標籤
    function getPeriodScoreLabel() {
        switch (currentPeriod) {
            case 'total': return '總分數';
            case 'week': return '本週分數';
            case 'month': return '本月分數';
            case 'semester': return '學期分數';
            default: return '分數';
        }
    }

    // 初始化頁面
    initPage();
    
    // 匯出資料
    function exportData() {
        const dataStr = JSON.stringify(students, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `學生名單_${new Date().toISOString().split('T')[0]}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    }

    // 匯入資料
    function importData(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedStudents = JSON.parse(e.target.result);
                if (Array.isArray(importedStudents)) {
                    if (confirm(`確定要匯入 ${importedStudents.length} 筆學生資料嗎？這將覆蓋現有資料。`)) {
                        localStorage.setItem('students', JSON.stringify(importedStudents));
                        loadStudents(); // 重新載入資料
                        alert('資料匯入成功！');
                    }
                } else {
                    alert('匯入的檔案格式不正確！');
                }
            } catch (error) {
                console.error('匯入錯誤:', error);
                alert('匯入檔案時發生錯誤！');
            }
            // 重置檔案輸入，允許重複選擇同一個檔案
            importFile.value = '';
        };
        reader.readAsText(file);
    }

    // 監聽本地存儲變化，實時更新排行榜
    window.addEventListener('storage', function(e) {
        if (e.key === 'students') {
            loadStudents();
        }
    });
});
