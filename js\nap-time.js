// 午休閉眼睛功能
document.addEventListener('DOMContentLoaded', function() {
    // 獲取DOM元素
    const napTimeTableBody = document.getElementById('napTimeTableBody');
    const markAllSleepingBtn = document.getElementById('markAllSleepingBtn');
    const markAllQuietBtn = document.getElementById('markAllQuietBtn');
    const unmarkAllBtn = document.getElementById('unmarkAllBtn');
    const searchInput = document.getElementById('searchInput');
    const confirmModal = document.getElementById('confirmModal');
    const editModal = document.getElementById('editModal');
    const confirmMessage = document.getElementById('confirmMessage');
    const studentNameSpan = document.getElementById('studentNameSpan');
    const editStudentNameSpan = document.getElementById('editStudentNameSpan');
    const currentRecord = document.getElementById('currentRecord');
    const currentTime = document.getElementById('currentTime');
    const confirmNapBtn = document.getElementById('confirmNapBtn');
    const confirmEditBtn = document.getElementById('confirmEditBtn');
    const cancelNapBtn = document.getElementById('cancelNapBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const closeModalBtns = document.querySelectorAll('.close');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const importFile = document.getElementById('importFile');
    const currentDateElement = document.getElementById('currentDate');
    const totalStudentsElement = document.getElementById('totalStudents');
    const sleepingCountElement = document.getElementById('sleepingCount');
    const quietCountElement = document.getElementById('quietCount');
    const notRecordedCountElement = document.getElementById('notRecordedCount');
    const paginationElement = document.getElementById('pagination');
    
    // 常數設定
    const ITEMS_PER_PAGE = 10; // 每頁顯示的學生數量
    const SLEEPING_REWARD = 5; // 閉眼睛睡覺獎勵分數
    const QUIET_REWARD = 3; // 守秩序獎勵分數
    
    // 狀態變數
    let students = [];
    let filteredStudents = [];
    let currentPage = 1;
    let selectedStudent = null;
    let selectedRating = null;
    let isEditMode = false; // 是否為修改模式
    let originalRecord = null; // 原始記錄
    let today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    
    // 初始化頁面
    function initPage() {
        // 設置當前日期
        const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
        const todayFormatted = new Date().toLocaleDateString('zh-TW', options);
        currentDateElement.textContent = todayFormatted;
        
        // 載入學生資料
        loadStudents();
        
        // 事件監聽器
        markAllSleepingBtn.addEventListener('click', () => markAllStudents('sleeping'));
        markAllQuietBtn.addEventListener('click', () => markAllStudents('quiet'));
        unmarkAllBtn.addEventListener('click', unmarkAllStudents);
        searchInput.addEventListener('input', filterStudents);
        confirmNapBtn.addEventListener('click', confirmNap);
        confirmEditBtn.addEventListener('click', confirmEdit);
        cancelNapBtn.addEventListener('click', closeModal);
        cancelEditBtn.addEventListener('click', closeEditModal);
        
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                closeModal();
                closeEditModal();
            });
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === confirmModal) {
                closeModal();
            }
            if (e.target === editModal) {
                closeEditModal();
            }
        });

        // 匯入匯出功能
        exportBtn.addEventListener('click', exportData);
        importBtn.addEventListener('click', () => importFile.click());
        importFile.addEventListener('change', importData);
        
        // 評分選項點擊事件 - 使用事件委託
        document.addEventListener('click', function(e) {
            if (e.target.closest('.rating-option')) {
                const option = e.target.closest('.rating-option');
                const modal = option.closest('.modal');
                const isEditModal = modal.id === 'editModal';
                
                // 只選中同一個模態框內的選項
                const modalOptions = modal.querySelectorAll('.rating-option');
                modalOptions.forEach(opt => opt.classList.remove('selected'));
                
                option.classList.add('selected');
                selectedRating = option.dataset.rating;
                
                if (isEditModal) {
                    confirmEditBtn.disabled = false;
                } else {
                    confirmNapBtn.disabled = false;
                }
            }
        });
    }
    
    // 載入學生資料
    function loadStudents() {
        const savedStudents = localStorage.getItem('students');
        if (savedStudents) {
            students = JSON.parse(savedStudents);
            
            // 初始化每個學生的午休記錄
            students.forEach(student => {
                if (!student.napTime) {
                    student.napTime = {};
                }
                
                // 如果今天還沒有午休記錄，初始化為未記錄
                if (!student.napTime[today]) {
                    student.napTime[today] = {
                        status: 'none', // 'sleeping', 'quiet', 'none'
                        timestamp: null,
                        score: 0
                    };
                }
            });
            
            // 保存更新後的學生資料
            saveStudents();
            
            // 過濾和顯示學生
            filterStudents();
            updateSummary();
        } else {
            alert('找不到學生資料，請先新增學生。');
            window.location.href = 'index.html';
        }
    }
    
    // 過濾學生
    function filterStudents() {
        const searchTerm = searchInput.value.toLowerCase();
        
        filteredStudents = students.filter(student => {
            return (
                student.name.toLowerCase().includes(searchTerm) ||
                student.number.toString().includes(searchTerm) ||
                student.class.toString().includes(searchTerm)
            );
        });
        
        // 重置到第一頁
        currentPage = 1;
        renderStudentTable();
        renderPagination();
        updateSummary();
    }
    
    // 渲染學生表格
    function renderStudentTable() {
        if (!filteredStudents.length) {
            napTimeTableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 30px 0;">沒有找到符合條件的學生</td></tr>';
            return;
        }
        
        // 計算當前頁的學生
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, filteredStudents.length);
        const currentPageStudents = filteredStudents.slice(startIndex, endIndex);
        
        napTimeTableBody.innerHTML = '';
        
        currentPageStudents.forEach(student => {
            const napRecord = student.napTime[today];
            const timestamp = napRecord?.timestamp;
            const timeString = timestamp ? formatTime(new Date(timestamp)) : '-';
            const monthlyPerformance = calculateMonthlyPerformance(student);
            
            const row = document.createElement('tr');
            
            // 動物頭像 - 使用統一的動物頭像顯示邏輯
            let animalDisplay = student.animal || '🐱';
            if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
                const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
                const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
                animalDisplay = `<div class="student-animal-avatar">
                    <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                    <span class="animal-emoji">${animalInfo.emoji}</span>
                </div>`;
            }
            const animalAvatar = animalDisplay;
            
            // 狀態顯示
            let statusBadge = '';
            let actionButton = '';
            
            switch (napRecord.status) {
                case 'sleeping':
                    statusBadge = '<span class="status-badge status-sleeping">閉眼睛睡覺</span>';
                    actionButton = `
                        <button class="btn btn-sm edit-btn" data-id="${student.id}">
                            <i class="fas fa-edit"></i> 修改
                        </button>
                    `;
                    break;
                case 'quiet':
                    statusBadge = '<span class="status-badge status-quiet">守秩序</span>';
                    actionButton = `
                        <button class="btn btn-sm edit-btn" data-id="${student.id}">
                            <i class="fas fa-edit"></i> 修改
                        </button>
                    `;
                    break;
                default:
                    statusBadge = '<span class="status-badge status-not-recorded">未記錄</span>';
                    actionButton = `<button class="btn btn-sm btn-primary nap-btn" data-id="${student.id}">
                        <i class="fas fa-bed"></i> 登記午休
                    </button>`;
            }
            
            row.innerHTML = `
                <td>
                    <div class="student-avatar">${animalAvatar}</div>
                </td>
                <td>${student.number}</td>
                <td>${student.name}</td>
                <td class="score">${student.score || 0} 分</td>
                <td>${statusBadge}</td>
                <td class="time-column">${timeString}</td>
                <td class="monthly-performance ${getPerformanceClass(monthlyPerformance)}">${monthlyPerformance}%</td>
                <td>${actionButton}</td>
            `;
            
            napTimeTableBody.appendChild(row);
        });
        
        // 添加午休按鈕事件監聽器
        document.querySelectorAll('.nap-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const studentId = parseInt(this.getAttribute('data-id'));
                showNapConfirmation(studentId);
            });
        });
        
        // 添加修改按鈕事件監聽器
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const studentId = parseInt(this.getAttribute('data-id'));
                showEditConfirmation(studentId);
            });
        });
    }
    
    // 計算學生本月午休表現百分比
    function calculateMonthlyPerformance(student) {
        if (!student.napTime) return 0;
        
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        let totalWorkdays = 0;
        let completedDays = 0;
        
        for (let d = new Date(firstDay); d <= lastDay && d <= now; d.setDate(d.getDate() + 1)) {
            // 跳過週末
            if (d.getDay() === 0 || d.getDay() === 6) continue;
            
            totalWorkdays++;
            const dateStr = d.toISOString().split('T')[0];
            
            if (student.napTime[dateStr] && student.napTime[dateStr].status !== 'none') {
                completedDays++;
            }
        }
        
        return totalWorkdays === 0 ? 0 : Math.round((completedDays / totalWorkdays) * 100);
    }
    
    // 獲取表現等級的CSS類別
    function getPerformanceClass(percentage) {
        if (percentage >= 80) return 'performance-sleeping';
        if (percentage >= 60) return 'performance-quiet';
        return 'performance-average';
    }
    
    // 渲染分頁
    function renderPagination() {
        const totalPages = Math.ceil(filteredStudents.length / ITEMS_PER_PAGE);
        
        if (totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // 上一頁按鈕
        paginationHTML += `
            <button class="page-btn" id="prevPage" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 頁碼按鈕
        for (let i = 1; i <= totalPages; i++) {
            paginationHTML += `
                <button class="page-btn ${i === currentPage ? 'active' : ''}" 
                        data-page="${i}">${i}</button>
            `;
        }
        
        // 下一頁按鈕
        paginationHTML += `
            <button class="page-btn" id="nextPage" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        paginationElement.innerHTML = paginationHTML;
        
        // 分頁事件監聽器
        document.getElementById('prevPage')?.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                renderStudentTable();
                renderPagination();
            }
        });
        
        document.getElementById('nextPage')?.addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                renderStudentTable();
                renderPagination();
            }
        });
        
        document.querySelectorAll('[data-page]').forEach(btn => {
            btn.addEventListener('click', function() {
                currentPage = parseInt(this.getAttribute('data-page'));
                renderStudentTable();
                renderPagination();
            });
        });
    }
    
    // 更新摘要統計
    function updateSummary() {
        const totalStudents = filteredStudents.length;
        const sleepingCount = filteredStudents.filter(s => s.napTime[today]?.status === 'sleeping').length;
        const quietCount = filteredStudents.filter(s => s.napTime[today]?.status === 'quiet').length;
        const notRecordedCount = filteredStudents.filter(s => s.napTime[today]?.status === 'none').length;
        
        totalStudentsElement.textContent = totalStudents;
        sleepingCountElement.textContent = sleepingCount;
        quietCountElement.textContent = quietCount;
        notRecordedCountElement.textContent = notRecordedCount;
    }
    
    // 顯示午休確認彈窗
    function showNapConfirmation(studentId) {
        selectedStudent = students.find(s => s.id === studentId);
        if (!selectedStudent) return;
        
        studentNameSpan.textContent = selectedStudent.name;
        selectedRating = null;
        isEditMode = false;
        
        // 重置選項狀態
        document.querySelectorAll('#confirmModal .rating-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        
        confirmNapBtn.disabled = true;
        confirmModal.style.display = 'block';
    }
    
    // 顯示修改確認彈窗
    function showEditConfirmation(studentId) {
        selectedStudent = students.find(s => s.id === studentId);
        if (!selectedStudent) return;
        
        const napRecord = selectedStudent.napTime[today];
        originalRecord = { ...napRecord };
        
        editStudentNameSpan.textContent = selectedStudent.name;
        
        // 顯示當前記錄
        const statusText = napRecord.status === 'sleeping' ? '閉眼睛睡覺' :
                          napRecord.status === 'quiet' ? '守秩序' : '未記錄';
        currentRecord.textContent = statusText;
        currentTime.textContent = napRecord.timestamp ? 
            formatDateTime(new Date(napRecord.timestamp)) : '無';
        
        selectedRating = null;
        isEditMode = true;
        
        // 重置選項狀態
        document.querySelectorAll('#editModal .rating-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        
        confirmEditBtn.disabled = true;
        editModal.style.display = 'block';
    }
    
    // 確認午休登記
    function confirmNap() {
        if (!selectedStudent || !selectedRating) return;
        
        const previousScore = selectedStudent.napTime[today]?.score || 0;
        const timestamp = new Date();
        let newScore = 0;
        
        // 計算新分數
        switch (selectedRating) {
            case 'sleeping':
                newScore = SLEEPING_REWARD;
                break;
            case 'quiet':
                newScore = QUIET_REWARD;
                break;
            default:
                newScore = 0;
        }
        
        // 更新學生記錄
        selectedStudent.napTime[today] = {
            status: selectedRating,
            timestamp: timestamp.toISOString(),
            score: newScore
        };
        
        // 更新學生總分
        selectedStudent.score = (selectedStudent.score || 0) - previousScore + newScore;
        
        // 保存資料
        saveStudents();
        
        // 更新顯示
        filterStudents();
        
        // 關閉彈窗
        closeModal();
        
        // 顯示成功訊息
        showSuccessMessage(`${selectedStudent.name} 的午休表現已記錄！獲得 ${newScore} 分`);
    }
    
    // 確認修改
    function confirmEdit() {
        if (!selectedStudent || !selectedRating) return;
        
        const previousScore = originalRecord.score || 0;
        let newScore = 0;
        let timestamp = new Date();
        
        // 如果選擇取消記錄
        if (selectedRating === 'none') {
            selectedStudent.napTime[today] = {
                status: 'none',
                timestamp: null,
                score: 0
            };
            newScore = 0;
        } else {
            // 計算新分數
            switch (selectedRating) {
                case 'sleeping':
                    newScore = SLEEPING_REWARD;
                    break;
                case 'quiet':
                    newScore = QUIET_REWARD;
                    break;
            }
            
            selectedStudent.napTime[today] = {
                status: selectedRating,
                timestamp: timestamp.toISOString(),
                score: newScore
            };
        }
        
        // 更新學生總分
        selectedStudent.score = (selectedStudent.score || 0) - previousScore + newScore;
        
        // 保存資料
        saveStudents();
        
        // 更新顯示
        filterStudents();
        
        // 關閉彈窗
        closeEditModal();
        
        // 顯示成功訊息
        const actionText = selectedRating === 'none' ? '已取消記錄' : `已修改為${getStatusText(selectedRating)}，獲得 ${newScore} 分`;
        showSuccessMessage(`${selectedStudent.name} 的午休記錄${actionText}！`);
    }
    
    // 獲取狀態文字
    function getStatusText(status) {
        switch (status) {
            case 'sleeping': return '閉眼睛睡覺';
            case 'quiet': return '守秩序';
            default: return '未記錄';
        }
    }
    
    // 全部標記為指定狀態
    function markAllStudents(status) {
        if (!confirm(`確定要將所有學生標記為「${getStatusText(status)}」嗎？`)) return;
        
        const score = status === 'sleeping' ? SLEEPING_REWARD : QUIET_REWARD;
        const timestamp = new Date().toISOString();
        
        filteredStudents.forEach(student => {
            const previousScore = student.napTime[today]?.score || 0;
            
            student.napTime[today] = {
                status: status,
                timestamp: timestamp,
                score: score
            };
            
            // 更新學生總分
            student.score = (student.score || 0) - previousScore + score;
        });
        
        // 保存資料
        saveStudents();
        
        // 更新顯示
        filterStudents();
        
        showSuccessMessage(`已將所有學生標記為「${getStatusText(status)}」！`);
    }
    
    // 取消全部標記
    function unmarkAllStudents() {
        if (!confirm('確定要取消所有學生的午休記錄嗎？')) return;
        
        filteredStudents.forEach(student => {
            const previousScore = student.napTime[today]?.score || 0;
            
            student.napTime[today] = {
                status: 'none',
                timestamp: null,
                score: 0
            };
            
            // 更新學生總分
            student.score = (student.score || 0) - previousScore;
        });
        
        // 保存資料
        saveStudents();
        
        // 更新顯示
        filterStudents();
        
        showSuccessMessage('已取消所有學生的午休記錄！');
    }
    
    // 關閉確認彈窗
    function closeModal() {
        confirmModal.style.display = 'none';
        selectedStudent = null;
        selectedRating = null;
        isEditMode = false;
    }
    
    // 關閉修改彈窗
    function closeEditModal() {
        editModal.style.display = 'none';
        selectedStudent = null;
        selectedRating = null;
        isEditMode = false;
        originalRecord = null;
    }
    
    // 保存學生資料
    function saveStudents() {
        localStorage.setItem('students', JSON.stringify(students));
    }
    
    // 格式化時間 (HH:MM)
    function formatTime(date) {
        return date.toLocaleTimeString('zh-TW', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    // 格式化日期時間
    function formatDateTime(date) {
        return date.toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    // 顯示成功訊息
    function showSuccessMessage(message) {
        // 創建訊息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = 'success-message';
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-weight: 600;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(messageDiv);
        
        // 動畫顯示
        setTimeout(() => {
            messageDiv.style.transform = 'translateX(0)';
        }, 100);
        
        // 3秒後自動移除
        setTimeout(() => {
            messageDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 300);
        }, 3000);
    }
    
    // 匯出資料
    function exportData() {
        const dataToExport = {
            students: students,
            exportDate: new Date().toISOString(),
            type: 'nap-time-data'
        };
        
        const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `午休閉眼睛資料_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showSuccessMessage('午休資料已匯出！');
    }
    
    // 匯入資料
    function importData(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedData = JSON.parse(e.target.result);
                
                if (importedData.type === 'nap-time-data' && importedData.students) {
                    if (confirm('確定要匯入資料嗎？這將覆蓋現有的午休資料。')) {
                        students = importedData.students;
                        saveStudents();
                        loadStudents();
                        showSuccessMessage('午休資料已匯入！');
                    }
                } else {
                    alert('檔案格式不正確，請選擇有效的午休資料檔案。');
                }
            } catch (error) {
                alert('檔案讀取失敗，請確認檔案格式正確。');
            }
        };
        
        reader.readAsText(file);
        event.target.value = ''; // 清空文件選擇
    }
    
    // 初始化頁面
    initPage();
});