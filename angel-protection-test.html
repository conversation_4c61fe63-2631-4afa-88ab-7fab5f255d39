<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天使保護功能測試</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/manual-score.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .test-container {
            max-width: 900px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        
        .student-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-student-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            position: relative;
        }
        
        .test-student-card.has-protection {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
        }
        
        .angel-icon {
            position: absolute;
            top: -10px;
            right: -10px;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            animation: angelGlow 2s infinite alternate;
        }
        
        @keyframes angelGlow {
            0% { box-shadow: 0 2px 8px rgba(0,0,0,0.2), 0 0 0 0 rgba(116, 185, 255, 0.4); }
            100% { box-shadow: 0 2px 8px rgba(0,0,0,0.2), 0 0 0 10px rgba(116, 185, 255, 0); }
        }
        
        .student-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .student-score {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .protection-status {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-bottom: 15px;
            display: inline-block;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn-add {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }
        
        .btn-subtract {
            background: linear-gradient(135deg, #ff7675, #e84393);
            color: white;
        }
        
        .btn-angel {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .log-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .log-protection {
            background: #e3f2fd;
            border-left: 4px solid #74b9ff;
        }
        
        .log-normal {
            background: #f0f0f0;
            border-left: 4px solid #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>😇 天使保護功能測試</h1>
            <p>測試天使保護在回頭扣分時的自動觸發和使用機制</p>
        </div>
        
        <div class="test-section">
            <h3>📋 測試學生</h3>
            <div class="student-cards" id="studentCards">
                <!-- 學生卡片將由 JavaScript 生成 -->
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 測試操作</h3>
            <div style="text-align: center;">
                <button class="test-btn btn-angel" onclick="giveAllAngelsProtection()">
                    <i class="fas fa-shield-alt"></i> 給所有學生天使保護
                </button>
                <button class="test-btn" style="background: #6c757d; color: white;" onclick="removeAllProtection()">
                    <i class="fas fa-times"></i> 移除所有保護
                </button>
                <button class="test-btn" style="background: #28a745; color: white;" onclick="resetScores()">
                    <i class="fas fa-refresh"></i> 重置分數
                </button>
                <button class="test-btn" style="background: #dc3545; color: white;" onclick="clearLog()">
                    <i class="fas fa-trash"></i> 清除日誌
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 操作日誌</h3>
            <div class="log-section" id="logSection">
                <div class="log-entry">等待操作...</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📖 使用說明</h3>
            <ul style="line-height: 1.8;">
                <li><strong>天使保護機制：</strong>當學生有天使保護狀態時，第一次回頭扣分會被免除，保護狀態同時消失</li>
                <li><strong>獲得方式：</strong>通過神祕寶箱的「天使」事件獲得，或使用測試按鈕手動添加</li>
                <li><strong>視覺提示：</strong>有保護的學生卡片會顯示天使圖示和特殊邊框</li>
                <li><strong>使用效果：</strong>保護生效時會顯示特殊動畫和通知</li>
                <li><strong>記錄追蹤：</strong>所有保護使用都會記錄在操作歷史中</li>
            </ul>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script src="js/mystery-box-config.js"></script>
    <script src="js/manual-score.js"></script>
    <script>
        // 測試學生資料
        let testStudents = [
            { id: 1, name: '小明', number: '01', class: '603', score: 15 },
            { id: 2, name: '小華', number: '02', class: '603', score: 20 },
            { id: 3, name: '小美', number: '03', class: '603', score: 12 },
            { id: 4, name: '小強', number: '04', class: '603', score: 18 }
        ];
        
        let operationLog = [];
        
        // 初始化
        function init() {
            window.students = [...testStudents];
            updateStudentCards();
            log('測試環境初始化完成');
        }
        
        // 更新學生卡片顯示
        function updateStudentCards() {
            const container = document.getElementById('studentCards');
            container.innerHTML = '';
            
            testStudents.forEach(student => {
                const hasProtection = student.specialStates && student.specialStates.angelProtection;
                
                const card = document.createElement('div');
                card.className = `test-student-card ${hasProtection ? 'has-protection' : ''}`;
                
                card.innerHTML = `
                    ${hasProtection ? '<div class="angel-icon">😇</div>' : ''}
                    <div class="student-name">${student.name}</div>
                    <div class="student-score">${student.score} 分</div>
                    ${hasProtection ? '<div class="protection-status">😇 天使保護</div>' : ''}
                    <div class="test-buttons">
                        <button class="test-btn btn-add" onclick="adjustScore(${student.id}, 5)">
                            +5分
                        </button>
                        <button class="test-btn btn-subtract" onclick="adjustScore(${student.id}, -3)">
                            -3分
                        </button>
                        <button class="test-btn btn-subtract" onclick="adjustScore(${student.id}, -10)">
                            -10分
                        </button>
                        <button class="test-btn btn-angel" onclick="giveAngelProtection(${student.id})">
                            給予保護
                        </button>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }
        
        // 調整分數
        function adjustScore(studentId, change) {
            const student = testStudents.find(s => s.id === studentId);
            if (!student) return;
            
            const oldScore = student.score;
            const hadProtection = student.specialStates && student.specialStates.angelProtection;
            
            log(`嘗試為 ${student.name} ${change > 0 ? '加' : '減'}${Math.abs(change)}分 (當前: ${oldScore}分, 保護: ${hadProtection ? '有' : '無'})`);
            
            // 模擬 applyQuickScore 的邏輯
            if (change < 0 && hadProtection) {
                // 使用天使保護
                delete student.specialStates.angelProtection;
                if (Object.keys(student.specialStates).length === 0) {
                    delete student.specialStates;
                }
                
                log(`🛡️ ${student.name} 的天使保護生效！免除了 ${Math.abs(change)} 分回頭扣分`, 'protection');
                
                // 顯示保護生效通知
                showProtectionNotification(student, Math.abs(change));
            } else {
                // 正常調整分數
                student.score = Math.max(0, oldScore + change);
                log(`✅ ${student.name} 分數調整：${oldScore} → ${student.score}`, 'normal');
            }
            
            // 更新全局學生資料
            window.students = [...testStudents];
            updateStudentCards();
        }
        
        // 給予天使保護
        function giveAngelProtection(studentId) {
            const student = testStudents.find(s => s.id === studentId);
            if (!student) return;
            
            if (!student.specialStates) {
                student.specialStates = {};
            }
            
            if (student.specialStates.angelProtection) {
                log(`${student.name} 已經有天使保護了`);
                return;
            }
            
            student.specialStates.angelProtection = true;
            log(`😇 給予 ${student.name} 天使保護`, 'protection');
            
            window.students = [...testStudents];
            updateStudentCards();
        }
        
        // 給所有學生天使保護
        function giveAllAngelsProtection() {
            testStudents.forEach(student => {
                if (!student.specialStates) {
                    student.specialStates = {};
                }
                student.specialStates.angelProtection = true;
            });
            
            log('😇 給予所有學生天使保護', 'protection');
            window.students = [...testStudents];
            updateStudentCards();
        }
        
        // 移除所有保護
        function removeAllProtection() {
            testStudents.forEach(student => {
                if (student.specialStates) {
                    delete student.specialStates.angelProtection;
                    if (Object.keys(student.specialStates).length === 0) {
                        delete student.specialStates;
                    }
                }
            });
            
            log('❌ 移除所有天使保護');
            window.students = [...testStudents];
            updateStudentCards();
        }
        
        // 重置分數
        function resetScores() {
            testStudents.forEach(student => {
                student.score = 15;
            });
            
            log('🔄 重置所有學生分數為15分');
            window.students = [...testStudents];
            updateStudentCards();
        }
        
        // 顯示保護生效通知
        function showProtectionNotification(student, protectedScore) {
            // 創建簡化版通知
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, #74b9ff, #0984e3);
                color: white;
                padding: 20px 30px;
                border-radius: 15px;
                font-size: 1.2rem;
                font-weight: bold;
                z-index: 3000;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                animation: bounceIn 0.5s ease;
            `;
            
            notification.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">😇</div>
                    <div>天使保護生效！</div>
                    <div style="font-size: 1rem; opacity: 0.9; margin-top: 5px;">
                        ${student.name} 免除了 ${protectedScore} 分回頭扣分
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // 2秒後自動移除
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 2000);
        }
        
        // 記錄日誌
        function log(message, type = 'normal') {
            const timestamp = new Date().toLocaleTimeString();
            operationLog.push({ time: timestamp, message, type });
            
            const logSection = document.getElementById('logSection');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            
            logSection.appendChild(entry);
            logSection.scrollTop = logSection.scrollHeight;
            
            console.log(`[天使保護測試] ${message}`);
        }
        
        // 清除日誌
        function clearLog() {
            operationLog = [];
            document.getElementById('logSection').innerHTML = '<div class="log-entry">日誌已清除...</div>';
        }
        
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
