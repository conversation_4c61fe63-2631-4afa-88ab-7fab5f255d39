<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 秩序禮儀</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/discipline.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li><a href="homework-submission.html"><i class="fas fa-book"></i> 📚 作業繳交</a></li>
                    <li><a href="homework-history.html"><i class="fas fa-clipboard-list"></i> 📋 繳交歷史</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li class="active"><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>

        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <!-- 浮動裝飾元素 -->
            <div class="floating-decorations">
                <div class="floating-decoration">⚖️</div>
                <div class="floating-decoration">🐶</div>
                <div class="floating-decoration">📏</div>
            </div>
            
            <div class="header-container">
                <h2><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀 - 生活常規管理
                    <span class="title-decoration">
                        <img src="images/animals/dog.png" alt="小狗" class="title-animal-icon">
                    </span>
                </h2>
                <div class="date-display">
                    <span id="currentDate"></span>
                </div>
            </div>
            
            <!-- 統計摘要 -->
            <div class="discipline-summary">
                <div class="summary-card">
                    <i class="fas fa-users"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="totalStudents">0</span>
                        <span class="summary-label">總學生數</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-exclamation-triangle violation"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="todayViolations">0</span>
                        <span class="summary-label">今日違規</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-calendar-times total"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="totalViolations">0</span>
                        <span class="summary-label">總違規次數</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-minus-circle penalty"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="totalPenalty">0</span>
                        <span class="summary-label">總扣分</span>
                    </div>
                </div>
            </div>

            <!-- 生活常規規則說明 -->
            <div class="rules-guide">
                <h3><i class="fas fa-list-ul"></i> 📋 生活常規 - 需要改進的行為</h3>
                <div class="rules-grid">
                    <div class="rule-card" data-violation="attitude">
                        <div class="rule-number">1</div>
                        <div class="rule-text">對老師態度不佳</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="talking_back">
                        <div class="rule-number">2</div>
                        <div class="rule-text">對老師幹部頂嘴</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="talking_after_bell">
                        <div class="rule-number">3</div>
                        <div class="rule-text">鐘響講話</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="talking_without_permission">
                        <div class="rule-number">4</div>
                        <div class="rule-text">上課、午餐或午休未經允許講話、未舉手講話</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="fighting_swearing">
                        <div class="rule-number">5</div>
                        <div class="rule-text">吵架、罵髒話</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="hitting_kicking">
                        <div class="rule-number">6</div>
                        <div class="rule-text">亂踢人打人</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="incorrect_corrections">
                        <div class="rule-number">7</div>
                        <div class="rule-text">訂正不確實三次以上</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="playing_with_violators">
                        <div class="rule-number">8</div>
                        <div class="rule-text">和欠作業或反省的同學玩、講話</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="missing_homework">
                        <div class="rule-number">9</div>
                        <div class="rule-text">欠作業超過三天</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="playing_with_toys">
                        <div class="rule-number">10</div>
                        <div class="rule-text">上課玩玩具、文具</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="chatting_while_cleaning">
                        <div class="rule-number">11</div>
                        <div class="rule-text">掃地時間聊天或玩</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="talking_in_line">
                        <div class="rule-number">12</div>
                        <div class="rule-text">整隊時講話或玩</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="littering">
                        <div class="rule-number">13</div>
                        <div class="rule-text">亂丟東西</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                    <div class="rule-card" data-violation="other">
                        <div class="rule-number">14</div>
                        <div class="rule-text">其它</div>
                        <div class="rule-penalty">-5分</div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按鈕 -->
            <div class="action-buttons">
                <button id="historyBtn" class="btn btn-info" style="margin-right: 10px;">
                    <i class="fas fa-history"></i> 📊 查看違規歷史
                </button>
                <button id="resetBtn" class="btn btn-danger" style="margin-right: 10px;">
                    <i class="fas fa-redo"></i> 重置所有記錄
                </button>
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="搜尋學生...">
                </div>
            </div>
            
            <!-- 學生表格 -->
            <div class="table-container">
                <table id="disciplineTable">
                    <thead>
                        <tr>
                            <th>🐾 動物</th>
                            <th>學號</th>
                            <th>姓名</th>
                            <th>目前分數</th>
                            <th>今日違規</th>
                            <th>總違規次數</th>
                            <th>總扣分</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="disciplineTableBody">
                        <!-- 學生違規資料將由 JavaScript 動態生成 -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination" id="pagination">
                <!-- 分頁按鈕將由 JavaScript 動態生成 -->
            </div>
        </main>
    </div>

    <!-- 違規登記彈窗 -->
    <div id="violationModal" class="modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h3>⚠️ 記錄違規行為</h3>
            <p>學生：<span id="studentNameSpan" class="student-name"></span></p>
            
            <div class="violation-selection">
                <h4>請選擇違規行為：</h4>
                <div class="violation-grid">
                    <div class="violation-option" data-violation="attitude">
                        <div class="violation-number">1</div>
                        <div class="violation-text">對老師態度不佳</div>
                    </div>
                    <div class="violation-option" data-violation="talking_back">
                        <div class="violation-number">2</div>
                        <div class="violation-text">對老師幹部頂嘴</div>
                    </div>
                    <div class="violation-option" data-violation="talking_after_bell">
                        <div class="violation-number">3</div>
                        <div class="violation-text">鐘響講話</div>
                    </div>
                    <div class="violation-option" data-violation="talking_without_permission">
                        <div class="violation-number">4</div>
                        <div class="violation-text">上課、午餐或午休未經允許講話、未舉手講話</div>
                    </div>
                    <div class="violation-option" data-violation="fighting_swearing">
                        <div class="violation-number">5</div>
                        <div class="violation-text">吵架、罵髒話</div>
                    </div>
                    <div class="violation-option" data-violation="hitting_kicking">
                        <div class="violation-number">6</div>
                        <div class="violation-text">亂踢人打人</div>
                    </div>
                    <div class="violation-option" data-violation="incorrect_corrections">
                        <div class="violation-number">7</div>
                        <div class="violation-text">訂正不確實三次以上</div>
                    </div>
                    <div class="violation-option" data-violation="playing_with_violators">
                        <div class="violation-number">8</div>
                        <div class="violation-text">和欠作業或反省的同學玩、講話</div>
                    </div>
                    <div class="violation-option" data-violation="missing_homework">
                        <div class="violation-number">9</div>
                        <div class="violation-text">欠作業超過三天</div>
                    </div>
                    <div class="violation-option" data-violation="playing_with_toys">
                        <div class="violation-number">10</div>
                        <div class="violation-text">上課玩玩具、文具</div>
                    </div>
                    <div class="violation-option" data-violation="chatting_while_cleaning">
                        <div class="violation-number">11</div>
                        <div class="violation-text">掃地時間聊天或玩</div>
                    </div>
                    <div class="violation-option" data-violation="talking_in_line">
                        <div class="violation-number">12</div>
                        <div class="violation-text">整隊時講話或玩</div>
                    </div>
                    <div class="violation-option" data-violation="littering">
                        <div class="violation-number">13</div>
                        <div class="violation-text">亂丟東西</div>
                    </div>
                    <div class="violation-option" data-violation="other">
                        <div class="violation-number">14</div>
                        <div class="violation-text">其它</div>
                    </div>
                </div>
                
                <div class="custom-note" id="customNoteSection" style="display: none;">
                    <label for="customNote">請說明其它違規行為：</label>
                    <textarea id="customNote" rows="3" placeholder="請詳細說明違規行為..."></textarea>
                </div>
            </div>
            
            <div class="penalty-info">
                <div class="penalty-display">
                    <i class="fas fa-minus-circle"></i>
                    <span>扣分：<strong>5分</strong></span>
                </div>
            </div>
            
            <div class="modal-buttons">
                <button id="confirmViolationBtn" class="btn btn-danger" disabled>確認記錄</button>
                <button id="cancelViolationBtn" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 歷史記錄彈窗 -->
    <div id="historyModal" class="modal">
        <div class="modal-content extra-large">
            <span class="close">&times;</span>
            <h3>📊 違規歷史記錄</h3>
            
            <div class="history-filters">
                <div class="filter-group">
                    <label for="dateFilterStart">開始日期：</label>
                    <input type="date" id="dateFilterStart" class="form-control">
                </div>
                <div class="filter-group">
                    <label for="dateFilterEnd">結束日期：</label>
                    <input type="date" id="dateFilterEnd" class="form-control">
                </div>
                <div class="filter-group">
                    <label for="studentFilter">學生：</label>
                    <select id="studentFilter" class="form-control">
                        <option value="">所有學生</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="violationFilter">違規類型：</label>
                    <select id="violationFilter" class="form-control">
                        <option value="">所有類型</option>
                        <option value="attitude">對老師態度不佳</option>
                        <option value="talking_back">對老師幹部頂嘴</option>
                        <option value="talking_after_bell">鐘響講話</option>
                        <option value="talking_without_permission">未經允許講話</option>
                        <option value="fighting_swearing">吵架、罵髒話</option>
                        <option value="hitting_kicking">亂踢人打人</option>
                        <option value="incorrect_corrections">訂正不確實</option>
                        <option value="playing_with_violators">和違規同學玩</option>
                        <option value="missing_homework">欠作業超過三天</option>
                        <option value="playing_with_toys">上課玩玩具</option>
                        <option value="chatting_while_cleaning">掃地時聊天</option>
                        <option value="talking_in_line">整隊時講話</option>
                        <option value="littering">亂丟東西</option>
                        <option value="other">其它</option>
                    </select>
                </div>
                <div class="filter-actions">
                    <button id="applyFilterBtn" class="btn btn-primary">篩選</button>
                    <button id="clearFilterBtn" class="btn btn-secondary">清除</button>
                    <button id="downloadHistoryBtn" class="btn btn-success">下載記錄</button>
                </div>
            </div>
            
            <div class="history-stats">
                <div class="stat-item">
                    <span class="stat-label">記錄總數：</span>
                    <span class="stat-value" id="historyTotal">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">總扣分：</span>
                    <span class="stat-value" id="historyPenalty">0</span>
                </div>
            </div>
            
            <div class="history-table-container">
                <table id="historyTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>時間</th>
                            <th>學生</th>
                            <th>違規行為</th>
                            <th>說明</th>
                            <th>扣分</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="historyTableBody">
                        <!-- 歷史記錄將由 JavaScript 動態生成 -->
                    </tbody>
                </table>
            </div>
            
            <div class="history-pagination" id="historyPagination">
                <!-- 分頁將由 JavaScript 動態生成 -->
            </div>
        </div>
    </div>

    <!-- 編輯違規記錄彈窗 -->
    <div id="editViolationModal" class="modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h3>✏️ 編輯違規記錄</h3>
            <p>學生：<span id="editStudentNameSpan" class="student-name"></span></p>
            
            <div class="violation-selection">
                <h4>修改違規行為：</h4>
                <div class="violation-grid">
                    <div class="edit-violation-option" data-violation="attitude">
                        <div class="violation-number">1</div>
                        <div class="violation-text">對老師態度不佳</div>
                    </div>
                    <div class="edit-violation-option" data-violation="talking_back">
                        <div class="violation-number">2</div>
                        <div class="violation-text">對老師幹部頂嘴</div>
                    </div>
                    <div class="edit-violation-option" data-violation="talking_after_bell">
                        <div class="violation-number">3</div>
                        <div class="violation-text">鐘響講話</div>
                    </div>
                    <div class="edit-violation-option" data-violation="talking_without_permission">
                        <div class="violation-number">4</div>
                        <div class="violation-text">上課、午餐或午休未經允許講話、未舉手講話</div>
                    </div>
                    <div class="edit-violation-option" data-violation="fighting_swearing">
                        <div class="violation-number">5</div>
                        <div class="violation-text">吵架、罵髒話</div>
                    </div>
                    <div class="edit-violation-option" data-violation="hitting_kicking">
                        <div class="violation-number">6</div>
                        <div class="violation-text">亂踢人打人</div>
                    </div>
                    <div class="edit-violation-option" data-violation="incorrect_corrections">
                        <div class="violation-number">7</div>
                        <div class="violation-text">訂正不確實三次以上</div>
                    </div>
                    <div class="edit-violation-option" data-violation="playing_with_violators">
                        <div class="violation-number">8</div>
                        <div class="violation-text">和欠作業或反省的同學玩、講話</div>
                    </div>
                    <div class="edit-violation-option" data-violation="missing_homework">
                        <div class="violation-number">9</div>
                        <div class="violation-text">欠作業超過三天</div>
                    </div>
                    <div class="edit-violation-option" data-violation="playing_with_toys">
                        <div class="violation-number">10</div>
                        <div class="violation-text">上課玩玩具、文具</div>
                    </div>
                    <div class="edit-violation-option" data-violation="chatting_while_cleaning">
                        <div class="violation-number">11</div>
                        <div class="violation-text">掃地時間聊天或玩</div>
                    </div>
                    <div class="edit-violation-option" data-violation="talking_in_line">
                        <div class="violation-number">12</div>
                        <div class="violation-text">整隊時講話或玩</div>
                    </div>
                    <div class="edit-violation-option" data-violation="littering">
                        <div class="violation-number">13</div>
                        <div class="violation-text">亂丟東西</div>
                    </div>
                    <div class="edit-violation-option" data-violation="other">
                        <div class="violation-number">14</div>
                        <div class="violation-text">其它</div>
                    </div>
                </div>
                
                <div class="custom-note" id="editCustomNoteSection" style="display: none;">
                    <label for="editCustomNote">請說明其它違規行為：</label>
                    <textarea id="editCustomNote" rows="3" placeholder="請詳細說明違規行為..."></textarea>
                </div>
            </div>
            
            <div class="penalty-info">
                <div class="penalty-display">
                    <i class="fas fa-minus-circle"></i>
                    <span>扣分：<strong>5分</strong></span>
                </div>
            </div>
            
            <div class="modal-buttons">
                <button id="confirmEditViolationBtn" class="btn btn-primary" disabled>確認修改</button>
                <button id="cancelEditViolationBtn" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 匯出格式選擇彈窗 -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <span class="close" id="exportModalClose">&times;</span>
            <h3>📊 選擇匯出格式</h3>
            <div class="export-options">
                <div class="export-option" data-format="json">
                    <div class="export-icon">📄</div>
                    <h4>JSON 格式</h4>
                    <p>完整資料結構，適合程式處理</p>
                    <button class="btn btn-primary">選擇 JSON</button>
                </div>
                <div class="export-option" data-format="csv">
                    <div class="export-icon">📊</div>
                    <h4>CSV 格式</h4>
                    <p>表格格式，適合 Excel 開啟</p>
                    <button class="btn btn-primary">選擇 CSV</button>
                </div>
                <div class="export-option" data-format="excel">
                    <div class="export-icon">📈</div>
                    <h4>Excel 格式</h4>
                    <p>完整 Excel 檔案，包含格式設定</p>
                    <button class="btn btn-primary">選擇 Excel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 違規記錄匯出格式選擇彈窗 -->
    <div id="exportHistoryModal" class="modal">
        <div class="modal-content">
            <span class="close" id="exportHistoryModalClose">&times;</span>
            <h3>📊 選擇違規記錄匯出格式</h3>
            <div class="export-options">
                <div class="export-option" data-format="json">
                    <div class="export-icon">📄</div>
                    <h4>JSON 格式</h4>
                    <p>完整資料結構，適合程式處理</p>
                    <button class="btn btn-primary">選擇 JSON</button>
                </div>
                <div class="export-option" data-format="csv">
                    <div class="export-icon">📊</div>
                    <h4>CSV 格式</h4>
                    <p>表格格式，適合 Excel 開啟</p>
                    <button class="btn btn-primary">選擇 CSV</button>
                </div>
                <div class="export-option" data-format="excel">
                    <div class="export-icon">📈</div>
                    <h4>Excel 格式</h4>
                    <p>完整 Excel 檔案，包含格式設定</p>
                    <button class="btn btn-primary">選擇 Excel</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="js/animal-config.js"></script>
    <script src="js/discipline.js"></script>
</body>
</html>