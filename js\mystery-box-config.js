// 神祕寶箱配置文件
// Mystery Box Configuration

// 神祕寶箱全局設定
const MYSTERY_BOX_CONFIG = {
    // 是否啟用神祕寶箱功能
    enabled: true,
    
    // 觸發條件設定
    trigger: {
        // 最小加分數才觸發神祕寶箱（設為0表示任何加分都觸發）
        minScoreIncrease: 1,
        
        // 是否在批量操作中觸發
        enableInBatchOperation: true,
        
        // 觸發機率（0-1之間，1表示100%觸發）
        triggerProbability: 1.0
    },
    
    // 事件機率設定（總和應為1.0）
    eventProbabilities: {
        lucky: 0.23,    // 強運：23%
        steal: 0.19,    // 掠奪：19%
        angel: 0.16,    // 天使：16%
        devil: 0.05,    // 惡魔：5%
        swap: 0.14,     // 交換：14%
        circus: 0.08,   // 馬戲團：8%
        meteor: 0.09,   // 流星雨：9%
        transform: 0.06 // 變身：6%
    },
    
    // 事件效果設定
    effects: {
        // 掠奪事件偷取的分數
        stealAmount: 5,
        
        // 強運事件的倍數（2表示翻倍）
        luckyMultiplier: 2,
        
        // 惡魔事件是否使用無條件捨去（true）還是四捨五入（false）
        devilUseFloor: true
    },
    
    // 視覺效果設定
    visual: {
        // 是否啟用動畫效果
        enableAnimations: true,
        
        // 動畫持續時間（毫秒）
        animationDuration: 2000,
        
        // 是否啟用粒子特效
        enableParticleEffects: true,
        
        // 是否啟用震動效果（支援的設備）
        enableVibration: true
    },
    
    // 音效設定
    audio: {
        // 是否啟用音效
        enabled: false,
        
        // 音效文件路徑
        sounds: {
            boxOpen: 'sounds/box-open.mp3',
            result: 'sounds/result.mp3',
            lucky: 'sounds/lucky.mp3',
            steal: 'sounds/steal.mp3',
            angel: 'sounds/angel.mp3',
            devil: 'sounds/devil.mp3',
            swap: 'sounds/swap.mp3'
        },
        
        // 音量設定（0-1之間）
        volume: 0.5
    },
    
    // 特殊狀態設定
    specialStates: {
        // 天使保護是否可以疊加
        angelProtectionStackable: false,
        
        // 天使保護的最大數量
        maxAngelProtection: 1,
        
        // 是否在學生卡片上顯示特殊狀態
        showStatusOnCard: true
    },
    
    // 記錄設定
    logging: {
        // 是否記錄到操作歷史
        enableHistory: true,
        
        // 是否記錄詳細的事件資訊
        detailedLogging: true,
        
        // 是否在控制台輸出調試資訊
        consoleDebug: false
    }
};

// 獲取配置值的輔助函數
function getMysteryBoxConfig(path) {
    const keys = path.split('.');
    let value = MYSTERY_BOX_CONFIG;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return undefined;
        }
    }
    
    return value;
}

// 設定配置值的輔助函數
function setMysteryBoxConfig(path, newValue) {
    const keys = path.split('.');
    let obj = MYSTERY_BOX_CONFIG;
    
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!(key in obj) || typeof obj[key] !== 'object') {
            obj[key] = {};
        }
        obj = obj[key];
    }
    
    obj[keys[keys.length - 1]] = newValue;
    
    // 保存到本地存儲
    localStorage.setItem('mysteryBoxConfig', JSON.stringify(MYSTERY_BOX_CONFIG));
}

// 從本地存儲載入配置
function loadMysteryBoxConfig() {
    try {
        const saved = localStorage.getItem('mysteryBoxConfig');
        if (saved) {
            const savedConfig = JSON.parse(saved);
            // 合併配置，保留預設值
            Object.assign(MYSTERY_BOX_CONFIG, savedConfig);
        }
    } catch (error) {
        console.warn('載入神祕寶箱配置失敗:', error);
    }
}

// 重置配置到預設值
function resetMysteryBoxConfig() {
    localStorage.removeItem('mysteryBoxConfig');
    location.reload(); // 重新載入頁面以應用預設配置
}

// 驗證事件機率總和
function validateEventProbabilities() {
    const probs = MYSTERY_BOX_CONFIG.eventProbabilities;
    const total = Object.values(probs).reduce((sum, prob) => sum + prob, 0);
    
    if (Math.abs(total - 1.0) > 0.001) {
        console.warn('警告：事件機率總和不等於1.0，當前總和:', total);
        return false;
    }
    
    return true;
}

// 根據機率選擇事件
function selectEventByProbability() {
    if (!validateEventProbabilities()) {
        // 如果機率設定有問題，使用平均機率
        const events = Object.keys(MYSTERY_BOX_CONFIG.eventProbabilities);
        return events[Math.floor(Math.random() * events.length)];
    }
    
    const random = Math.random();
    let cumulative = 0;
    
    for (const [event, probability] of Object.entries(MYSTERY_BOX_CONFIG.eventProbabilities)) {
        cumulative += probability;
        if (random <= cumulative) {
            return event;
        }
    }
    
    // 備用方案
    return 'lucky';
}

// 檢查是否應該觸發神祕寶箱
function shouldTriggerMysteryBox(scoreIncrease, isBatchOperation = false) {
    // 檢查功能是否啟用
    if (!getMysteryBoxConfig('enabled')) {
        return false;
    }
    
    // 檢查最小加分要求
    if (scoreIncrease < getMysteryBoxConfig('trigger.minScoreIncrease')) {
        return false;
    }
    
    // 檢查批量操作設定
    if (isBatchOperation && !getMysteryBoxConfig('trigger.enableInBatchOperation')) {
        return false;
    }
    
    // 檢查觸發機率
    const probability = getMysteryBoxConfig('trigger.triggerProbability');
    if (Math.random() > probability) {
        return false;
    }
    
    return true;
}

// 播放音效（如果啟用）
function playMysteryBoxSound(soundType) {
    if (!getMysteryBoxConfig('audio.enabled')) {
        return;
    }
    
    try {
        const soundPath = getMysteryBoxConfig(`audio.sounds.${soundType}`);
        const volume = getMysteryBoxConfig('audio.volume');
        
        if (soundPath) {
            const audio = new Audio(soundPath);
            audio.volume = volume;
            audio.play().catch(error => {
                if (getMysteryBoxConfig('logging.consoleDebug')) {
                    console.log('音效播放失敗:', error);
                }
            });
        }
    } catch (error) {
        if (getMysteryBoxConfig('logging.consoleDebug')) {
            console.log('音效播放錯誤:', error);
        }
    }
}

// 觸發震動效果（如果支援且啟用）
function triggerVibration(pattern = [200]) {
    if (!getMysteryBoxConfig('visual.enableVibration')) {
        return;
    }
    
    if ('vibrate' in navigator) {
        navigator.vibrate(pattern);
    }
}

// 記錄調試資訊
function debugLog(message, ...args) {
    if (getMysteryBoxConfig('logging.consoleDebug')) {
        console.log('[神祕寶箱]', message, ...args);
    }
}

// 頁面載入時初始化配置
document.addEventListener('DOMContentLoaded', function() {
    loadMysteryBoxConfig();
    validateEventProbabilities();
    debugLog('神祕寶箱配置已載入', MYSTERY_BOX_CONFIG);
});

// 匯出配置對象（如果使用模組系統）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MYSTERY_BOX_CONFIG,
        getMysteryBoxConfig,
        setMysteryBoxConfig,
        loadMysteryBoxConfig,
        resetMysteryBoxConfig,
        selectEventByProbability,
        shouldTriggerMysteryBox,
        playMysteryBoxSound,
        triggerVibration,
        debugLog
    };
}
