/* 幹部工作頁面專用樣式 */

/* 主標題區域 */
.main-content h2 {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(255, 215, 0, 0.1);
}

.subtitle {
    text-align: center;
    color: #FF8C00;
    font-size: 1.1em;
    margin-bottom: 30px;
    font-weight: 500;
}

/* 統計卡片網格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-medium);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
    border: 2px solid transparent;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
}

.stat-card.excellent {
    border-color: #FFD700;
    background: linear-gradient(135deg, #FFFACD 0%, #FFF8DC 100%);
}

.stat-card.good {
    border-color: #4169E1;
    background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 100%);
}

.stat-card.total {
    border-color: #FF69B4;
    background: linear-gradient(135deg, #FFF0F5 0%, #FFE4E1 100%);
}

.stat-icon {
    font-size: 2.5em;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 1.1em;
    color: var(--dark-color);
}

.stat-number {
    font-size: 2em;
    font-weight: 700;
    color: var(--primary-color);
    margin: 5px 0;
}

.stat-content p {
    margin: 0;
    font-size: 0.9em;
    color: #666;
}

/* 本月統計區域 */
.monthly-stats {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    border: 2px solid #FFD700;
    box-shadow: var(--shadow-medium);
}

.monthly-stats h3 {
    color: #FF8C00;
    margin-bottom: 20px;
    text-align: center;
}

.monthly-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-item {
    background: linear-gradient(135deg, #FFFACD 0%, #FFF8DC 100%);
    padding: 15px;
    border-radius: var(--border-radius-small);
    text-align: center;
    border: 1px solid #FFD700;
}

.summary-item strong {
    color: #FF8C00;
    font-size: 1.1em;
}

/* 學生網格 */
.students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.student-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.student-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #FFD700, #FFA500, #FF8C00);
}

.student-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
    border-color: #FFD700;
}

.student-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.student-avatar {
    font-size: 2em;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #FFFACD 0%, #FFF8DC 100%);
    border-radius: 50%;
    border: 2px solid #FFD700;
}

.student-info h4 {
    margin: 0 0 5px 0;
    color: var(--dark-color);
    font-size: 1.1em;
}

.student-number {
    color: #666;
    font-size: 0.9em;
}

.student-actions {
    display: flex;
    gap: 10px;
}

.btn-excellent {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
    border: none;
    flex: 1;
}

.btn-excellent:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
    transform: translateY(-2px);
}

.btn-good {
    background: linear-gradient(135deg, #4169E1 0%, #1E90FF 100%);
    color: white;
    border: none;
    flex: 1;
}

.btn-good:hover {
    background: linear-gradient(135deg, #1E90FF 0%, #0000FF 100%);
    transform: translateY(-2px);
}

/* 今日記錄區域 */
.daily-records {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 25px;
    border: 2px solid #FFD700;
    box-shadow: var(--shadow-medium);
}

.daily-records h3 {
    color: #FF8C00;
    margin-bottom: 20px;
    text-align: center;
}

.records-table {
    overflow-x: auto;
}

.records-table table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.records-table th,
.records-table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #E0E0E0;
}

.records-table th {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.records-table tr:hover {
    background: linear-gradient(135deg, #FFFACD 0%, #FFF8DC 100%);
}

.performance-badge {
    padding: 5px 12px;
    border-radius: var(--border-radius-small);
    font-size: 0.85em;
    font-weight: 600;
    display: inline-block;
}

.performance-badge.excellent {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
}

.performance-badge.good {
    background: linear-gradient(135deg, #4169E1 0%, #1E90FF 100%);
    color: white;
}

.score-badge {
    background: var(--gradient-pink);
    color: white;
    padding: 4px 8px;
    border-radius: var(--border-radius-small);
    font-weight: 600;
}

/* 修改記錄彈窗樣式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-background);
    margin: 5% auto;
    padding: 30px;
    border-radius: var(--border-radius-large);
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow-strong);
    position: relative;
    border: 2px solid #FFD700;
}

.modal-content h3 {
    color: #FF8C00;
    text-align: center;
    margin-bottom: 25px;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: var(--danger-color);
}

.student-info {
    background: linear-gradient(135deg, #FFFACD 0%, #FFF8DC 100%);
    padding: 15px;
    border-radius: var(--border-radius-small);
    border: 1px solid #FFD700;
    text-align: center;
    margin-bottom: 20px;
}

.performance-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.performance-option {
    display: block;
    cursor: pointer;
    border: 2px solid #E0E0E0;
    border-radius: var(--border-radius-small);
    padding: 15px;
    transition: var(--transition);
    background: var(--card-background);
}

.performance-option:hover {
    border-color: #FFD700;
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

.performance-option input[type="radio"] {
    display: none;
}

.performance-option.excellent:has(input:checked) {
    border-color: #FFD700;
    background: linear-gradient(135deg, #FFFACD 0%, #FFF8DC 100%);
}

.performance-option.good:has(input:checked) {
    border-color: #4169E1;
    background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 100%);
}

.option-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.option-icon {
    font-size: 1.5em;
}

.option-text strong {
    display: block;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.option-text small {
    color: #666;
    font-size: 0.9em;
}

.form-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 25px;
}

.form-buttons .btn {
    flex: 1;
    max-width: 140px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .students-grid {
        grid-template-columns: 1fr;
    }
    
    .monthly-summary {
        grid-template-columns: 1fr;
    }
    
    .student-actions {
        flex-direction: column;
    }
    
    .form-buttons {
        flex-direction: column;
    }
    
    .form-buttons .btn {
        max-width: none;
    }
}

@media (max-width: 480px) {
    .modal-content {
        margin: 2% auto;
        padding: 20px;
        width: 95%;
    }
    
    .records-table {
        font-size: 0.9em;
    }
    
    .records-table th,
    .records-table td {
        padding: 8px 4px;
    }
}