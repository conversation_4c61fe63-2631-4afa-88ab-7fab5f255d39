/* 糖果風格基礎樣式 */
:root {
    /* 糖果色調主色系 */
    --candy-pink: #FFB6C1;          /* 粉色糖果 */
    --candy-mint: #98FB98;          /* 薄荷綠 */
    --candy-sky: #87CEEB;           /* 天空藍 */
    --candy-lemon: #FFFFE0;         /* 檸檬黃 */
    --candy-lavender: #E6E6FA;      /* 薰衣草紫 */
    --candy-peach: #FFDAB9;         /* 蜜桃色 */
    --candy-cream: #FFF8DC;         /* 奶油白 */
    
    /* 深度糖果色（用於文字和邊框） */
    --candy-pink-deep: #FF69B4;
    --candy-mint-deep: #00FA9A;
    --candy-sky-deep: #4169E1;
    --candy-lemon-deep: #FFD700;
    --candy-lavender-deep: #9370DB;
    --candy-peach-deep: #FF7F50;
    
    /* 兼容原有變數名稱 */
    --primary-color: var(--candy-pink-deep);
    --secondary-color: var(--candy-lavender);
    --success-color: var(--candy-mint-deep);
    --danger-color: #ff7675;
    --warning-color: var(--candy-lemon-deep);
    --light-color: var(--candy-cream);
    --dark-color: #4A4A4A;
    
    /* 背景色系 */
    --background: #FFFEF7;           /* 溫暖米白 */
    --card-background: #FFFFFF;      /* 純白卡片 */
    --soft-gray: #F5F5F5;          /* 柔和灰 */
    
    /* 圓角設定 - 氣泡感 */
    --border-radius: 20px;
    --border-radius-small: 12px;
    --border-radius-large: 28px;
    --border-radius-xl: 40px;
    
    /* 陰影設定 - 柔和立體感 */
    --box-shadow: 0 4px 20px rgba(255, 182, 193, 0.15);
    --shadow-soft: 0 4px 20px rgba(255, 182, 193, 0.15);
    --shadow-medium: 0 8px 30px rgba(255, 182, 193, 0.2);
    --shadow-strong: 0 12px 40px rgba(255, 182, 193, 0.25);
    --shadow-glow: 0 0 20px rgba(255, 182, 193, 0.3);
    
    /* 動畫時間 */
    --transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-fast: 0.2s ease-out;
    --transition-medium: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
    
    /* 漸變色系 */
    --gradient-pink: linear-gradient(135deg, var(--candy-pink) 0%, var(--candy-peach) 100%);
    --gradient-mint: linear-gradient(135deg, var(--candy-mint) 0%, var(--candy-sky) 100%);
    --gradient-lemon: linear-gradient(135deg, var(--candy-lemon) 0%, var(--candy-cream) 100%);
    --gradient-lavender: linear-gradient(135deg, var(--candy-lavender) 0%, var(--candy-pink) 100%);
    --gradient-background: linear-gradient(135deg, var(--background) 0%, var(--candy-cream) 50%, var(--candy-lavender) 100%);
}

/* 全域基礎樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Nunito', 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: var(--gradient-background);
    min-height: 100vh;
    color: var(--dark-color);
    position: relative;
    overflow-x: hidden;
}

/* 浮動氣泡背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(255, 182, 193, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(152, 251, 152, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(135, 206, 235, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 容器 */
.container {
    display: flex;
    min-height: 100vh;
}

/* 側邊欄 */
.sidebar {
    width: 250px;
    background: var(--card-background);
    padding: 20px;
    box-shadow: var(--shadow-medium);
    position: relative;
    z-index: 10;
    border-radius: 0 var(--border-radius-large) var(--border-radius-large) 0;
    border: 2px solid var(--candy-pink);
}

.sidebar h1 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
    font-weight: 700;
    padding-bottom: 15px;
    border-bottom: 3px solid var(--candy-pink);
    background: var(--gradient-pink);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.sidebar h1::after {
    content: '🍭';
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    animation: wiggle-cute 2s ease-in-out infinite alternate;
}

.sidebar nav ul {
    list-style: none;
}

.sidebar nav li {
    margin-bottom: 12px;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    border: 2px solid transparent;
}

.sidebar nav li:hover {
    background: var(--gradient-lavender);
    border-color: var(--candy-lavender-deep);
    transform: translateX(5px);
    box-shadow: var(--shadow-soft);
}

.sidebar nav li.active {
    background: var(--gradient-pink);
    border-color: var(--candy-pink-deep);
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

.sidebar nav li.active a {
    color: white;
    font-weight: 700;
}

.sidebar nav a {
    display: block;
    padding: 15px 18px;
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border-radius: var(--border-radius-small);
}

.sidebar nav a i {
    width: 24px;
    text-align: center;
    margin-right: 12px;
    font-size: 1.1rem;
}

.data-management {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid var(--candy-mint);
    background: var(--gradient-lemon);
    border-radius: var(--border-radius);
    padding: 20px;
}

.data-management h3 {
    font-size: 1.1rem;
    color: var(--candy-lemon-deep);
    margin-bottom: 15px;
    font-weight: 700;
    text-align: center;
}

/* 主要內容區 */
.main-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.main-content h2 {
    color: var(--primary-color);
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 糖果風格按鈕樣式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius-xl);
    background: var(--gradient-lavender);
    color: white;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    box-shadow: var(--shadow-soft);
    position: relative;
    overflow: hidden;
    font-family: inherit;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-glow);
}

.btn:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.1s ease-out;
}

.btn i {
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-pink);
    border: 2px solid var(--candy-pink-deep);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--candy-pink-deep) 0%, var(--candy-peach-deep) 100%);
    border-color: var(--candy-peach-deep);
}

.btn-secondary {
    background: var(--gradient-mint);
    color: var(--dark-color);
    border: 2px solid var(--candy-mint-deep);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--candy-mint-deep) 0%, var(--candy-sky-deep) 100%);
    color: white;
    border-color: var(--candy-sky-deep);
}

.btn-success {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
    border: 2px solid #00b894;
}

.btn-success:hover {
    background: linear-gradient(135deg, #00a085 0%, #00b7b8 100%);
    border-color: #00a085;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);
}

.btn-success:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.2);
}

.btn-danger {
    background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
    color: white;
    border: 2px solid #ff7675;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #e74c3c 0%, #e84393 100%);
    border-color: #e74c3c;
}

/* 糖果風格表格樣式 */
.table-container {
    background: var(--card-background);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    margin-top: 25px;
    border: 3px solid var(--candy-mint);
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 18px;
    text-align: left;
    border-bottom: 2px solid var(--candy-cream);
    transition: var(--transition-fast);
}

th {
    background: var(--gradient-mint);
    font-weight: 700;
    color: var(--dark-color);
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

tbody tr {
    transition: var(--transition-fast);
}

tbody tr:hover {
    background: var(--gradient-lemon);
    transform: scale(1.01);
    box-shadow: inset 0 0 10px rgba(255, 215, 0, 0.2);
}

tbody tr:nth-child(even) {
    background: rgba(255, 182, 193, 0.05);
}

tbody tr:nth-child(even):hover {
    background: var(--gradient-lemon);
}

/* 排序功能樣式 */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: all 0.2s ease;
}

.sortable:hover {
    background-color: #e9ecef;
    color: var(--primary-color);
}

.sort-icon {
    margin-left: 8px;
    font-size: 0.8rem;
    opacity: 0.6;
    transition: all 0.2s ease;
}

.sortable:hover .sort-icon {
    opacity: 1;
}

.sortable.sort-asc .sort-icon {
    opacity: 1;
    color: var(--primary-color);
}

.sortable.sort-desc .sort-icon {
    opacity: 1;
    color: var(--primary-color);
}

/* 排序提示 */
.sortable {
    position: relative;
}

.sortable::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(108, 92, 231, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
}

.sortable:hover::before {
    opacity: 1;
}

tbody tr:hover {
    background-color: #f8f9fa;
}

/* 移除重複的 .score 樣式定義，使用下方更完整的定義 */

.action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px;
}

.reset-score-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reset-score-section input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 0.9rem;
    width: 120px;
    text-align: center;
    transition: var(--transition);
}

.reset-score-section input:focus {
    border-color: var(--danger-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 118, 117, 0.2);
}

/* 糖果風格彈窗樣式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 182, 193, 0.3);
    backdrop-filter: blur(5px);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    animation: modal-fade-in 0.3s ease-out;
}

.modal-content {
    background: var(--card-background);
    padding: 40px;
    border-radius: var(--border-radius-large);
    width: 100%;
    max-width: 500px;
    position: relative;
    box-shadow: var(--shadow-strong);
    border: 3px solid var(--candy-pink);
    animation: modal-bounce-in 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    cursor: pointer;
    color: var(--candy-pink-deep);
    transition: var(--transition-fast);
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--gradient-pink);
}

.close:hover {
    transform: rotate(90deg) scale(1.1);
    box-shadow: var(--shadow-glow);
}

/* 匯出模態框特殊樣式 */
#exportModal .modal-content {
    max-width: 700px;
    padding: 40px;
}

#exportModal h3 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
}

.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.export-option {
    background: var(--card-background);
    border: 2px solid var(--candy-lavender);
    border-radius: var(--border-radius);
    padding: 25px 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.export-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-lavender);
    transition: var(--transition);
    z-index: -1;
}

.export-option:hover::before {
    left: 0;
}

.export-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.export-option .export-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    transition: var(--transition);
}

.export-option:hover .export-icon {
    transform: scale(1.1);
}

.export-option h4 {
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.export-option p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 20px;
    line-height: 1.4;
}

.export-option .btn {
    width: 100%;
    padding: 10px;
    font-size: 0.9rem;
    border-radius: var(--border-radius-small);
}

/* 表單樣式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.2);
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
}

/* 糖果風格動畫效果 */
@keyframes wiggle-cute {
    0%, 100% { transform: rotate(-3deg); }
    50% { transform: rotate(3deg); }
}

@keyframes modal-fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modal-bounce-in {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    70% {
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce-gentle {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes float-up {
    from {
        transform: translateY(100vh) translateX(0);
        opacity: 0.3;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    to {
        transform: translateY(-20vh) translateX(50px);
        opacity: 0;
    }
}

@keyframes scale-breathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes gentle-glow {
    0% { box-shadow: var(--shadow-medium); }
    100% { box-shadow: var(--shadow-glow); }
}

/* 動物選擇器樣式 */
.animal-selection {
    margin-bottom: 15px;
}

.animal-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.animal-option {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    border: 3px solid var(--candy-cream);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    background: var(--gradient-lavender);
    box-shadow: var(--shadow-soft);
}

.animal-option:hover {
    transform: scale(1.1) rotate(5deg);
    border-color: var(--candy-pink-deep);
    box-shadow: var(--shadow-glow);
}

.animal-option.selected {
    border-color: var(--candy-pink-deep);
    background: var(--gradient-pink);
    transform: scale(1.15);
    box-shadow: var(--shadow-glow);
    animation: bounce-gentle 0.5s ease-out;
}

/* 分數顯示樣式 - 移除透明文字效果 */
.score {
    font-weight: 700;
    /* 移除透明文字效果，改用普通文字顏色 */
    color: var(--primary-color);
    text-align: center;
    font-size: 1.1rem;
}

/* 動物頭像在表格中的樣式 */
.student-avatar {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    border: 2px solid var(--candy-pink);
    border-radius: 50%;
    background: var(--gradient-lavender);
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    margin: 0 auto;
}

.student-avatar:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-glow);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        padding: 15px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .reset-score-section {
        justify-content: center;
        margin-top: 10px;
    }

    .reset-score-section input {
        width: 150px;
    }

    .btn {
        width: 100%;
    }
}

/* 動物選擇樣式 */
.animal-selection {
    margin: 15px 0;
}

.animal-selection label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 1.1em;
}

.animal-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    margin-top: 10px;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    border: 2px solid var(--candy-pink);
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, var(--candy-cream) 0%, var(--candy-lavender) 100%);
    box-shadow: var(--shadow-medium);
}

/* 自定義滾動條 */
.animal-grid::-webkit-scrollbar {
    width: 8px;
}

.animal-grid::-webkit-scrollbar-track {
    background: var(--soft-gray);
    border-radius: 4px;
}

.animal-grid::-webkit-scrollbar-thumb {
    background: var(--candy-pink);
    border-radius: 4px;
}

.animal-grid::-webkit-scrollbar-thumb:hover {
    background: var(--candy-pink-deep);
}

.animal-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    font-size: 22px;
    border: 2px solid var(--soft-gray);
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition);
    background: var(--card-background);
    box-shadow: var(--shadow-soft);
    position: relative;
}

.animal-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.animal-option.selected {
    border-color: var(--primary-color);
    background: var(--gradient-pink);
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

.animal-cell {
    font-size: 24px;
    text-align: center;
    vertical-align: middle;
    width: 60px;
}

/* 響應式設計 - 動物選擇 */
@media (max-width: 768px) {
    .animal-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 6px;
    }
    
    .animal-option {
        width: 38px;
        height: 38px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .animal-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 5px;
        max-height: 300px;
    }
    
    .animal-option {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    .animal-cell {
        font-size: 18px;
        width: 45px;
    }
}

/* ===== 增強糖果風格設計 ===== */

/* 按鈕美化 - 糖果風格 */
.btn {
    position: relative;
    overflow: hidden;
    border: none;
    border-radius: var(--border-radius);
    padding: 12px 24px;
    font-size: 0.95rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
    background: linear-gradient(135deg, var(--candy-pink), var(--candy-pink-deep));
    color: white;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-medium);
}

.btn-primary {
    background: linear-gradient(135deg, var(--candy-pink), var(--candy-pink-deep));
    border: 2px solid var(--candy-pink-deep);
}

.btn-success {
    background: linear-gradient(135deg, var(--candy-mint), var(--candy-mint-deep));
    border: 2px solid var(--candy-mint-deep);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--candy-lavender), var(--candy-lavender-deep));
    border: 2px solid var(--candy-lavender-deep);
}

.btn-danger {
    background: linear-gradient(135deg, #ff7675, #e17055);
    border: 2px solid #e17055;
}

/* 卡片美化 - 糖果風格 */
.summary-card,
.statistics-card,
.student-card {
    background: linear-gradient(135deg, var(--candy-cream), white);
    border: 3px solid var(--candy-pink);
    border-radius: var(--border-radius-large);
    padding: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.summary-card::before,
.statistics-card::before,
.student-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,182,193,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
    transition: var(--transition);
}

.summary-card:hover,
.statistics-card:hover,
.student-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-glow);
    border-color: var(--candy-pink-deep);
}

.summary-card:hover::before,
.statistics-card:hover::before,
.student-card:hover::before {
    background: radial-gradient(circle, rgba(255,182,193,0.2) 0%, transparent 70%);
}

/* 表格美化 - 糖果風格 */
table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    background: linear-gradient(135deg, var(--candy-cream), white);
}

table thead th {
    background: linear-gradient(135deg, var(--candy-pink), var(--candy-pink-deep));
    color: white;
    padding: 15px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    border-bottom: 3px solid var(--candy-pink-deep);
}

table tbody tr {
    transition: var(--transition);
}

table tbody tr:nth-child(even) {
    background: linear-gradient(135deg, var(--candy-lemon), var(--candy-cream));
}

table tbody tr:nth-child(odd) {
    background: linear-gradient(135deg, var(--candy-mint), var(--candy-cream));
}

table tbody tr:hover {
    background: linear-gradient(135deg, var(--candy-peach), var(--candy-lemon));
    transform: scale(1.01);
    box-shadow: inset 0 0 20px rgba(255,182,193,0.3);
}

table tbody td {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255,182,193,0.3);
}

/* 輸入框美化 - 糖果風格 */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
select,
textarea {
    border: 3px solid var(--candy-pink);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    font-size: 1rem;
    background: linear-gradient(135deg, var(--candy-cream), white);
    transition: var(--transition);
    box-shadow: inset 0 2px 8px rgba(255,182,193,0.1);
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--candy-pink-deep);
    box-shadow: 0 0 20px rgba(255,182,193,0.4), inset 0 2px 8px rgba(255,182,193,0.2);
    background: linear-gradient(135deg, white, var(--candy-lemon));
    transform: scale(1.02);
}

/* 糖果主題標題美化 */
h1, h2, h3, h4, h5, h6 {
    color: var(--dark-color);
    text-shadow: 2px 2px 4px rgba(255,182,193,0.3);
    position: relative;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--candy-pink), var(--candy-mint), var(--candy-lemon));
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(255,182,193,0.3);
}

/* 溫馨背景圖案 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 50%, rgba(255,182,193,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(152,251,152,0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(135,206,235,0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* ===== 可愛動物頭像美化樣式 ===== */

/* 標題動物圖標 */
.title-decoration {
    display: inline-block;
    margin-left: 15px;
}

.title-animal-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin: 0 5px;
    box-shadow: 0 3px 10px rgba(255, 182, 193, 0.3);
    transition: var(--transition);
    animation: titleAnimalBounce 2s ease-in-out infinite;
}

.title-animal-icon:hover {
    transform: scale(1.2) rotate(10deg);
    box-shadow: 0 5px 20px rgba(255, 182, 193, 0.5);
}

.title-animal-icon:nth-child(1) {
    animation-delay: 0s;
}

.title-animal-icon:nth-child(2) {
    animation-delay: 1s;
}

@keyframes titleAnimalBounce {
    0%, 100% { 
        transform: translateY(0px) scale(1); 
    }
    50% { 
        transform: translateY(-5px) scale(1.05); 
    }
}

/* 側邊欄美化 */
.sidebar h1 {
    position: relative;
    overflow: visible;
}

.sidebar h1::after {
    content: '🦊 🐻 🐰';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 0.8rem;
    opacity: 0.6;
    animation: sidebarFloat 4s ease-in-out infinite;
}

@keyframes sidebarFloat {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.6;
    }
    50% { 
        transform: translateY(-8px) rotate(5deg); 
        opacity: 0.8;
    }
}

/* 動物頭像容器 */
.student-animal-avatar {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    background: linear-gradient(135deg, var(--candy-cream), var(--candy-lemon));
    border: 3px solid var(--candy-pink);
}

.student-animal-avatar:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-glow);
}

.animal-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.animal-emoji {
    position: absolute;
    bottom: -5px;
    right: -5px;
    background: var(--candy-pink);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.student-animal-emoji {
    font-size: 2rem;
    text-align: center;
    padding: 5px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--candy-cream), var(--candy-lemon));
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    border: 3px solid var(--candy-mint);
}

.student-animal-emoji:hover {
    transform: scale(1.1) rotate(-5deg);
    box-shadow: var(--shadow-glow);
}

/* 學生姓名美化 */
.student-name-cell {
    position: relative;
}

.student-name {
    font-weight: 600;
    color: var(--dark-color);
    padding: 5px 12px;
    background: linear-gradient(135deg, var(--candy-cream), var(--candy-peach));
    border-radius: var(--border-radius-small);
    border: 2px solid var(--candy-peach-deep);
    display: inline-block;
    transition: var(--transition);
    box-shadow: 0 2px 8px rgba(255, 127, 80, 0.1);
}

.student-name:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 127, 80, 0.2);
    background: linear-gradient(135deg, var(--candy-peach), var(--candy-lemon));
}

/* 分數顯示美化 */
.score {
    font-weight: bold;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
    color: var(--dark-color); /* 添加默認文字顏色 */
    background: linear-gradient(135deg, var(--candy-cream), white); /* 添加默認背景 */
}

.score.high-score {
    background: linear-gradient(135deg, var(--candy-mint), var(--candy-mint-deep));
    color: white;
    border: 2px solid var(--candy-mint-deep);
}

.score.medium-score {
    background: linear-gradient(135deg, var(--candy-lemon), var(--candy-lemon-deep));
    color: var(--dark-color);
    border: 2px solid var(--candy-lemon-deep);
}

.score.low-score {
    background: linear-gradient(135deg, var(--candy-pink), var(--candy-pink-deep));
    color: white;
    border: 2px solid var(--candy-pink-deep);
}

.score:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

/* 動物單元格美化 */
.animal-cell {
    text-align: center;
    padding: 10px;
    background: linear-gradient(135deg, var(--candy-cream), white);
    border-radius: var(--border-radius-small);
    margin: 2px;
}

/* 浮動裝飾動畫 */
.floating-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    z-index: 1;
}

.floating-decoration {
    position: absolute;
    font-size: 1.5rem;
    opacity: 0.3;
    animation: float 6s ease-in-out infinite;
}

.floating-decoration:nth-child(1) {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-decoration:nth-child(2) {
    top: 30%;
    right: 15%;
    animation-delay: 2s;
}

.floating-decoration:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg); 
        opacity: 0.3;
    }
    25% { 
        transform: translateY(-10px) rotate(5deg); 
        opacity: 0.5;
    }
    50% { 
        transform: translateY(-20px) rotate(0deg); 
        opacity: 0.7;
    }
    75% { 
        transform: translateY(-10px) rotate(-5deg); 
        opacity: 0.5;
    }
}

/* 動物頭像浮動動畫 */
@keyframes animalFloat {
    0%, 100% { 
        transform: translateY(0px); 
    }
    50% { 
        transform: translateY(-3px); 
    }
}

.student-animal-avatar,
.student-animal-emoji {
    animation: animalFloat 3s ease-in-out infinite;
}

.student-animal-avatar:nth-child(even),
.student-animal-emoji:nth-child(even) {
    animation-delay: 1.5s;
}

/* 響應式設計 - 動物頭像 */
@media (max-width: 768px) {
    .student-animal-avatar,
    .student-animal-emoji {
        width: 40px;
        height: 40px;
    }
    
    .animal-avatar-img {
        width: 100%;
        height: 100%;
    }
    
    .animal-emoji {
        width: 16px;
        height: 16px;
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .student-animal-avatar,
    .student-animal-emoji {
        width: 35px;
        height: 35px;
    }
    
    .student-name {
        padding: 3px 8px;
        font-size: 0.9rem;
    }
}
