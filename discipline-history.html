<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍭 計分小達人 - 違規歷史記錄</title>
    
    <!-- Google Fonts - 糖果風格字體 -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/discipline-history.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 側邊欄 -->
        <aside class="sidebar">
            <h1>🍭 計分小達人</h1>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-users"></i> 🐾 學生名單</a></li>
                    <li><a href="lucky-draw.html"><i class="fas fa-dharmachakra"></i> 🎲 幸運轉盤</a></li>
                    <li><a href="daily-checkin.html"><i class="fas fa-calendar-check"></i> 📅 每日簽到</a></li>
                    <li><a href="homework-submission.html"><i class="fas fa-book"></i> 📚 作業繳交</a></li>
                    <li><a href="homework-history.html"><i class="fas fa-clipboard-list"></i> 📋 繳交歷史</a></li>
                    <li><a href="tooth-brushing.html"><i class="fas fa-tooth"></i> 🦷 刷牙登記</a></li>
                    <li><a href="cleaning.html"><i class="fas fa-broom"></i> 🧹 打掃登記</a></li>
                    <li><a href="seat-cleaning.html"><i class="fas fa-chair"></i> 🪑 清潔座位</a></li>
                    <li><a href="officer-duties.html"><i class="fas fa-crown"></i> 👑 幹部工作</a></li>
                    <li><a href="duty-student.html"><i class="fas fa-clipboard-list"></i> 📋 值日生工作</a></li>
                    <li><a href="lunch.html"><i class="fas fa-utensils"></i> 🍽️ 午餐登記</a></li>
                    <li><a href="nap-time.html"><i class="fas fa-bed"></i> 😴 午休閉眼睛</a></li>
                    <li><a href="honesty-truth.html"><i class="fas fa-heart"></i> ❤️ 不妄語</a></li>
                    <li><a href="discipline.html"><i class="fas fa-balance-scale"></i> ⚖️ 秩序禮儀</a></li>
                    <li class="active"><a href="discipline-history.html"><i class="fas fa-history"></i> 📊 違規歷史</a></li>
                    <li><a href="leaderboard.html"><i class="fas fa-trophy"></i> 🏆 榮譽榜</a></li>
                    <li><a href="manual-score.html"><i class="fas fa-edit"></i> ✏️ 手動加減分</a></li>
                </ul>
            </nav>
            <div class="data-management">
                <h3>📊 資料管理</h3>
                <button id="exportBtn" class="btn btn-secondary"><i class="fas fa-file-export"></i> 匯出資料</button>
                <button id="importBtn" class="btn btn-secondary"><i class="fas fa-file-import"></i> 匯入資料</button>
                <button id="exportHistoryBtn" class="btn btn-warning"><i class="fas fa-download"></i> 匯出違規 CSV</button>
                <input type="file" id="importFile" accept=".json" style="display: none;">
            </div>
        </aside>

        <!-- 主要內容區 -->
        <main class="main-content">
            <div class="header-container">
                <h2><i class="fas fa-history"></i> 📊 違規歷史記錄管理</h2>
                <div class="controls-container">
                    <!-- 時段選擇器 -->
                    <div class="period-selector">
                        <button id="totalPeriodBtn" class="period-btn active" data-period="total">
                            <i class="fas fa-list-alt"></i> 總違規記錄
                        </button>
                        <button id="weekPeriodBtn" class="period-btn" data-period="week">
                            <i class="fas fa-calendar-week"></i> 週違規記錄
                        </button>
                        <button id="monthPeriodBtn" class="period-btn" data-period="month">
                            <i class="fas fa-calendar-alt"></i> 月違規記錄
                        </button>
                    </div>

                    <div class="date-display">
                        <span id="currentDate"></span>
                    </div>
                </div>
            </div>

            <!-- 時段資訊顯示 -->
            <div class="period-info" id="periodInfo">
                <div class="period-stats">
                    <div class="stat-card">
                        <i class="fas fa-calendar"></i>
                        <div class="stat-content">
                            <span class="stat-label">統計期間</span>
                            <span class="stat-value" id="periodRange">全部時間</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div class="stat-content">
                            <span class="stat-label">違規次數</span>
                            <span class="stat-value" id="periodViolations">0</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-minus-circle"></i>
                        <div class="stat-content">
                            <span class="stat-label">總扣分</span>
                            <span class="stat-value" id="periodPenalty">0</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-users"></i>
                        <div class="stat-content">
                            <span class="stat-label">涉及學生</span>
                            <span class="stat-value" id="periodStudents">0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 扣分併入排行榜功能 -->
            <div class="leaderboard-integration">
                <h3><i class="fas fa-trophy"></i> 排行榜扣分設定</h3>
                <div class="integration-controls">
                    <div class="control-group">
                        <label class="control-label">
                            <input type="checkbox" id="enableLeaderboardIntegration" class="integration-checkbox">
                            <span class="checkmark"></span>
                            啟用違規扣分併入排行榜
                        </label>
                        <p class="control-description">勾選後，選定時段的違規記錄將會影響學生在排行榜中的分數</p>
                    </div>

                    <div class="date-range-selector" id="dateRangeSelector" style="display: none;">
                        <div class="range-group">
                            <label for="integrationStartDate">扣分起始日期：</label>
                            <input type="date" id="integrationStartDate" class="form-control">
                        </div>
                        <div class="range-group">
                            <label for="integrationEndDate">扣分結束日期：</label>
                            <input type="date" id="integrationEndDate" class="form-control">
                        </div>
                        <div class="range-actions">
                            <button id="applyIntegrationBtn" class="btn btn-primary">
                                <i class="fas fa-check"></i> 套用設定
                            </button>
                            <button id="clearIntegrationBtn" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 清除設定
                            </button>
                        </div>
                    </div>

                    <div class="integration-status" id="integrationStatus">
                        <div class="status-info">
                            <i class="fas fa-info-circle"></i>
                            <span id="integrationStatusText">目前未啟用違規扣分併入排行榜</span>
                        </div>
                        <div class="affected-records" id="affectedRecords" style="display: none;">
                            <span class="affected-count">影響記錄：<strong id="affectedCount">0</strong> 筆</span>
                            <span class="affected-penalty">總扣分：<strong id="affectedPenalty">0</strong> 分</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 統計摘要 -->
            <div class="history-summary">
                <div class="summary-card">
                    <i class="fas fa-list-alt"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="totalRecords">0</span>
                        <span class="summary-label">總記錄數</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-exclamation-triangle violation"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="todayRecords">0</span>
                        <span class="summary-label">今日記錄</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-calendar-week"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="weekRecords">0</span>
                        <span class="summary-label">本週記錄</span>
                    </div>
                </div>
                <div class="summary-card">
                    <i class="fas fa-minus-circle penalty"></i>
                    <div class="summary-info">
                        <span class="summary-count" id="totalPenalty">0</span>
                        <span class="summary-label">總扣分</span>
                    </div>
                </div>
            </div>

            <!-- 篩選器 -->
            <div class="history-filters">
                <h3><i class="fas fa-filter"></i> 篩選條件</h3>
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="dateFilterStart">開始日期：</label>
                        <input type="date" id="dateFilterStart" class="form-control">
                    </div>
                    <div class="filter-group">
                        <label for="dateFilterEnd">結束日期：</label>
                        <input type="date" id="dateFilterEnd" class="form-control">
                    </div>
                    <div class="filter-group">
                        <label for="studentFilter">學生：</label>
                        <select id="studentFilter" class="form-control">
                            <option value="">所有學生</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="violationFilter">違規類型：</label>
                        <select id="violationFilter" class="form-control">
                            <option value="">所有類型</option>
                            <option value="attitude">對老師態度不佳</option>
                            <option value="talking_back">對老師幹部頂嘴</option>
                            <option value="talking_after_bell">鐘響講話</option>
                            <option value="talking_without_permission">未經允許講話</option>
                            <option value="fighting_swearing">吵架、罵髒話</option>
                            <option value="hitting_kicking">亂踢人打人</option>
                            <option value="incorrect_corrections">訂正不確實</option>
                            <option value="playing_with_violators">和違規同學玩</option>
                            <option value="missing_homework">欠作業超過三天</option>
                            <option value="playing_with_toys">上課玩玩具</option>
                            <option value="chatting_while_cleaning">掃地時聊天</option>
                            <option value="talking_in_line">整隊時講話</option>
                            <option value="littering">亂丟東西</option>
                            <option value="other">其它</option>
                        </select>
                    </div>
                </div>
                <div class="filter-actions">
                    <button id="applyFilterBtn" class="btn btn-primary">
                        <i class="fas fa-search"></i> 篩選
                    </button>
                    <button id="clearFilterBtn" class="btn btn-secondary">
                        <i class="fas fa-eraser"></i> 清除
                    </button>
                    <button id="downloadHistoryBtn" class="btn btn-success">
                        <i class="fas fa-download"></i> 下載記錄
                    </button>
                    <button id="backBtn" class="btn btn-info">
                        <i class="fas fa-arrow-left"></i> 返回秩序禮儀
                    </button>
                </div>
            </div>
            
            <!-- 統計資訊 -->
            <div class="history-stats">
                <div class="stat-item">
                    <span class="stat-label">篩選結果：</span>
                    <span class="stat-value" id="filteredTotal">0</span>
                    <span class="stat-unit">筆記錄</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">篩選扣分：</span>
                    <span class="stat-value" id="filteredPenalty">0</span>
                    <span class="stat-unit">分</span>
                </div>
            </div>
            
            <!-- 歷史記錄表格 -->
            <div class="history-table-container">
                <table id="historyTable" class="history-table">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>時間</th>
                            <th>🐾</th>
                            <th>座號</th>
<parameter name="new_str">                            <th>姓名</th>
                            <th>違規行為</th>
                            <th>詳細說明</th>
                            <th>扣分</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="historyTableBody">
                        <!-- 歷史記錄將由 JavaScript 動態生成 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分頁 -->
            <div class="history-pagination" id="historyPagination">
                <!-- 分頁將由 JavaScript 動態生成 -->
            </div>
        </main>
    </div>

    <!-- 編輯違規記錄彈窗 -->
    <div id="editViolationModal" class="modal">
        <div class="modal-content large">
            <span class="close">&times;</span>
            <h3>✏️ 編輯違規記錄</h3>
            <p>學生：<span id="editStudentNameSpan" class="student-name"></span></p>
            
            <div class="violation-selection">
                <h4>修改違規行為：</h4>
                <div class="violation-grid">
                    <div class="edit-violation-option" data-violation="attitude">
                        <div class="violation-number">1</div>
                        <div class="violation-text">對老師態度不佳</div>
                    </div>
                    <div class="edit-violation-option" data-violation="talking_back">
                        <div class="violation-number">2</div>
                        <div class="violation-text">對老師幹部頂嘴</div>
                    </div>
                    <div class="edit-violation-option" data-violation="talking_after_bell">
                        <div class="violation-number">3</div>
                        <div class="violation-text">鐘響講話</div>
                    </div>
                    <div class="edit-violation-option" data-violation="talking_without_permission">
                        <div class="violation-number">4</div>
                        <div class="violation-text">上課、午餐或午休未經允許講話、未舉手講話</div>
                    </div>
                    <div class="edit-violation-option" data-violation="fighting_swearing">
                        <div class="violation-number">5</div>
                        <div class="violation-text">吵架、罵髒話</div>
                    </div>
                    <div class="edit-violation-option" data-violation="hitting_kicking">
                        <div class="violation-number">6</div>
                        <div class="violation-text">亂踢人打人</div>
                    </div>
                    <div class="edit-violation-option" data-violation="incorrect_corrections">
                        <div class="violation-number">7</div>
                        <div class="violation-text">訂正不確實三次以上</div>
                    </div>
                    <div class="edit-violation-option" data-violation="playing_with_violators">
                        <div class="violation-number">8</div>
                        <div class="violation-text">和欠作業或反省的同學玩、講話</div>
                    </div>
                    <div class="edit-violation-option" data-violation="missing_homework">
                        <div class="violation-number">9</div>
                        <div class="violation-text">欠作業超過三天</div>
                    </div>
                    <div class="edit-violation-option" data-violation="playing_with_toys">
                        <div class="violation-number">10</div>
                        <div class="violation-text">上課玩玩具、文具</div>
                    </div>
                    <div class="edit-violation-option" data-violation="chatting_while_cleaning">
                        <div class="violation-number">11</div>
                        <div class="violation-text">掃地時間聊天或玩</div>
                    </div>
                    <div class="edit-violation-option" data-violation="talking_in_line">
                        <div class="violation-number">12</div>
                        <div class="violation-text">整隊時講話或玩</div>
                    </div>
                    <div class="edit-violation-option" data-violation="littering">
                        <div class="violation-number">13</div>
                        <div class="violation-text">亂丟東西</div>
                    </div>
                    <div class="edit-violation-option" data-violation="other">
                        <div class="violation-number">14</div>
                        <div class="violation-text">其它</div>
                    </div>
                </div>
                
                <div class="custom-note" id="editCustomNoteSection" style="display: none;">
                    <label for="editCustomNote">請說明其它違規行為：</label>
                    <textarea id="editCustomNote" rows="3" placeholder="請詳細說明違規行為..."></textarea>
                </div>
            </div>
            
            <div class="penalty-info">
                <div class="penalty-display">
                    <i class="fas fa-minus-circle"></i>
                    <span>扣分：<strong>5分</strong></span>
                </div>
            </div>
            
            <div class="modal-buttons">
                <button id="confirmEditViolationBtn" class="btn btn-primary" disabled>確認修改</button>
                <button id="cancelEditViolationBtn" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script src="js/animal-config.js"></script>
    <script src="js/discipline-history.js"></script>
</body>
</html>