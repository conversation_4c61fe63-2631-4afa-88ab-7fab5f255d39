/* 作業繳交頁面樣式 */

/* 頁面頂部區域 */
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.date-display {
    background: white;
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: var(--box-shadow);
    font-weight: 500;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-display i {
    color: var(--primary-color);
}

/* 常用作業庫管理區域 */
.frequent-homework-management {
    background: linear-gradient(135deg, #e3f2fd, #ffffff);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    border: 2px solid rgba(33, 150, 243, 0.1);
}

.frequent-homework-management h3 {
    color: #1976d2;
    margin-bottom: 20px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.frequent-homework-management h3 i {
    color: #2196f3;
}

.frequent-homework-input-section {
    margin-bottom: 20px;
}

.frequent-homework-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    min-height: 50px;
    padding: 15px;
    background: rgba(227, 242, 253, 0.3);
    border-radius: 12px;
    border: 2px dashed rgba(33, 150, 243, 0.3);
}

.frequent-homework-item {
    background: linear-gradient(135deg, #2196f3, #42a5f5);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    transition: all 0.3s ease;
    animation: slideIn 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.frequent-homework-item:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
    background: linear-gradient(135deg, #1976d2, #2196f3);
}

.frequent-homework-item:active {
    transform: translateY(0) scale(0.98);
}

.frequent-homework-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.frequent-homework-item:hover::before {
    left: 100%;
}

.frequent-homework-item .remove-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.frequent-homework-item .remove-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.frequent-homework-item .add-to-today {
    background: none;
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.frequent-homework-item .add-to-today:hover {
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.empty-frequent-hint, .empty-today-hint {
    color: #666;
    font-style: italic;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
    width: 100%;
    padding: 10px;
    font-size: 14px;
}

.empty-frequent-hint i, .empty-today-hint i {
    color: #ffc107;
    font-size: 16px;
}

/* 作業項目管理區域 */
.homework-management {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
}

.homework-management h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.homework-input-section {
    margin-bottom: 20px;
}

.input-group {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.input-group input {
    flex: 1;
    min-width: 300px;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.1);
    transform: translateY(-1px);
}

.homework-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    min-height: 50px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.homework-item {
    background: linear-gradient(135deg, var(--primary-color), #ff69b4);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(255, 182, 193, 0.3);
    transition: all 0.3s ease;
    animation: slideIn 0.3s ease;
}

.homework-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
}

.homework-item-remove {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 10px;
    transition: all 0.3s ease;
}

.homework-item-remove:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.homework-items-empty {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    width: 100%;
    padding: 20px;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 作業繳交摘要卡片 */
.homework-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.summary-card i {
    font-size: 2.5rem;
    padding: 15px;
    border-radius: 50%;
    background: rgba(255, 182, 193, 0.2);
    color: var(--primary-color);
}

.summary-card i.success {
    background: rgba(152, 251, 152, 0.2);
    color: #28a745;
}

.summary-card i.warning {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.summary-card i.primary {
    background: rgba(135, 206, 235, 0.2);
    color: #007bff;
}

.summary-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.summary-count {
    font-size: 2rem;
    font-weight: bold;
    color: var(--text-color);
}

.summary-label {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* 操作按鈕區域 */
.action-buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    margin-right: auto;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.search-box input {
    padding: 10px 15px 10px 45px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    width: 250px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.1);
}

/* 表格樣式 */
.table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

#homeworkTable {
    width: 100%;
    border-collapse: collapse;
}

#homeworkTable th {
    background: linear-gradient(135deg, var(--primary-color), #ffa6c9);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    position: relative;
}

#homeworkTable th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

#homeworkTable td {
    padding: 12px 15px;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.3s ease;
}

#homeworkTable tr:hover td {
    background-color: rgba(255, 182, 193, 0.05);
}

#homeworkTable tr:last-child td {
    border-bottom: none;
}

/* 學生頭像 */
.student-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.student-avatar:hover {
    transform: scale(1.1) rotate(5deg);
}

/* 學號和姓名樣式 */
.student-id {
    font-weight: 600;
    color: var(--primary-color);
}

.student-name {
    font-weight: 500;
    color: var(--text-color);
}

/* 分數顯示 */
.score-display {
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--primary-color);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* 繳交狀態 */
.submission-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
}

.status-submitted {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.status-not-submitted {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

/* 作業項目按鈕樣式 */
.homework-button {
    padding: 4px 8px;
    border: none;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin: 2px;
    min-width: 60px;
    justify-content: center;
}

.homework-button.submitted {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.homework-button.not-submitted {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.homework-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.homework-button.submitted:hover {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
}

.homework-button.not-submitted:hover {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.homework-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    max-width: 200px;
}

/* 完成度顯示 */
.completion-rate {
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.9rem;
    text-align: center;
    min-width: 50px;
}

.completion-100 {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.completion-75 {
    background: linear-gradient(135deg, #17a2b8, #007bff);
    color: white;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.completion-50 {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.completion-0 {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* 今日得分顯示 */
.daily-score {
    font-weight: bold;
    color: var(--primary-color);
    background: rgba(255, 182, 193, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.9rem;
    text-align: center;
    min-width: 40px;
}

/* 按鈕樣式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #ff69b4);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 182, 193, 0.6);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.5);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #007bff);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.5);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.5);
}

/* 分頁樣式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination button {
    padding: 8px 12px;
    border: 2px solid var(--primary-color);
    background: white;
    color: var(--primary-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.pagination button:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.pagination button.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 182, 193, 0.4);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 彈窗樣式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

.modal-content.large {
    max-width: 800px;
    width: 95%;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    position: absolute;
    right: 20px;
    top: 20px;
}

.close:hover {
    color: var(--primary-color);
}

.modal h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.reward-info {
    background: linear-gradient(135deg, #e8f5e8, #f0fdf4);
    padding: 15px;
    border-radius: 12px;
    border-left: 4px solid #28a745;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.reward-info i {
    color: #28a745;
    font-size: 1.2rem;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
}

/* 歷史記錄篩選 */
.history-filters {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.history-filters label {
    font-weight: 500;
    color: var(--text-color);
}

.history-filters select,
.history-filters input {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.history-filters select:focus,
.history-filters input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.history-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

#historyTable {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

#historyTable th {
    background: var(--primary-color);
    color: white;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

#historyTable td {
    padding: 10px 12px;
    border-bottom: 1px solid #f8f9fa;
}

#historyTable tr:hover td {
    background-color: rgba(255, 182, 193, 0.05);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .homework-summary {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .search-box {
        margin-right: 0;
    }
    
    .search-box input {
        width: 100%;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    #homeworkTable {
        min-width: 800px;
    }
    
    .modal-content {
        margin: 5% auto;
        padding: 20px;
        width: 95%;
    }
    
    .history-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .summary-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .summary-card i {
        font-size: 2rem;
        padding: 10px;
    }
    
    .summary-count {
        font-size: 1.5rem;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}