<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>記分小達人 - 功能測試</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a4fcf;
        }
        .nav-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .nav-links a {
            background: #6c5ce7;
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #5a4fcf;
        }
    </style>
</head>
<body>
    <h1>記分小達人 - 系統測試頁面</h1>
    
    <div class="nav-links">
        <a href="index.html">學生名單</a>
        <a href="lucky-draw.html">幸運轉蛋</a>
        <a href="daily-checkin.html">每日簽到</a>
        <a href="leaderboard.html">榮譽榜</a>
        <a href="manual-score.html">手動加減分</a>
    </div>
    
    <div class="test-section">
        <h2>📋 檔案完整性檢查</h2>
        <div id="fileCheck"></div>
        <button onclick="checkFiles()">檢查檔案</button>
    </div>
    
    <div class="test-section">
        <h2>💾 本地儲存測試</h2>
        <div id="storageTest"></div>
        <button onclick="testLocalStorage()">測試本地儲存</button>
        <button onclick="clearStorage()">清除儲存</button>
    </div>
    
    <div class="test-section">
        <h2>👥 學生資料測試</h2>
        <div id="studentTest"></div>
        <button onclick="addTestStudents()">新增測試學生</button>
        <button onclick="showStudents()">顯示學生資料</button>
    </div>
    
    <div class="test-section">
        <h2>🎯 功能整合測試</h2>
        <div id="integrationTest"></div>
        <button onclick="runIntegrationTest()">執行整合測試</button>
    </div>

    <script>
        // 檔案檢查
        function checkFiles() {
            const files = [
                'index.html',
                'lucky-draw.html', 
                'daily-checkin.html',
                'leaderboard.html',
                'manual-score.html',
                'js/script.js',
                'js/lucky-draw.js',
                'js/daily-checkin.js',
                'js/leaderboard.js',
                'js/manual-score.js',
                'css/style.css',
                'css/lucky-draw.css',
                'css/daily-checkin.css',
                'css/leaderboard.css',
                'css/manual-score.css'
            ];
            
            const results = document.getElementById('fileCheck');
            results.innerHTML = '<p>正在檢查檔案...</p>';
            
            let checkResults = [];
            let completed = 0;
            
            files.forEach(file => {
                fetch(file)
                    .then(response => {
                        checkResults.push({
                            file: file,
                            status: response.ok ? 'success' : 'error',
                            message: response.ok ? '✅ 存在' : `❌ 不存在 (${response.status})`
                        });
                    })
                    .catch(error => {
                        checkResults.push({
                            file: file,
                            status: 'error',
                            message: `❌ 載入失敗: ${error.message}`
                        });
                    })
                    .finally(() => {
                        completed++;
                        if (completed === files.length) {
                            displayFileResults(checkResults);
                        }
                    });
            });
        }
        
        function displayFileResults(results) {
            const container = document.getElementById('fileCheck');
            let html = '<h3>檔案檢查結果：</h3>';
            
            results.forEach(result => {
                html += `<div class="test-result ${result.status}">
                    <strong>${result.file}</strong>: ${result.message}
                </div>`;
            });
            
            const successCount = results.filter(r => r.status === 'success').length;
            html += `<div class="test-result info">
                總計：${results.length} 個檔案，${successCount} 個成功，${results.length - successCount} 個失敗
            </div>`;
            
            container.innerHTML = html;
        }
        
        // 本地儲存測試
        function testLocalStorage() {
            const results = document.getElementById('storageTest');
            let html = '<h3>本地儲存測試結果：</h3>';
            
            try {
                // 測試寫入
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('test', JSON.stringify(testData));
                html += '<div class="test-result success">✅ 寫入測試成功</div>';
                
                // 測試讀取
                const retrieved = JSON.parse(localStorage.getItem('test'));
                if (retrieved && retrieved.test === 'data') {
                    html += '<div class="test-result success">✅ 讀取測試成功</div>';
                } else {
                    html += '<div class="test-result error">❌ 讀取測試失敗</div>';
                }
                
                // 測試現有資料
                const students = localStorage.getItem('students');
                const scoreHistory = localStorage.getItem('scoreHistory');
                
                html += `<div class="test-result info">
                    📊 學生資料: ${students ? `存在 (${JSON.parse(students).length} 筆)` : '不存在'}
                </div>`;
                html += `<div class="test-result info">
                    📝 操作記錄: ${scoreHistory ? `存在 (${JSON.parse(scoreHistory).length} 筆)` : '不存在'}
                </div>`;
                
                // 清理測試資料
                localStorage.removeItem('test');
                
            } catch (error) {
                html += `<div class="test-result error">❌ 本地儲存測試失敗: ${error.message}</div>`;
            }
            
            results.innerHTML = html;
        }
        
        function clearStorage() {
            if (confirm('確定要清除所有本地儲存資料嗎？這將刪除所有學生資料和操作記錄！')) {
                localStorage.clear();
                document.getElementById('storageTest').innerHTML = 
                    '<div class="test-result info">🗑️ 所有本地儲存資料已清除</div>';
            }
        }
        
        // 學生資料測試
        function addTestStudents() {
            const testStudents = [
                { id: 1, number: '01', class: '603', name: '測試學生A', score: 85 },
                { id: 2, number: '02', class: '603', name: '測試學生B', score: 92 },
                { id: 3, number: '03', class: '603', name: '測試學生C', score: 78 },
                { id: 4, number: '04', class: '604', name: '測試學生D', score: 88 },
                { id: 5, number: '05', class: '604', name: '測試學生E', score: 95 }
            ];
            
            // 添加簽到記錄
            const today = new Date().toISOString().split('T')[0];
            testStudents.forEach(student => {
                student.checkins = {};
                student.checkins[today] = {
                    checkedIn: Math.random() > 0.5,
                    timestamp: new Date().toISOString()
                };
            });
            
            localStorage.setItem('students', JSON.stringify(testStudents));
            
            document.getElementById('studentTest').innerHTML = 
                '<div class="test-result success">✅ 已新增 5 筆測試學生資料</div>';
        }
        
        function showStudents() {
            const students = localStorage.getItem('students');
            const container = document.getElementById('studentTest');
            
            if (!students) {
                container.innerHTML = '<div class="test-result info">📝 沒有學生資料</div>';
                return;
            }
            
            const studentData = JSON.parse(students);
            let html = '<h3>學生資料：</h3>';
            
            studentData.forEach(student => {
                html += `<div class="test-result info">
                    ${student.number} ${student.name} (${student.class}班) - ${student.score || 0}分
                </div>`;
            });
            
            container.innerHTML = html;
        }
        
        // 整合測試
        function runIntegrationTest() {
            const container = document.getElementById('integrationTest');
            container.innerHTML = '<p>正在執行整合測試...</p>';
            
            let results = [];
            
            // 測試 1: 檢查必要的 DOM 元素
            try {
                const testElements = [
                    'studentTable',
                    'addStudentBtn', 
                    'studentModal',
                    'exportBtn',
                    'importBtn'
                ];
                
                // 由於我們不在實際頁面上，這個測試會失敗，但這是預期的
                results.push({
                    test: 'DOM 元素檢查',
                    status: 'info',
                    message: '⚠️ 需要在實際頁面上測試'
                });
            } catch (error) {
                results.push({
                    test: 'DOM 元素檢查',
                    status: 'error',
                    message: `❌ ${error.message}`
                });
            }
            
            // 測試 2: JavaScript 函數可用性
            try {
                // 檢查基本 JavaScript 功能
                const testObj = { test: 'value' };
                const jsonString = JSON.stringify(testObj);
                const parsed = JSON.parse(jsonString);
                
                if (parsed.test === 'value') {
                    results.push({
                        test: 'JavaScript 基本功能',
                        status: 'success',
                        message: '✅ JSON 序列化/反序列化正常'
                    });
                }
            } catch (error) {
                results.push({
                    test: 'JavaScript 基本功能',
                    status: 'error',
                    message: `❌ ${error.message}`
                });
            }
            
            // 測試 3: 日期處理
            try {
                const now = new Date();
                const dateString = now.toISOString().split('T')[0];
                const formatted = now.toLocaleDateString('zh-TW');
                
                results.push({
                    test: '日期處理',
                    status: 'success',
                    message: `✅ 日期格式化正常 (${formatted})`
                });
            } catch (error) {
                results.push({
                    test: '日期處理',
                    status: 'error',
                    message: `❌ ${error.message}`
                });
            }
            
            // 顯示結果
            let html = '<h3>整合測試結果：</h3>';
            results.forEach(result => {
                html += `<div class="test-result ${result.status}">
                    <strong>${result.test}</strong>: ${result.message}
                </div>`;
            });
            
            html += `<div class="test-result info">
                💡 提示：完整的功能測試需要在各個實際頁面上進行
            </div>`;
            
            container.innerHTML = html;
        }
        
        // 頁面載入時自動執行基本檢查
        window.addEventListener('DOMContentLoaded', function() {
            console.log('記分小達人測試頁面已載入');
            testLocalStorage();
        });
    </script>
</body>
</html>
