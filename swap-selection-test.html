<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交換選擇功能測試</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .students-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .student-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            position: relative;
        }
        
        .student-card.has-protection {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
        }
        
        .student-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .student-score {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .angel-icon {
            position: absolute;
            top: -10px;
            right: -10px;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .control-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .instructions ul {
            line-height: 1.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1>🔄 交換選擇功能測試</h1>
            <p>測試神祕寶箱交換事件的新選擇機制</p>
        </div>
        
        <div class="students-grid" id="studentsGrid">
            <!-- 學生卡片將由 JavaScript 生成 -->
        </div>
        
        <div class="controls">
            <button class="control-btn" onclick="randomizeScores()">
                <i class="fas fa-dice"></i> 隨機分數
            </button>
            <button class="control-btn" onclick="addAngelProtection()">
                <i class="fas fa-shield-alt"></i> 添加天使保護
            </button>
            <button class="control-btn" onclick="resetTest()">
                <i class="fas fa-refresh"></i> 重置測試
            </button>
        </div>
        
        <div class="instructions">
            <h3>📖 測試說明</h3>
            <ul>
                <li><strong>觸發交換：</strong>點擊任意學生的「觸發交換」按鈕</li>
                <li><strong>選擇對象：</strong>在彈出的界面中選擇要交換的同學</li>
                <li><strong>策略提示：</strong>
                    <ul>
                        <li>綠色「↗ 獲利」：交換後會增加分數</li>
                        <li>紅色「↘ 虧損」：交換後會減少分數</li>
                        <li>藍色「→ 持平」：交換後分數相同</li>
                    </ul>
                </li>
                <li><strong>天使保護：</strong>有😇圖示的學生擁有天使保護狀態</li>
                <li><strong>取消選項：</strong>可以選擇取消交換，不進行任何變動</li>
            </ul>
        </div>
    </div>

    <script>
        // 測試學生資料
        let testStudents = [
            { id: 1, name: '小明', score: 15 },
            { id: 2, name: '小華', score: 25 },
            { id: 3, name: '小美', score: 8 },
            { id: 4, name: '小強', score: 20 },
            { id: 5, name: '小芳', score: 12 },
            { id: 6, name: '小傑', score: 30 }
        ];
        
        // 初始化
        function init() {
            window.students = [...testStudents];
            updateStudentsGrid();
        }
        
        // 更新學生網格
        function updateStudentsGrid() {
            const grid = document.getElementById('studentsGrid');
            grid.innerHTML = '';
            
            testStudents.forEach(student => {
                const hasProtection = student.specialStates && student.specialStates.angelProtection;
                
                const card = document.createElement('div');
                card.className = `student-card ${hasProtection ? 'has-protection' : ''}`;
                
                card.innerHTML = `
                    ${hasProtection ? '<div class="angel-icon">😇</div>' : ''}
                    <div class="student-name">${student.name}</div>
                    <div class="student-score">${student.score} 分</div>
                    <button class="test-btn" onclick="triggerSwapEvent(${student.id})">
                        <i class="fas fa-exchange-alt"></i> 觸發交換
                    </button>
                `;
                
                grid.appendChild(card);
            });
        }
        
        // 觸發交換事件
        function triggerSwapEvent(studentId) {
            const student = testStudents.find(s => s.id === studentId);
            if (!student) return;
            
            console.log('觸發交換事件:', student.name);
            
            // 模擬神祕寶箱的交換事件
            const swapCandidates = testStudents.filter(s => s.id !== studentId);
            
            if (swapCandidates.length === 0) {
                alert('沒有其他學生可以交換！');
                return;
            }
            
            // 創建模擬的神祕寶箱彈窗
            const modal = createMockMysteryBoxModal();
            document.body.appendChild(modal);
            
            // 顯示交換選擇界面
            setTimeout(() => {
                showSwapSelection(student, swapCandidates, modal);
            }, 500);
        }
        
        // 創建模擬神祕寶箱彈窗
        function createMockMysteryBoxModal() {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 2000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            
            const content = document.createElement('div');
            content.className = 'modal-content';
            content.style.cssText = `
                max-width: 600px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
                color: white;
                border-radius: 15px;
                position: relative;
            `;
            
            modal.appendChild(content);
            
            // 顯示彈窗
            setTimeout(() => {
                modal.style.opacity = '1';
            }, 10);
            
            return modal;
        }
        
        // 顯示交換選擇界面
        function showSwapSelection(student, candidates, modal) {
            const content = modal.querySelector('.modal-content');
            
            content.innerHTML = `
                <div style="padding: 20px; text-align: center;">
                    <div style="font-size: 3rem; margin-bottom: 15px;">🔄</div>
                    <h3 style="font-size: 1.8rem; margin-bottom: 10px;">分數交換</h3>
                    <p style="margin-bottom: 20px;">${student.name} 可以選擇與其他同學交換分數</p>
                    
                    <div style="
                        background: rgba(255, 255, 255, 0.2);
                        padding: 15px;
                        border-radius: 10px;
                        margin: 20px 0;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                    ">
                        <div style="font-weight: 600; margin-bottom: 10px;">
                            ${student.name} 目前分數：${student.score} 分
                        </div>
                    </div>
                    
                    <div style="
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                        gap: 15px;
                        margin: 20px 0;
                        max-height: 300px;
                        overflow-y: auto;
                    ">
                        ${candidates.map(candidate => {
                            const diff = candidate.score - student.score;
                            const diffText = diff > 0 ? `↗ 獲利 ${diff}` : diff < 0 ? `↘ 虧損 ${Math.abs(diff)}` : '→ 持平 0';
                            const diffColor = diff > 0 ? '#00b894' : diff < 0 ? '#ff7675' : '#74b9ff';
                            const hasProtection = candidate.specialStates && candidate.specialStates.angelProtection;
                            
                            return `
                                <div onclick="executeTestSwap('${student.id}', '${candidate.id}', this.closest('.modal'))"
                                     style="
                                    background: rgba(255, 255, 255, 0.15);
                                    padding: 15px;
                                    border-radius: 10px;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    border: 2px solid rgba(255, 255, 255, 0.2);
                                    position: relative;
                                " 
                                onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='translateY(-2px)'"
                                onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='translateY(0)'">
                                    <div style="font-weight: 600; margin-bottom: 8px;">
                                        ${candidate.name}
                                    </div>
                                    <div style="font-size: 1.2rem; font-weight: bold; color: ${diffColor}; margin-bottom: 5px;">
                                        ${candidate.score} 分
                                    </div>
                                    <div style="font-size: 0.8rem; opacity: 0.8; color: ${diffColor};">
                                        ${diffText} 分
                                    </div>
                                    ${hasProtection ? '<div style="position: absolute; top: 5px; right: 5px;">😇</div>' : ''}
                                </div>
                            `;
                        }).join('')}
                    </div>
                    
                    <button onclick="cancelTestSwap(this.closest('.modal'))" style="
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        color: white;
                        padding: 10px 20px;
                        border-radius: 25px;
                        cursor: pointer;
                        font-weight: 600;
                        margin-top: 20px;
                    ">
                        <i class="fas fa-times"></i> 取消交換
                    </button>
                </div>
            `;
        }
        
        // 執行測試交換
        window.executeTestSwap = function(studentId, targetId, modal) {
            const student = testStudents.find(s => s.id == studentId);
            const target = testStudents.find(s => s.id == targetId);
            
            if (!student || !target) return;
            
            const studentOldScore = student.score;
            const targetOldScore = target.score;
            
            // 執行交換
            student.score = targetOldScore;
            target.score = studentOldScore;
            
            // 更新全局資料
            window.students = [...testStudents];
            
            // 顯示結果
            showSwapResult(student, target, studentOldScore, targetOldScore, modal);
        };
        
        // 取消測試交換
        window.cancelTestSwap = function(modal) {
            showSwapResult(null, null, 0, 0, modal, true);
        };
        
        // 顯示交換結果
        function showSwapResult(student, target, oldScore1, oldScore2, modal, cancelled = false) {
            const content = modal.querySelector('.modal-content');
            
            if (cancelled) {
                content.innerHTML = `
                    <div style="padding: 20px; text-align: center;">
                        <div style="font-size: 3rem; margin-bottom: 15px;">🔄</div>
                        <h3 style="font-size: 1.8rem; margin-bottom: 10px;">交換取消</h3>
                        <p style="margin-bottom: 20px;">您選擇了取消交換，沒有進行任何分數變動。</p>
                        <button onclick="closeTestModal(this.closest('.modal'))" style="
                            background: rgba(255, 255, 255, 0.9);
                            color: #333;
                            border: none;
                            padding: 12px 30px;
                            border-radius: 25px;
                            font-weight: 600;
                            cursor: pointer;
                        ">確定</button>
                    </div>
                `;
            } else {
                content.innerHTML = `
                    <div style="padding: 20px; text-align: center;">
                        <div style="font-size: 3rem; margin-bottom: 15px;">🔄</div>
                        <h3 style="font-size: 1.8rem; margin-bottom: 10px;">交換成功！</h3>
                        <div style="
                            background: rgba(255, 255, 255, 0.2);
                            padding: 15px;
                            border-radius: 10px;
                            margin: 20px 0;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                        ">
                            <div style="font-weight: 600; margin-bottom: 10px;">
                                ${student.name}: ${oldScore1} → ${student.score} 分
                            </div>
                            <div style="font-weight: 600;">
                                ${target.name}: ${oldScore2} → ${target.score} 分
                            </div>
                        </div>
                        <button onclick="closeTestModal(this.closest('.modal'))" style="
                            background: rgba(255, 255, 255, 0.9);
                            color: #333;
                            border: none;
                            padding: 12px 30px;
                            border-radius: 25px;
                            font-weight: 600;
                            cursor: pointer;
                        ">確定</button>
                    </div>
                `;
            }
        }
        
        // 關閉測試彈窗
        window.closeTestModal = function(modal) {
            modal.style.opacity = '0';
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
                updateStudentsGrid();
            }, 300);
        };
        
        // 隨機分數
        function randomizeScores() {
            testStudents.forEach(student => {
                student.score = Math.floor(Math.random() * 30) + 5;
            });
            window.students = [...testStudents];
            updateStudentsGrid();
        }
        
        // 添加天使保護
        function addAngelProtection() {
            const randomStudent = testStudents[Math.floor(Math.random() * testStudents.length)];
            if (!randomStudent.specialStates) {
                randomStudent.specialStates = {};
            }
            randomStudent.specialStates.angelProtection = true;
            window.students = [...testStudents];
            updateStudentsGrid();
            alert(`給予 ${randomStudent.name} 天使保護！`);
        }
        
        // 重置測試
        function resetTest() {
            testStudents = [
                { id: 1, name: '小明', score: 15 },
                { id: 2, name: '小華', score: 25 },
                { id: 3, name: '小美', score: 8 },
                { id: 4, name: '小強', score: 20 },
                { id: 5, name: '小芳', score: 12 },
                { id: 6, name: '小傑', score: 30 }
            ];
            window.students = [...testStudents];
            updateStudentsGrid();
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
