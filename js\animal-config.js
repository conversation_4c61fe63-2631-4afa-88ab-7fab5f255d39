// 可愛動物圖片配置系統
const ANIMAL_CONFIG = {
    // 動物圖片路徑配置
    images: {
        bear: 'images/animals/bear.png',
        rabbit: 'images/animals/rabbit.png',
        dog: 'images/animals/dog.png',
        chick: 'images/animals/chick.png',
        fox: 'images/animals/fox.png'
    },
    
    // 動物資料配置（包含中文名稱和特性）
    animals: {
        bear: {
            name: '小熊',
            emoji: '🐻',
            color: '#D2B48C',
            personality: '溫和可愛'
        },
        rabbit: {
            name: '小兔',
            emoji: '🐰',
            color: '#F5DEB3',
            personality: '活潑機靈'
        },
        dog: {
            name: '小狗',
            emoji: '🐶',
            color: '#DEB887',
            personality: '忠誠友善'
        },
        chick: {
            name: '小雞',
            emoji: '🐤',
            color: '#FFD700',
            personality: '天真無邪'
        },
        fox: {
            name: '小狐',
            emoji: '🦊',
            color: '#FF8C00',
            personality: '聰明伶俐'
        }
    },
    
    // 為學生隨機分配動物頭像
    assignRandomAnimal() {
        const animalKeys = Object.keys(this.animals);
        const randomIndex = Math.floor(Math.random() * animalKeys.length);
        return animalKeys[randomIndex];
    },
    
    // 取得動物圖片路徑
    getAnimalImage(animalType) {
        return this.images[animalType] || this.images.bear;
    },
    
    // 取得動物資訊
    getAnimalInfo(animalType) {
        return this.animals[animalType] || this.animals.bear;
    },
    
    // 為所有學生初始化動物頭像（如果還沒有的話）
    initializeStudentAnimals(students) {
        students.forEach(student => {
            if (!student.animal) {
                student.animal = this.assignRandomAnimal();
            }
        });
        return students;
    }
};

// 動物主題裝飾圖案配置
const DECORATIVE_ANIMALS = {
    // 頁面標題對應的動物裝飾
    pageDecorations: {
        'index': ['🐾', '🦊', '🐻'],
        'daily-checkin': ['📅', '🐤', '☀️'],
        'tooth-brushing': ['🦷', '🐰', '✨'],
        'cleaning': ['🧹', '🐶', '🌟'],
        'seat-cleaning': ['🪑', '🐻', '💫'],
        'officer-duties': ['👑', '🦊', '⭐'],
        'duty-student': ['📋', '🐤', '📝'],
        'lunch': ['🍽️', '🐰', '🥕'],
        'nap-time': ['😴', '🐻', '🌙'],
        'honesty-truth': ['❤️', '🦊', '💝'],
        'discipline': ['⚖️', '🐶', '📏'],
        'leaderboard': ['🏆', '🐾', '🎉'],
        'manual-score': ['✏️', '🐤', '📊']
    },
    
    // 獲取頁面裝飾圖案
    getPageDecorations(pageType) {
        return this.pageDecorations[pageType] || ['🐾', '⭐', '💫'];
    },
    
    // 創建浮動裝飾元素HTML
    createFloatingDecorations(pageType) {
        const decorations = this.getPageDecorations(pageType);
        let html = '<div class="floating-decorations">';
        
        decorations.forEach((decoration, index) => {
            html += `<div class="floating-decoration" style="animation-delay: ${index * 0.5}s">${decoration}</div>`;
        });
        
        html += '</div>';
        return html;
    }
};

// 如果在瀏覽器環境中，將配置添加到全域
if (typeof window !== 'undefined') {
    window.ANIMAL_CONFIG = ANIMAL_CONFIG;
    window.DECORATIVE_ANIMALS = DECORATIVE_ANIMALS;
}