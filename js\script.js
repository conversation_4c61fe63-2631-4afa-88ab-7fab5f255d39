// 學生資料變數
let students = [];

// 歷史人物名字庫
const historicalFigures = [
    // 古代帝王
    '拿破崙', '亞歷山大大帝', '凱撒大帝', '秦始皇', '漢武帝', '唐太宗', '康熙皇帝',
    '武則天', '埃及豔后', '伊莉莎白一世', '路易十四', '彼得大帝', '查理曼大帝',
    '成吉思汗', '忽必烈', '朱元璋', '雍正皇帝', '乾隆皇帝', '明治天皇',

    // 藝術家與音樂家
    '達文西', '米開朗基羅', '莫札特', '貝多芬', '巴哈', '蕭邦', '畢卡索', '梵谷',
    '莎士比亞', '但丁', '歌德', '托爾斯泰', '雨果', '狄更斯', '拉斐爾', '莫內',
    '雷諾瓦', '羅丹', '李白', '杜甫', '蘇東坡', '王羲之', '顏真卿',

    // 科學家與發明家
    '牛頓', '愛因斯坦', '居禮夫人', '達爾文', '伽利略', '哥白尼', '愛迪生', '特斯拉',
    '法拉第', '孟德爾', '巴斯德', '弗萊明', '華生', '克里克', '霍金', '諾貝爾',
    '萊特兄弟', '貝爾', '馬可尼', '富蘭克林', '阿基米德', '歐幾里得',

    // 探險家與航海家
    '哥倫布', '鄭和', '麥哲倫', '庫克船長', '馬可波羅', '阿蒙森', '史考特',
    '達伽馬', '哥德薩', '亞美利哥', '卡布拉爾', '巴爾博亞',

    // 政治家與改革家
    '甘地', '林肯', '華盛頓', '邱吉爾', '羅斯福', '曼德拉', '孫中山', '毛澤東',
    '傑佛遜', '富蘭克林', '馬丁路德', '馬丁路德金', '甘迺迪', '里根',
    '戴高樂', '俾斯麥', '加里波底', '玻利瓦爾',

    // 哲學家與思想家
    '孔子', '老子', '莊子', '孟子', '荀子', '蘇格拉底', '柏拉圖', '亞里斯多德', 
    '笛卡爾', '康德', '尼采', '盧梭', '伏爾泰', '洛克', '休謨', '黑格爾',
    '馬克思', '恩格斯', '佛洛伊德', '榮格',

    // 古代名將與英雄
    '項羽', '劉邦', '關羽', '張飛', '趙雲', '諸葛亮', '韓信', '岳飛',
    '文天祥', '鄭成功', '戚繼光', '霍去病', '衛青', '李廣', '馬援',
    '漢尼拔', '斯巴達克斯', '阿基里斯', '奧德修斯', '羅蘭', '亞瑟王',

    // 女性歷史人物
    '居禮夫人', '南丁格爾', '德蕾莎修女', '海倫凱勒', '安妮法蘭克', '貞德',
    '埃及豔后', '武則天', '慈禧太后', '花木蘭', '梁紅玉', '李清照',
    '居里夫人', '瑪莉亞·蒙特梭利', '可可·香奈兒', '奧普拉',

    // 宗教領袖
    '釋迦牟尼', '耶穌', '穆罕默德', '摩西', '達賴喇嘛', '教宗若望保祿二世',
    '聖方濟各', '路德', '加爾文', '玄奘', '鑑真', '慧能',

    // 企業家與創新者
    '福特', '卡內基', '洛克菲勒', '賈伯斯', '比爾蓋茲', '沃爾特·迪士尼',
    '亨利·福特', '湯瑪斯·愛迪生', '亞當·史密斯', '約翰·洛克菲勒',

    // 現代政治人物
    '甘迺迪', '柴契爾夫人', '戈巴契夫', '雷根', '克林頓', '歐巴馬',
    '尼爾森·曼德拉', '德蕾莎修女', '達賴喇嘛', '昂山素姬',

    // 體育明星（歷史性人物）
    '貝比·魯斯', '拳王阿里', '傑西·歐文斯', '魯道夫', '貝利', '馬拉度納'
];

// 獲取隨機歷史人物名字
function getRandomHistoricalName() {
    return historicalFigures[Math.floor(Math.random() * historicalFigures.length)];
}

// DOM 元素 - 安全獲取，避免在不同頁面出錯
const studentTableElement = document.getElementById('studentTable');
const studentTable = studentTableElement ? studentTableElement.getElementsByTagName('tbody')[0] : null;
const addStudentBtn = document.getElementById('addStudentBtn');
const studentModal = document.getElementById('studentModal');
const closeBtn = document.querySelector('.close');
const cancelBtn = document.getElementById('cancelBtn');
const studentForm = document.getElementById('studentForm');
const modalTitle = document.getElementById('modalTitle');
const exportBtn = document.getElementById('exportBtn');
const importBtn = document.getElementById('importBtn');
const importFile = document.getElementById('importFile');
const resetCodeInput = document.getElementById('resetCodeInput');
const resetScoresBtn = document.getElementById('resetScoresBtn');
const randomNameBtn = document.getElementById('randomNameBtn');
const updateHistoricalNamesBtn = document.getElementById('updateHistoricalNamesBtn');

// 當前編輯的學生 ID
let currentStudentId = null;

// 排序狀態
let currentSort = {
    field: 'number', // 預設按編號排序
    direction: 'asc' // 'asc' 或 'desc'
};

// 初始化頁面
document.addEventListener('DOMContentLoaded', () => {
    // 載入本地儲存的學生資料
    const savedStudents = localStorage.getItem('students');
    if (savedStudents) {
        students = JSON.parse(savedStudents);
    } else {
        // 如果沒有已保存的資料，使用預設學生資料
        students = [
            { id: 1, number: '01', class: '603', name: '拿破崙', score: 0, animal: '🦁' },
            { id: 2, number: '02', class: '603', name: '亞歷山大大帝', score: 0, animal: '🐯' },
            { id: 3, number: '03', class: '603', name: '凱撒大帝', score: 0, animal: '🦅' },
            { id: 4, number: '04', class: '603', name: '秦始皇', score: 0, animal: '🐉' },
            { id: 5, number: '05', class: '603', name: '漢武帝', score: 0, animal: '🐺' },
            { id: 6, number: '06', class: '603', name: '唐太宗', score: 0, animal: '🐻' },
            { id: 7, number: '07', class: '603', name: '康熙皇帝', score: 0, animal: '🦄' },
            { id: 8, number: '08', class: '603', name: '武則天', score: 0, animal: '🦋' },
            { id: 9, number: '09', class: '603', name: '埃及豔后', score: 0, animal: '🐱' },
            { id: 10, number: '14', class: '603', name: '伊莉莎白一世', score: 0, animal: '🦉' },
            { id: 11, number: '15', class: '603', name: '達文西', score: 0, animal: '🐨' },
            { id: 12, number: '16', class: '603', name: '米開朗基羅', score: 0, animal: '🐼' },
            { id: 13, number: '18', class: '603', name: '莫札特', score: 0, animal: '🐦' },
            { id: 14, number: '19', class: '603', name: '貝多芬', score: 0, animal: '🦊' },
            { id: 15, number: '20', class: '603', name: '莎士比亞', score: 0, animal: '🐰' },
            { id: 16, number: '21', class: '603', name: '牛頓', score: 0, animal: '🐸' },
            { id: 17, number: '22', class: '603', name: '愛因斯坦', score: 0, animal: '🦔' },
            { id: 18, number: '23', class: '603', name: '居禮夫人', score: 0, animal: '🐝' },
            { id: 19, number: '24', class: '603', name: '哥倫布', score: 0, animal: '🐬' },
            { id: 20, number: '25', class: '603', name: '鄭和', score: 0, animal: '🐳' },
            { id: 21, number: '26', class: '603', name: '甘地', score: 0, animal: '🐧' },
            { id: 22, number: '27', class: '603', name: '林肯', score: 0, animal: '🦘' }
        ];
        // 保存到本地儲存
        saveToLocalStorage();
    }

    renderStudentTable();

    // 只在學生管理頁面執行相關功能
    if (studentTable) {
        // 添加排序事件監聽器
        setupSortListeners();

        // 設置動物選擇功能
        setupAnimalSelection();
    }
});

// 設置動物選擇功能
function setupAnimalSelection() {
    const animalOptions = document.querySelectorAll('.animal-option');
    const selectedAnimalInput = document.getElementById('selectedAnimal');
    
    animalOptions.forEach(option => {
        option.addEventListener('click', () => {
            // 移除其他選項的選擇狀態
            animalOptions.forEach(opt => opt.classList.remove('selected'));
            
            // 添加當前選項的選擇狀態
            option.classList.add('selected');
            
            // 設置隱藏輸入的值
            selectedAnimalInput.value = option.dataset.animal;
        });
    });
    
    // 設置預設選擇（第一個動物）
    if (animalOptions.length > 0 && !selectedAnimalInput.value) {
        animalOptions[0].click();
    }
}

// 設置排序事件監聽器
function setupSortListeners() {
    document.querySelectorAll('.sortable').forEach(th => {
        th.addEventListener('click', () => {
            const field = th.dataset.sort;

            // 如果點擊的是當前排序欄位，則切換方向
            if (currentSort.field === field) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                // 如果點擊的是新欄位，則設為升序
                currentSort.field = field;
                currentSort.direction = 'asc';
            }

            renderStudentTable();
        });
    });
}

// 排序學生資料
function sortStudents(field, direction) {
    students.sort((a, b) => {
        let aValue, bValue;

        switch (field) {
            case 'number':
                aValue = parseInt(a.number) || 0;
                bValue = parseInt(b.number) || 0;
                break;
            case 'class':
                aValue = a.class || '';
                bValue = b.class || '';
                break;
            case 'name':
                aValue = a.name || '';
                bValue = b.name || '';
                break;
            case 'score':
                aValue = a.score || 0;
                bValue = b.score || 0;
                break;
            default:
                return 0;
        }

        if (direction === 'asc') {
            if (typeof aValue === 'string') {
                return aValue.localeCompare(bValue);
            }
            return aValue - bValue;
        } else {
            if (typeof aValue === 'string') {
                return bValue.localeCompare(aValue);
            }
            return bValue - aValue;
        }
    });
}

// 更新排序圖示
function updateSortIcons() {
    // 重置所有排序圖示
    document.querySelectorAll('.sortable').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('.sort-icon');
        if (icon) {
            icon.className = 'fas fa-sort sort-icon';
        }
    });

    // 設置當前排序欄位的圖示
    const currentTh = document.querySelector(`[data-sort="${currentSort.field}"]`);
    if (currentTh) {
        currentTh.classList.add(`sort-${currentSort.direction}`);
        const icon = currentTh.querySelector('.sort-icon');
        if (icon) {
            if (currentSort.direction === 'asc') {
                icon.className = 'fas fa-sort-up sort-icon';
            } else {
                icon.className = 'fas fa-sort-down sort-icon';
            }
        }
    }
}

// 渲染學生表格
function renderStudentTable() {
    // 如果不在學生管理頁面，則不執行
    if (!studentTable) {
        return;
    }

    // 先排序
    sortStudents(currentSort.field, currentSort.direction);

    // 確保所有學生都有動物頭像
    if (window.ANIMAL_CONFIG) {
        window.ANIMAL_CONFIG.initializeStudentAnimals(students);
    }

    studentTable.innerHTML = '';

    students.forEach(student => {
        const row = document.createElement('tr');
        
        // 創建動物頭像顯示
        let animalDisplay = student.animal || '🐱';
        if (window.ANIMAL_CONFIG && typeof student.animal === 'string' && window.ANIMAL_CONFIG.images[student.animal]) {
            const animalInfo = window.ANIMAL_CONFIG.getAnimalInfo(student.animal);
            const animalImage = window.ANIMAL_CONFIG.getAnimalImage(student.animal);
            animalDisplay = `
                <div class="student-animal-avatar" title="${animalInfo.name} - ${animalInfo.personality}">
                    <img src="${animalImage}" alt="${animalInfo.name}" class="animal-avatar-img">
                    <span class="animal-emoji">${animalInfo.emoji}</span>
                </div>
            `;
        } else if (typeof student.animal === 'string') {
            // 如果是emoji，直接顯示
            animalDisplay = `<div class="student-animal-emoji">${student.animal}</div>`;
        }
        
        row.innerHTML = `
            <td class="animal-cell">${animalDisplay}</td>
            <td>${student.number}</td>
            <td>${student.class}</td>
            <td class="student-name-cell">
                <span class="student-name">${student.name}</span>
            </td>
            <td class="score ${student.score >= 50 ? 'high-score' : student.score >= 20 ? 'medium-score' : 'low-score'}">${student.score || 0} 分</td>
            <td class="actions">
                <button class="btn btn-edit" data-id="${student.id}"><i class="fas fa-edit"></i> 編輯</button>
                <button class="btn btn-danger" data-id="${student.id}"><i class="fas fa-trash"></i> 刪除</button>
            </td>
        `;
        studentTable.appendChild(row);
    });

    // 更新排序圖示
    updateSortIcons();

    // 添加編輯和刪除按鈕的事件監聽器
    document.querySelectorAll('.btn-edit').forEach(btn => {
        btn.addEventListener('click', handleEditStudent);
    });

    document.querySelectorAll('.btn-danger[data-id]').forEach(btn => {
        btn.addEventListener('click', handleDeleteStudent);
    });

    // 儲存到本地存儲
    saveToLocalStorage();
}

// 處理新增學生按鈕點擊
if (addStudentBtn) {
    addStudentBtn.addEventListener('click', () => {
    currentStudentId = null;
    modalTitle.textContent = '🐾 新增小動物學生';
    studentForm.reset();
    document.getElementById('studentNumber').value = '';
    document.getElementById('studentClass').value = '603';
    document.getElementById('selectedAnimal').value = '';
    
    // 重置動物選擇狀態
    document.querySelectorAll('.animal-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // 設置預設動物選擇
    const firstAnimal = document.querySelector('.animal-option');
    if (firstAnimal) {
        firstAnimal.click();
    }
    
    studentModal.style.display = 'flex';
    });
}

// 處理隨機名字按鈕點擊
if (randomNameBtn) {
    randomNameBtn.addEventListener('click', () => {
    const randomName = getRandomHistoricalName();
    document.getElementById('studentName').value = randomName;

    // 添加一個小動畫效果
    const nameInput = document.getElementById('studentName');
    nameInput.style.background = 'linear-gradient(135deg, #6c5ce7, #a29bfe)';
    nameInput.style.color = 'white';
    nameInput.style.transition = 'all 0.3s ease';

    setTimeout(() => {
        nameInput.style.background = '';
        nameInput.style.color = '';
    }, 1000);
    });
}

// 處理重置分數按鈕點擊
if (resetScoresBtn) {
    resetScoresBtn.addEventListener('click', () => {
    const resetCode = resetCodeInput.value.trim();

    if (resetCode === '0718') {
        if (confirm('確定要重置所有學生的分數為 0 分嗎？此操作無法復原！')) {
            // 重置所有學生分數為 0
            students.forEach(student => {
                student.score = 0;
            });

            // 保存到本地儲存
            saveToLocalStorage();

            // 重新渲染表格
            renderStudentTable();

            // 清空輸入欄位
            resetCodeInput.value = '';

            // 顯示成功訊息
            alert('所有學生分數已重置為 0 分！');
        }
    } else if (resetCode !== '') {
        alert('重置代碼錯誤！請輸入正確的代碼。');
        resetCodeInput.value = '';
    } else {
        alert('請輸入重置代碼！');
    }
    });
}

// 處理批量更新歷史人物名字按鈕點擊
if (updateHistoricalNamesBtn) {
    updateHistoricalNamesBtn.addEventListener('click', () => {
    if (students.length === 0) {
        alert('目前沒有學生資料，請先新增學生！');
        return;
    }
    
    if (confirm(`確定要將所有 ${students.length} 位學生的名字更新為隨機歷史人物嗎？\n\n這個操作會：\n- 保留學生的其他資料（編號、班級、分數、動物頭像）\n- 只更換學生姓名為歷史人物\n- 確保每位學生都有不同的名字\n\n此操作無法復原！`)) {
        
        // 批量更新學生名字為歷史人物
        updateAllStudentsToHistoricalNames();
        
        // 保存到本地儲存
        saveToLocalStorage();
        
        // 重新渲染表格
        renderStudentTable();
        
        // 顯示成功訊息
        alert(`✅ 成功更新！所有 ${students.length} 位學生的名字已更新為歷史人物！\n\n快去看看你的班級變成了歷史名人堂吧！ 🎭✨`);
    }
    });
}

// 批量更新所有學生名字為歷史人物的函數
function updateAllStudentsToHistoricalNames() {
    const usedNames = new Set();
    const shuffledFigures = [...historicalFigures].sort(() => Math.random() - 0.5);
    
    students.forEach((student, index) => {
        let newName;
        let attempts = 0;
        const maxAttempts = historicalFigures.length * 2;
        
        do {
            if (index < shuffledFigures.length && !usedNames.has(shuffledFigures[index])) {
                newName = shuffledFigures[index];
            } else {
                newName = getRandomHistoricalName();
            }
            attempts++;
            
            // 如果所有名字都用完了，允許重複（理論上不會發生，因為歷史人物名字庫很大）
            if (attempts >= maxAttempts) {
                break;
            }
        } while (usedNames.has(newName) && usedNames.size < historicalFigures.length);
        
        usedNames.add(newName);
        student.name = newName;
        
        console.log(`學生 ${student.number} 的名字已更新為：${newName}`);
    });
    
    console.log(`批量更新完成！共更新了 ${students.length} 位學生的名字`);
}

// 處理編輯學生
function handleEditStudent(e) {
    const id = parseInt(e.target.closest('button').dataset.id);
    const student = students.find(s => s.id === id);
    
    if (student) {
        currentStudentId = id;
        modalTitle.textContent = '🐾 編輯小動物學生';
        document.getElementById('studentId').value = student.id;
        document.getElementById('studentNumber').value = student.number;
        document.getElementById('studentClass').value = student.class;
        document.getElementById('studentName').value = student.name;
        
        // 設置動物頭像選擇
        const selectedAnimal = document.getElementById('selectedAnimal');
        selectedAnimal.value = student.animal || '🐱';
        
        // 更新動物選擇的視覺狀態
        document.querySelectorAll('.animal-option').forEach(option => {
            option.classList.remove('selected');
            if (option.dataset.animal === student.animal) {
                option.classList.add('selected');
            }
        });
        
        studentModal.style.display = 'flex';
    }
}

// 處理刪除學生
function handleDeleteStudent(e) {
    if (confirm('確定要刪除這位學生嗎？此操作無法復原。')) {
        const id = parseInt(e.target.closest('button').dataset.id);
        students = students.filter(student => student.id !== id);
        renderStudentTable();
    }
}

// 處理表單提交
if (studentForm) {
    studentForm.addEventListener('submit', (e) => {
    e.preventDefault();
    
    const number = document.getElementById('studentNumber').value.padStart(2, '0');
    const studentClass = document.getElementById('studentClass').value;
    const name = document.getElementById('studentName').value.trim();
    const animal = document.getElementById('selectedAnimal').value;
    
    if (!number || !studentClass || !name || !animal) {
        alert('請填寫所有必填欄位！');
        return;
    }
    
    if (currentStudentId === null) {
        // 新增學生
        const newId = students.length > 0 ? Math.max(...students.map(s => s.id)) + 1 : 1;
        students.push({
            id: newId,
            number,
            class: studentClass,
            name,
            score: 0,
            animal
        });
    } else {
        // 更新學生
        const index = students.findIndex(s => s.id === currentStudentId);
        if (index !== -1) {
            students[index] = {
                ...students[index],
                number,
                class: studentClass,
                name,
                animal
            };
        }
    }
    
    renderStudentTable();
    studentModal.style.display = 'none';
    });
}

// 關閉模態框
function closeModal() {
    studentModal.style.display = 'none';
}

if (closeBtn) {
    closeBtn.addEventListener('click', closeModal);
}
if (cancelBtn) {
    cancelBtn.addEventListener('click', closeModal);
}

// 點擊模態框外部關閉
if (studentModal) {
    window.addEventListener('click', (e) => {
        if (e.target === studentModal) {
            closeModal();
        }
    });
}

// 匯出資料 - 直接匯出 CSV 格式
if (exportBtn) {
    exportBtn.addEventListener('click', () => {
        exportData('csv');
    });
}

// CSV 解析函數
function parseCSV(csvText) {
    const lines = csvText.trim().split('\n');
    if (lines.length < 2) {
        throw new Error('CSV 檔案格式不正確');
    }

    // 解析標題行
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

    // 檢查必要的欄位
    const requiredFields = ['animal', 'number', 'class', 'name'];
    const missingFields = requiredFields.filter(field => !headers.includes(field));
    if (missingFields.length > 0) {
        throw new Error(`CSV 檔案缺少必要欄位: ${missingFields.join(', ')}`);
    }

    const students = [];

    // 解析資料行
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue; // 跳過空行

        const values = line.split(',').map(v => v.trim().replace(/"/g, ''));

        if (values.length !== headers.length) {
            console.warn(`第 ${i + 1} 行資料欄位數量不正確，跳過此行`);
            continue;
        }

        const student = {};
        headers.forEach((header, index) => {
            student[header] = values[index];
        });

        // 轉換數字類型
        if (student.number) {
            student.number = parseInt(student.number);
        }
        if (student.score) {
            student.score = parseInt(student.score) || 0;
        } else {
            student.score = 0;
        }

        // 確保必要欄位存在
        if (student.animal && student.number && student.class && student.name) {
            students.push(student);
        } else {
            console.warn(`第 ${i + 1} 行資料不完整，跳過此行`);
        }
    }

    return students;
}

// 匯入資料
if (importBtn) {
    importBtn.addEventListener('click', () => {
        importFile.click();
    });
}

if (importFile) {
    importFile.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
        try {
            let importedStudents;
            const fileContent = e.target.result;

            // 判斷檔案類型
            if (file.name.toLowerCase().endsWith('.csv')) {
                // 處理 CSV 檔案
                importedStudents = parseCSV(fileContent);
            } else {
                // 處理 JSON 檔案
                importedStudents = JSON.parse(fileContent);
            }

            if (Array.isArray(importedStudents) && importedStudents.length > 0) {
                if (confirm(`確定要匯入 ${importedStudents.length} 筆學生資料嗎？這將覆蓋現有資料。`)) {
                    students = importedStudents;
                    renderStudentTable();
                    alert('資料匯入成功！');
                }
            } else {
                alert('匯入的檔案格式不正確或沒有資料！');
            }
        } catch (error) {
            console.error('匯入錯誤:', error);
            alert('匯入檔案時發生錯誤！請確認檔案格式正確。');
        }
        // 重置檔案輸入，允許重複選擇同一個檔案
        importFile.value = '';
    };
    reader.readAsText(file);
    });
}

// 儲存到本地存儲
function saveToLocalStorage() {
    localStorage.setItem('students', JSON.stringify(students));
}

// 批量更新學生名字為歷史人物（隱藏功能）
function updateAllStudentsToHistoricalNames() {
    if (students.length === 0) {
        alert('沒有學生資料可以更新！');
        return;
    }

    if (confirm('確定要將所有學生的名字更新為隨機歷史人物嗎？此操作無法復原！')) {
        const usedNames = new Set();

        students.forEach(student => {
            let randomName;
            // 確保不重複使用相同的名字
            do {
                randomName = getRandomHistoricalName();
            } while (usedNames.has(randomName) && usedNames.size < historicalFigures.length);

            usedNames.add(randomName);
            student.name = randomName;
        });

        saveToLocalStorage();
        renderStudentTable();
        alert(`已成功更新 ${students.length} 位學生的名字為歷史人物！`);
    }
}

// 隱藏的快捷鍵功能：按 Ctrl+Shift+H 觸發批量更新
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'H') {
        e.preventDefault();
        updateAllStudentsToHistoricalNames();
    }
});

// ==================== 匯出功能實現 ====================

// 顯示匯出格式選擇彈窗
function showExportModal() {
    const exportModal = document.getElementById('exportModal');
    if (exportModal) {
        exportModal.style.display = 'flex';

        // 設置格式選擇事件
        setupExportFormatSelection();
    }
}

// 設置匯出格式選擇事件
function setupExportFormatSelection() {
    const exportOptions = document.querySelectorAll('.export-option');
    const exportModalClose = document.getElementById('exportModalClose');
    const exportModal = document.getElementById('exportModal');

    // 關閉彈窗事件
    if (exportModalClose) {
        exportModalClose.addEventListener('click', () => {
            exportModal.style.display = 'none';
        });
    }

    // 點擊彈窗外部關閉
    exportModal.addEventListener('click', (e) => {
        if (e.target === exportModal) {
            exportModal.style.display = 'none';
        }
    });

    // 格式選擇事件
    exportOptions.forEach(option => {
        const button = option.querySelector('.btn');
        if (button) {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const format = option.dataset.format;
                exportData(format);
                exportModal.style.display = 'none';
            });
        }
    });
}

// 匯出資料主函數
function exportData(format) {
    const today = new Date().toISOString().split('T')[0];
    const timestamp = new Date().toLocaleString('zh-TW');

    try {
        switch (format) {
            case 'json':
                exportAsJSON(today);
                break;
            case 'csv':
                exportAsCSV(today);
                break;
            case 'excel':
                exportAsExcel(today, timestamp);
                break;
            default:
                alert('不支援的匯出格式！');
        }
    } catch (error) {
        console.error('匯出錯誤:', error);
        alert('匯出資料時發生錯誤，請稍後再試！');
    }
}

// JSON 格式匯出
function exportAsJSON(today) {
    const exportData = {
        exportDate: new Date().toISOString(),
        exportFormat: 'JSON',
        version: '1.0',
        totalStudents: students.length,
        students: students.map(student => ({
            id: student.id,
            number: student.number,
            class: student.class,
            name: student.name,
            score: student.score || 0,
            animal: student.animal || '🐱'
        }))
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

    const fileName = `學生名單_${today}.json`;
    downloadFile(dataUri, fileName);

    showExportSuccess('JSON', fileName, students.length);
}

// CSV 格式匯出
function exportAsCSV(today) {
    // CSV 標題行
    const headers = ['編號', '班級', '姓名', '分數', '動物頭像'];

    // 準備 CSV 資料
    const csvData = [headers];

    students.forEach(student => {
        csvData.push([
            student.number || '',
            student.class || '',
            student.name || '',
            student.score || 0,
            student.animal || '🐱'
        ]);
    });

    // 轉換為 CSV 格式
    const csvContent = csvData.map(row =>
        row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
    ).join('\n');

    // 添加 BOM 以支援中文
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const fileName = `學生名單_${today}.csv`;
    downloadFile(url, fileName);

    // 清理 URL
    setTimeout(() => URL.revokeObjectURL(url), 1000);

    showExportSuccess('CSV', fileName, students.length);
}

// Excel 格式匯出
function exportAsExcel(today, timestamp) {
    // 檢查是否載入了 XLSX 庫
    if (typeof XLSX === 'undefined') {
        alert('Excel 匯出功能需要載入相關庫，請重新整理頁面後再試！');
        return;
    }

    // 準備工作表資料
    const worksheetData = [
        ['🍭 計分小達人 - 學生名單'],
        ['匯出時間：' + timestamp],
        ['總學生數：' + students.length],
        [], // 空行
        ['編號', '班級', '姓名', '分數', '動物頭像', '分數等級']
    ];

    // 添加學生資料
    students.forEach(student => {
        const score = student.score || 0;
        let scoreLevel = '需要加油';
        if (score >= 50) scoreLevel = '表現優秀';
        else if (score >= 20) scoreLevel = '表現良好';

        worksheetData.push([
            student.number || '',
            student.class || '',
            student.name || '',
            score,
            student.animal || '🐱',
            scoreLevel
        ]);
    });

    // 創建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // 設置列寬
    worksheet['!cols'] = [
        { width: 8 },   // 編號
        { width: 8 },   // 班級
        { width: 15 },  // 姓名
        { width: 10 },  // 分數
        { width: 12 },  // 動物頭像
        { width: 12 }   // 分數等級
    ];

    // 設置標題行樣式（合併儲存格）
    worksheet['!merges'] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: 5 } }, // 標題行
        { s: { r: 1, c: 0 }, e: { r: 1, c: 5 } }, // 匯出時間
        { s: { r: 2, c: 0 }, e: { r: 2, c: 5 } }  // 總學生數
    ];

    // 創建工作簿
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '學生名單');

    // 匯出檔案
    const fileName = `學生名單_${today}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    showExportSuccess('Excel', fileName, students.length);
}

// 下載檔案輔助函數
function downloadFile(url, fileName) {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 顯示匯出成功訊息
function showExportSuccess(format, fileName, studentCount) {
    const message = `✅ ${format} 格式匯出成功！\n\n檔案名稱：${fileName}\n學生數量：${studentCount} 位\n\n檔案已下載到您的下載資料夾中。`;
    alert(message);

    // 在控制台記錄匯出資訊
    console.log(`匯出成功 - 格式：${format}，檔案：${fileName}，學生數：${studentCount}`);
}
